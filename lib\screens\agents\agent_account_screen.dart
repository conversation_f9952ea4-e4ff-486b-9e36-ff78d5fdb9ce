import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_account_model.dart';
import '../../models/invoice_model.dart';
import '../../models/payment_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';



class AgentAccountScreen extends StatefulWidget {
  const AgentAccountScreen({super.key});

  @override
  State<AgentAccountScreen> createState() => _AgentAccountScreenState();
}

class _AgentAccountScreenState extends State<AgentAccountScreen> with SingleTickerProviderStateMixin {
  final DataService _dataService = DataService.instance;
  
  late TabController _tabController;
  List<InvoiceModel> _agentInvoices = [];
  List<PaymentModel> _agentPayments = [];
  AgentAccountModel? _agentAccount;
  double _totalSales = 0;
  double _totalProfits = 0;
  double _totalDebt = 0; // Total debt owed by agent
  double _totalPayments = 0;
  double _currentBalance = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser != null) {
        // Load agent invoices
        _agentInvoices = await _dataService.getAgentInvoices(currentUser.id);

        // Load agent payments
        final paymentsData = await _dataService.getAgentPayments(currentUser.id);
        _agentPayments = paymentsData.map((data) {
          // Convert the raw payment data to PaymentModel format
          final paymentMap = Map<String, dynamic>.from(data);

          // Ensure all required fields are present with proper types
          paymentMap['receiptNumber'] = paymentMap['id'] ?? AppUtils.generateId();
          paymentMap['paymentMethod'] = paymentMap['paymentMethod'] ?? 'cash';
          paymentMap['paymentDate'] = paymentMap['createdAt'] ?? DateTime.now().toIso8601String();
          paymentMap['status'] = paymentMap['status'] ?? 'confirmed';
          paymentMap['updatedAt'] = paymentMap['updatedAt'] ?? paymentMap['createdAt'] ?? DateTime.now().toIso8601String();

          return PaymentModel.fromMap(paymentMap);
        }).toList();

        // Load agent account for transactions
        _agentAccount = await _dataService.getAgentAccount(currentUser.id);

        if (kDebugMode) {
          if (_agentAccount != null) {
            print('✅ Agent account loaded: ${_agentAccount!.agentName}');
            print('📊 Account balance: ${_agentAccount!.currentBalance}');
            print('📊 Total transactions: ${_agentAccount!.transactions.length}');
          } else {
            print('❌ Failed to load agent account for: ${currentUser.id}');
          }
        }

        // Load agent account summary for transactions
        final accountSummary = await _dataService.getAgentAccountSummary(currentUser.id);

        if (kDebugMode) {
          print('Loaded ${_agentInvoices.length} invoices and ${_agentPayments.length} payments for agent ${currentUser.fullName}');
          print('Agent account balance: ${accountSummary['currentBalance']}');
        }
      } else {
        _agentInvoices = [];
        _agentPayments = [];
      }

      _calculateTotals();
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _rebuildAccountFromInvoices() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null) return;

      if (kDebugMode) {
        print('🔧 Rebuilding account for agent: ${currentUser.fullName}');
      }

      // Get all invoices for this agent
      final allInvoices = await _dataService.getInvoices();
      final agentInvoices = allInvoices.where((invoice) =>
        invoice.createdBy == currentUser.id ||
        (invoice.additionalData != null &&
         invoice.additionalData!['agentId'] == currentUser.id)
      ).toList();

      if (kDebugMode) {
        print('🔧 Found ${agentInvoices.length} invoices for agent');
      }

      // Create new account with transactions from invoices
      final List<AgentTransaction> transactions = [];
      double totalDebt = 0.0;
      double totalPaid = 0.0;

      for (final invoice in agentInvoices) {
        if (invoice.type == 'goods') {
          // Add debt for goods transferred
          final debtTransaction = AgentTransaction(
            id: AppUtils.generateId(),
            type: 'debt',
            amount: invoice.sellingPrice, // Use sellingPrice as total amount
            description: 'تكلفة البضاعة المحولة - ${invoice.id}',
            invoiceId: invoice.id,
            timestamp: invoice.createdAt,
            createdBy: currentUser.id,
          );
          transactions.add(debtTransaction);
          totalDebt += invoice.sellingPrice;

          if (kDebugMode) {
            print('🔧 Added goods debt: ${invoice.sellingPrice}');
          }
        } else if (invoice.type == 'customer') {
          // Add company profit share for customer sales
          final profit = invoice.sellingPrice - invoice.purchasePrice;
          final companyShare = profit * 0.5;

          if (companyShare > 0) {
            final profitTransaction = AgentTransaction(
              id: AppUtils.generateId(),
              type: 'debt',
              amount: companyShare,
              description: 'نصيب المؤسسة من الربح (50%) - ${invoice.id}',
              invoiceId: invoice.id,
              timestamp: invoice.createdAt,
              createdBy: currentUser.id,
            );
            transactions.add(profitTransaction);
            totalDebt += companyShare;

            if (kDebugMode) {
              print('🔧 Added profit share: $companyShare');
            }
          }
        }
      }

      // Create updated account
      final updatedAccount = AgentAccountModel(
        id: currentUser.id,
        agentId: currentUser.id,
        agentName: currentUser.fullName,
        agentPhone: currentUser.phone,
        totalDebt: totalDebt,
        totalPaid: totalPaid,
        currentBalance: totalDebt - totalPaid,
        transactions: transactions,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: currentUser.id,
      );

      // Save the rebuilt account
      await _dataService.createOrUpdateAgentAccount(updatedAccount);

      if (kDebugMode) {
        print('🔧 Account rebuilt with ${transactions.length} transactions');
        print('🔧 Total debt: $totalDebt, Balance: ${totalDebt - totalPaid}');
      }

      // Reload data
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة بناء الحساب بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error rebuilding account: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة بناء الحساب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _calculateTotals() {
    // Calculate total sales from customer invoices only (not goods invoices)
    final customerInvoices = _agentInvoices.where((invoice) =>
      invoice.type == 'customer' || invoice.type == AppConstants.customerInvoice
    ).toList();

    _totalSales = customerInvoices.fold(0, (sum, invoice) => sum + invoice.sellingPrice);

    // Calculate total debt and payments from agent account transactions
    double totalDebt = 0.0;
    double totalPaid = 0.0;

    if (_agentAccount != null) {
      if (kDebugMode) {
        print('📊 Calculating totals for agent: ${_agentAccount!.agentName}');
        print('📊 Total transactions: ${_agentAccount!.transactions.length}');
      }

      for (final transaction in _agentAccount!.transactions) {
        if (kDebugMode) {
          print('📊 Transaction: ${transaction.type} - ${transaction.amount}');
        }

        // Use correct transaction types from the detailed statement
        if (transaction.type == 'transfer' || transaction.type == 'sale_profit') {
          totalDebt += transaction.amount;
        } else if (transaction.type == 'payment') {
          totalPaid += transaction.amount;
        }
      }

      if (kDebugMode) {
        print('📊 Total debt: $totalDebt, Total paid: $totalPaid');
      }
    } else {
      if (kDebugMode) {
        print('❌ Agent account is null!');
      }
    }

    _totalProfits = totalDebt; // Total debt owed by agent
    _totalPayments = totalPaid; // Total payments made by agent
    _currentBalance = _totalProfits - _totalPayments; // Positive = agent owes money

    if (kDebugMode) {
      print('📊 Final calculations:');
      print('   Sales: $_totalSales');
      print('   Debt: $_totalProfits');
      print('   Payments: $_totalPayments');
      print('   Balance: $_currentBalance');
    }
  }

  Future<void> _recordPayment() async {
    // Show message that this feature is for managers only
    AppUtils.showSnackBar(
      context,
      'تسجيل الدفعات متاح للمديرين فقط. يرجى التواصل مع الإدارة لتسجيل دفعتك.',
      isError: true
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.currentUser;
        final canRecordPayment = currentUser != null &&
            (currentUser.isSuperAdmin || currentUser.isAdmin);

        return Scaffold(
          appBar: AppBar(
            title: const Text('حسابي'),
            actions: [
              IconButton(
                icon: const Icon(Icons.build),
                onPressed: _isLoading ? null : _rebuildAccountFromInvoices,
                tooltip: 'إعادة بناء الحساب',
              ),
              IconButton(
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.refresh),
                onPressed: _isLoading ? null : _loadData,
                tooltip: 'تحديث البيانات',
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'نظرة عامة'),
                Tab(text: 'المبيعات'),
                Tab(text: 'المدفوعات'),
              ],
            ),
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildSalesTab(),
                    _buildPaymentsTab(),
                  ],
                ),
          // Only show payment button for managers, not for agents
          floatingActionButton: canRecordPayment ? FloatingActionButton.extended(
            onPressed: _recordPayment,
            icon: const Icon(Icons.payment),
            label: const Text('تسجيل دفعة'),
          ) : null,
        );
      },
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Balance card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.largePadding),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        size: 48,
                        color: _currentBalance >= 0 ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: AppConstants.defaultPadding),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الرصيد الحالي',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            Text(
                              AppUtils.formatCurrency(_currentBalance.abs()),
                              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: _currentBalance > 0 ? Colors.red : Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _currentBalance > 0 ? 'مديون للمؤسسة' :
                              _currentBalance < 0 ? 'دائن لدى المؤسسة' : 'لا يوجد رصيد',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: _currentBalance > 0 ? Colors.red : Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  if (_currentBalance > 0)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(25),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                        border: Border.all(color: Colors.red.withAlpha(76)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.red),
                          const SizedBox(width: AppConstants.smallPadding),
                          Expanded(
                            child: Text(
                              'لديك مديونية مستحقة. يرجى تسوية الحساب.',
                              style: TextStyle(
                                color: Colors.red.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Statistics cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المبيعات',
                  AppUtils.formatCurrency(_totalSales),
                  Icons.point_of_sale,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  'إجمالي المديونية',
                  AppUtils.formatCurrency(_totalProfits),
                  Icons.account_balance_wallet,
                  Colors.orange,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المدفوعات',
                  AppUtils.formatCurrency(_totalPayments),
                  Icons.payment,
                  Colors.green,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  _currentBalance >= 0 ? 'رصيد مستحق' : 'رصيد دائن',
                  AppUtils.formatCurrency(_currentBalance.abs()),
                  _currentBalance >= 0 ? Icons.account_balance : Icons.account_balance_wallet,
                  _currentBalance >= 0 ? Colors.red : Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'عدد المبيعات',
                  _agentInvoices.where((invoice) =>
                    invoice.type == 'customer' || invoice.type == AppConstants.customerInvoice
                  ).length.toString(),
                  Icons.receipt,
                  Colors.purple,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  'أرباح الوكيل',
                  AppUtils.formatCurrency(_agentInvoices.fold(0.0, (sum, invoice) =>
                    sum + (invoice.type == 'customer' ? invoice.agentProfitShare : 0))),
                  Icons.trending_up,
                  Colors.teal,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesTab() {
    if (_agentInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(127),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد مبيعات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _agentInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _agentInvoices[index];
        return _buildInvoiceCard(invoice);
      },
    );
  }

  Widget _buildInvoiceCard(InvoiceModel invoice) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فاتورة رقم: ${invoice.invoiceNumber}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildStatusChip(invoice.status),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Text('العميل: ${invoice.customerName ?? 'غير محدد'}'),
            Text('تاريخ البيع: ${AppUtils.formatDate(invoice.createdAt)}'),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('سعر البيع: ${AppUtils.formatCurrency(invoice.sellingPrice)}'),
                Text(
                  'ربحك: ${AppUtils.formatCurrency(invoice.agentProfitShare)}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsTab() {
    if (_agentPayments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(127),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد مدفوعات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _agentPayments.length,
      itemBuilder: (context, index) {
        final payment = _agentPayments[index];
        return _buildPaymentCard(payment);
      },
    );
  }

  Widget _buildPaymentCard(PaymentModel payment) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'سند رقم: ${payment.receiptNumber}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildStatusChip(payment.status),
              ],
            ),
            
            const SizedBox(height: AppConstants.smallPadding),
            
            Text('المبلغ: ${AppUtils.formatCurrency(payment.amount)}'),
            Text('طريقة الدفع: ${payment.paymentMethod}'),
            Text('تاريخ الدفع: ${AppUtils.formatDate(payment.paymentDate)}'),
            
            if (payment.notes != null && payment.notes!.isNotEmpty) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                'ملاحظات: ${payment.notes}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color chipColor;
    String statusText;
    
    switch (status) {
      case 'pending':
        chipColor = Colors.orange;
        statusText = 'معلق';
        break;
      case 'confirmed':
      case 'paid':
        chipColor = Colors.green;
        statusText = 'مؤكد';
        break;
      case 'cancelled':
        chipColor = Colors.red;
        statusText = 'ملغي';
        break;
      default:
        chipColor = Colors.grey;
        statusText = status;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withAlpha(76)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

class _PaymentDialog extends StatefulWidget {
  @override
  State<_PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<_PaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  
  String _selectedMethod = 'نقدي';
  
  final List<String> _paymentMethods = [
    'نقدي',
    'تحويل بنكي',
    'شيك',
    'فيزا',
  ];

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تسجيل دفعة جديدة'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'المبلغ (ج.م)',
                hintText: '1000',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'المبلغ مطلوب';
                }
                final amount = double.tryParse(value.trim());
                if (amount == null || amount <= 0) {
                  return 'مبلغ غير صحيح';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            DropdownButtonFormField<String>(
              value: _selectedMethod,
              decoration: const InputDecoration(
                labelText: 'طريقة الدفع',
              ),
              items: _paymentMethods.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(method),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedMethod = value!;
                });
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                hintText: 'أي ملاحظات إضافية',
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.of(context).pop({
                'amount': double.parse(_amountController.text.trim()),
                'method': _selectedMethod,
                'notes': _notesController.text.trim(),
              });
            }
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
