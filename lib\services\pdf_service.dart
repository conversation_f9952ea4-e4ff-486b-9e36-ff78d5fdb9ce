
import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class PdfService {
  static PdfService? _instance;
  static PdfService get instance => _instance ??= PdfService._();
  
  PdfService._();

  pw.Font? _arabicFont;
  pw.Font? _arabicBoldFont;

  // Initialize Arabic fonts
  Future<void> _initializeFonts() async {
    if (_arabicFont == null) {
      try {
        if (kDebugMode) {
          print('🔄 Initializing PDF fonts...');
        }
        // Load Arabic font from Google Fonts
        _arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
        _arabicBoldFont = await PdfGoogleFonts.notoSansArabicBold();
        if (kDebugMode) {
          print('✅ Successfully loaded Noto Sans Arabic fonts');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Google Fonts failed, using fallback: $e');
        }
        // Use basic fonts as fallback
        _arabicFont = pw.Font.helvetica();
        _arabicBoldFont = pw.Font.helveticaBold();
        if (kDebugMode) {
          print('✅ Using Helvetica as fallback font');
        }
      }
    }
  }

  // Generate inventory report PDF
  Future<Uint8List> generateInventoryReportPdf({
    required List<Map<String, dynamic>> items,
    required String title,
    Map<String, dynamic>? summary,
  }) async {
    await _initializeFonts();
    
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4.landscape,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: _arabicFont!,
          bold: _arabicBoldFont!,
        ),
        build: (pw.Context context) {
          return [
            // Header
            pw.Container(
              alignment: pw.Alignment.center,
              child: pw.Column(
                children: [
                  pw.Text(
                    title,
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      font: _arabicBoldFont,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'الفرحان للنقل الخفيف',
                    style: pw.TextStyle(
                      fontSize: 18,
                      font: _arabicFont,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    'تاريخ التقرير: ${DateTime.now().toString().split(' ')[0]}',
                    style: pw.TextStyle(
                      fontSize: 12,
                      font: _arabicFont,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Summary section
            if (summary != null) ...[
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey),
                  borderRadius: pw.BorderRadius.circular(5),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                  children: [
                    _buildSummaryItem('إجمالي الأصناف', summary['totalItems']?.toString() ?? '0'),
                    _buildSummaryItem('القيمة الإجمالية', '${summary['totalValue']?.toStringAsFixed(0) ?? '0'} ج.م'),
                    _buildSummaryItem('المتاح', summary['availableItems']?.toString() ?? '0'),
                  ],
                ),
              ),
              pw.SizedBox(height: 20),
            ],
            
            // Table
            pw.TableHelper.fromTextArray(
              context: context,
              data: [
                // Headers
                ['النوع', 'الموديل', 'اللون', 'الماركة', 'سنة الصنع', 'سعر الشراء', 'سعر البيع', 'الحالة', 'المخزن'],
                // Data rows
                ...items.map((item) => [
                  item['type'] ?? '',
                  item['model'] ?? '',
                  item['color'] ?? '',
                  item['brand'] ?? '',
                  item['yearOfManufacture']?.toString() ?? '',
                  '${item['purchasePrice']?.toStringAsFixed(0) ?? '0'} ج.م',
                  '${item['suggestedSellingPrice']?.toStringAsFixed(0) ?? '0'} ج.م',
                  _getStatusLabel(item['status'] ?? ''),
                  _getWarehouseName(item['currentWarehouseId'] ?? ''),
                ]),
              ],
              headerStyle: pw.TextStyle(
                fontWeight: pw.FontWeight.bold,
                font: _arabicBoldFont,
                fontSize: 10,
              ),
              cellStyle: pw.TextStyle(
                font: _arabicFont,
                fontSize: 9,
              ),
              headerDecoration: const pw.BoxDecoration(
                color: PdfColors.grey300,
              ),
              cellAlignment: pw.Alignment.center,
              cellPadding: const pw.EdgeInsets.all(5),
              border: pw.TableBorder.all(color: PdfColors.grey),
              columnWidths: {
                0: const pw.FlexColumnWidth(1.2),
                1: const pw.FlexColumnWidth(1.2),
                2: const pw.FlexColumnWidth(1),
                3: const pw.FlexColumnWidth(1),
                4: const pw.FlexColumnWidth(0.8),
                5: const pw.FlexColumnWidth(1),
                6: const pw.FlexColumnWidth(1),
                7: const pw.FlexColumnWidth(0.8),
                8: const pw.FlexColumnWidth(1),
              },
            ),
            
            pw.SizedBox(height: 20),
            
            // Footer
            pw.Container(
              alignment: pw.Alignment.center,
              child: pw.Text(
                'تم إنشاء هذا التقرير بواسطة تطبيق الفرحان للنقل الخفيف',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _arabicFont,
                  color: PdfColors.grey600,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ),
          ];
        },
      ),
    );

    return pdf.save();
  }

  // Generate sales report PDF
  Future<Uint8List> generateSalesReportPdf({
    required List<Map<String, dynamic>> sales,
    required String title,
    required DateTime? startDate,
    required DateTime? endDate,
    Map<String, dynamic>? summary,
  }) async {
    await _initializeFonts();
    
    final pdf = pw.Document();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4.landscape,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: _arabicFont!,
          bold: _arabicBoldFont!,
        ),
        build: (pw.Context context) {
          return [
            // Header
            pw.Container(
              alignment: pw.Alignment.center,
              child: pw.Column(
                children: [
                  pw.Text(
                    title,
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      font: _arabicBoldFont,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'الفرحان للنقل الخفيف',
                    style: pw.TextStyle(
                      fontSize: 18,
                      font: _arabicFont,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 5),
                  if (startDate != null && endDate != null)
                    pw.Text(
                      'فترة التقرير: من ${startDate.toString().split(' ')[0]} إلى ${endDate.toString().split(' ')[0]}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                ],
              ),
            ),
            
            pw.SizedBox(height: 20),
            
            // Summary section
            if (summary != null) ...[
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey),
                  borderRadius: pw.BorderRadius.circular(5),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                  children: [
                    _buildSummaryItem('عدد المبيعات', summary['salesCount']?.toString() ?? '0'),
                    _buildSummaryItem('إجمالي المبيعات', '${summary['totalSales']?.toStringAsFixed(0) ?? '0'} ج.م'),
                    _buildSummaryItem('إجمالي الربح', '${summary['totalProfit']?.toStringAsFixed(0) ?? '0'} ج.م'),
                  ],
                ),
              ),
              pw.SizedBox(height: 20),
            ],
            
            // Table
            pw.TableHelper.fromTextArray(
              context: context,
              data: [
                // Headers
                ['التاريخ', 'النوع', 'الموديل', 'اللون', 'الماركة', 'سعر الشراء', 'سعر البيع', 'الربح'],
                // Data rows
                ...sales.map((sale) {
                  final profit = (sale['suggestedSellingPrice'] ?? 0) - (sale['purchasePrice'] ?? 0);
                  return [
                    sale['updatedAt']?.toString().split(' ')[0] ?? '',
                    sale['type'] ?? '',
                    sale['model'] ?? '',
                    sale['color'] ?? '',
                    sale['brand'] ?? '',
                    '${sale['purchasePrice']?.toStringAsFixed(0) ?? '0'} ج.م',
                    '${sale['suggestedSellingPrice']?.toStringAsFixed(0) ?? '0'} ج.م',
                    '${profit.toStringAsFixed(0)} ج.م',
                  ];
                }),
              ],
              headerStyle: pw.TextStyle(
                fontWeight: pw.FontWeight.bold,
                font: _arabicBoldFont,
                fontSize: 10,
              ),
              cellStyle: pw.TextStyle(
                font: _arabicFont,
                fontSize: 9,
              ),
              headerDecoration: const pw.BoxDecoration(
                color: PdfColors.grey300,
              ),
              cellAlignment: pw.Alignment.center,
              cellPadding: const pw.EdgeInsets.all(5),
              border: pw.TableBorder.all(color: PdfColors.grey),
            ),
            
            pw.SizedBox(height: 20),
            
            // Footer
            pw.Container(
              alignment: pw.Alignment.center,
              child: pw.Text(
                'تم إنشاء هذا التقرير بواسطة تطبيق الفرحان للنقل الخفيف',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _arabicFont,
                  color: PdfColors.grey600,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ),
          ];
        },
      ),
    );

    return pdf.save();
  }

  pw.Widget _buildSummaryItem(String title, String value) {
    return pw.Column(
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 10,
            font: _arabicFont,
            color: PdfColors.grey600,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 5),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            font: _arabicBoldFont,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'available':
        return 'متاح';
      case 'sold':
        return 'مباع';
      case 'transferred':
        return 'محول';
      default:
        return status;
    }
  }

  String _getWarehouseName(String warehouseId) {
    switch (warehouseId) {
      case 'warehouse_001':
        return 'مخزن القاهرة';
      case 'warehouse_002':
        return 'مخزن الإسكندرية';
      case 'warehouse_003':
        return 'معرض الجيزة';
      default:
        return 'غير محدد';
    }
  }

  // Print PDF
  Future<void> printPdf(Uint8List pdfData, String title) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfData,
      name: title,
    );
  }

  // Share PDF
  Future<void> sharePdf(Uint8List pdfData, String title) async {
    await Printing.sharePdf(
      bytes: pdfData,
      filename: '$title.pdf',
    );
  }
}
