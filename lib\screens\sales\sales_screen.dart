
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/invoice_model.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/data_service.dart';
import '../../providers/auth_provider.dart';
import 'create_invoice_screen.dart';
import 'invoice_details_screen.dart';

class SalesScreen extends StatefulWidget {
  const SalesScreen({super.key});

  @override
  State<SalesScreen> createState() => _SalesScreenState();
}

class _SalesScreenState extends State<SalesScreen> with TickerProviderStateMixin {
  final DataService _dataService = DataService.instance;
  late TabController _tabController;
  
  List<InvoiceModel> _allInvoices = [];
  List<InvoiceModel> _filteredInvoices = [];
  List<UserModel> _agents = [];
  List<WarehouseModel> _warehouses = [];
  
  bool _isLoading = true;
  String _searchQuery = '';
  String? _selectedAgentId;
  String? _selectedWarehouseId;
  String _selectedStatus = 'all';
  DateTimeRange? _selectedDateRange;
  
  // Statistics
  Map<String, dynamic> _todayStats = {};
  Map<String, dynamic> _monthStats = {};
  Map<String, dynamic> _totalStats = {};

  final List<String> _statusOptions = [
    'all',
    'pending',
    'paid',
    'cancelled',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Load data in parallel for better performance
      await Future.wait([
        _loadInvoices(),
        _loadAgents(),
        _loadWarehouses(),
      ]);

      if (!mounted) return;

      _calculateStatistics();
      _applyFilters();
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadInvoices() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return;

    if (currentUser.isAgent) {
      // Load only agent's customer invoices (sales to end customers)
      _allInvoices = await _dataService.getAgentInvoices(currentUser.id);
      // Filter to show only customer invoices (not goods transfers)
      _allInvoices = _allInvoices.where((invoice) =>
        invoice.type == 'customer' || invoice.type == AppConstants.customerInvoice).toList();

      if (kDebugMode) {
        print('Loaded ${_allInvoices.length} customer invoices for agent ${currentUser.fullName}');
      }
    } else {
      // Load all customer invoices for admins (all sales to end customers)
      _allInvoices = await _dataService.getInvoices();

      // Filter to show only customer invoices (not goods transfers)
      _allInvoices = _allInvoices.where((invoice) =>
        invoice.type == 'customer' || invoice.type == AppConstants.customerInvoice).toList();

      if (kDebugMode) {
        print('Loaded ${_allInvoices.length} customer invoices for admin ${currentUser.fullName}');
      }
    }
  }

  Future<void> _loadAgents() async {
    final allUsers = await _dataService.getAllUsers();
    _agents = allUsers.where((user) => user.role == 'agent').toList();
  }

  Future<void> _loadWarehouses() async {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    if (currentUser != null && currentUser.isAgent) {
      // Agents should not see warehouse filters - load only their warehouse
      _warehouses = await _dataService.getWarehousesByOwnerId(currentUser.id);
    } else {
      // Admins can see all warehouses
      _warehouses = await _dataService.getAllWarehouses();
    }
  }

  void _calculateStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final monthStart = DateTime(now.year, now.month, 1);
    
    // Today's stats
    final todayInvoices = _allInvoices.where((invoice) {
      final invoiceDate = DateTime(
        invoice.createdAt.year,
        invoice.createdAt.month,
        invoice.createdAt.day,
      );
      return invoiceDate.isAtSameMomentAs(today);
    }).toList();
    
    // This month's stats
    final monthInvoices = _allInvoices.where((invoice) {
      return invoice.createdAt.isAfter(monthStart);
    }).toList();
    
    _todayStats = _calculateStatsForInvoices(todayInvoices);
    _monthStats = _calculateStatsForInvoices(monthInvoices);
    _totalStats = _calculateStatsForInvoices(_allInvoices);
  }

  Map<String, dynamic> _calculateStatsForInvoices(List<InvoiceModel> invoices) {
    double totalSales = 0;
    double totalProfit = 0;
    int totalCount = invoices.length;
    int paidCount = 0;
    int pendingCount = 0;
    
    for (final invoice in invoices) {
      totalSales += invoice.sellingPrice;
      totalProfit += invoice.profitAmount;
      
      if (invoice.status == 'paid') paidCount++;
      if (invoice.status == 'pending') pendingCount++;
    }
    
    return {
      'totalSales': totalSales,
      'totalProfit': totalProfit,
      'totalCount': totalCount,
      'paidCount': paidCount,
      'pendingCount': pendingCount,
      'averageSale': totalCount > 0 ? totalSales / totalCount : 0,
    };
  }

  void _applyFilters() {
    List<InvoiceModel> filtered = List.from(_allInvoices);

    // Enhanced search filter - search in multiple fields
    if (_searchQuery.isNotEmpty) {
      final searchLower = _searchQuery.toLowerCase();
      filtered = filtered.where((invoice) {
        // Search in invoice number
        if (invoice.invoiceNumber.toLowerCase().contains(searchLower)) return true;

        // Search in customer data
        if (invoice.customerName?.toLowerCase().contains(searchLower) ?? false) return true;
        if (invoice.customerPhone?.toLowerCase().contains(searchLower) ?? false) return true;
        if (invoice.customerAddress?.toLowerCase().contains(searchLower) ?? false) return true;
        if (invoice.customerNationalId?.toLowerCase().contains(searchLower) ?? false) return true;

        // Search in additional customer data fields
        if (invoice.customerData != null) {
          final customerData = invoice.customerData!;
          for (final value in customerData.values) {
            if (value != null && value.toString().toLowerCase().contains(searchLower)) {
              return true;
            }
          }
        }

        // Search in selling price
        if (invoice.sellingPrice.toString().contains(searchLower)) return true;

        // Search in status
        if (_getStatusText(invoice.status).toLowerCase().contains(searchLower)) return true;

        // Search in date (formatted)
        if (AppUtils.formatDate(invoice.createdAt).contains(searchLower)) return true;

        return false;
      }).toList();
    }
    
    // Agent filter
    if (_selectedAgentId != null) {
      filtered = filtered.where((invoice) => invoice.agentId == _selectedAgentId).toList();
    }
    
    // Warehouse filter
    if (_selectedWarehouseId != null) {
      filtered = filtered.where((invoice) => invoice.warehouseId == _selectedWarehouseId).toList();
    }
    
    // Status filter
    if (_selectedStatus != 'all') {
      filtered = filtered.where((invoice) => invoice.status == _selectedStatus).toList();
    }
    
    // Date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((invoice) {
        return invoice.createdAt.isAfter(_selectedDateRange!.start) &&
               invoice.createdAt.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }
    
    // Sort by creation date (newest first)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    setState(() {
      _filteredInvoices = filtered;
    });
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      locale: const Locale('ar'),
    );
    
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _applyFilters();
    }
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedAgentId = null;
      _selectedWarehouseId = null;
      _selectedStatus = 'all';
      _selectedDateRange = null;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المبيعات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CreateInvoiceScreen()),
              );
              if (result == true) {
                _loadData();
              }
            },
            tooltip: 'إنشاء فاتورة جديدة',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'الفواتير', icon: Icon(Icons.receipt_long)),
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildInvoicesTab(),
                _buildStatisticsTab(),
              ],
            ),
    );
  }

  Widget _buildInvoicesTab() {
    return Column(
      children: [
        _buildFiltersSection(),
        Expanded(
          child: _buildInvoicesList(),
        ),
      ],
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildStatsCard('إحصائيات اليوم', _todayStats, Colors.blue),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildStatsCard('إحصائيات الشهر', _monthStats, Colors.green),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildStatsCard('الإحصائيات الإجمالية', _totalStats, Colors.orange),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.grey[50],
      child: Column(
        children: [
          // Search field
          TextField(
            decoration: const InputDecoration(
              labelText: 'البحث',
              hintText: 'ابحث برقم الفاتورة، اسم العميل، أو رقم الهاتف',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _applyFilters();
            },
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Filters row
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Agent filter
                if (_agents.isNotEmpty) ...[
                  _buildFilterChip(
                    label: 'الوكيل',
                    value: _selectedAgentId != null
                        ? _agents.firstWhere((a) => a.id == _selectedAgentId).fullName
                        : null,
                    onTap: () => _showAgentFilter(),
                  ),
                  const SizedBox(width: 8),
                ],

                // Warehouse filter - only show for admins, not agents
                if (_warehouses.isNotEmpty && !Provider.of<AuthProvider>(context, listen: false).isAgent) ...[
                  _buildFilterChip(
                    label: 'المخزن',
                    value: _selectedWarehouseId != null
                        ? _warehouses.firstWhere((w) => w.id == _selectedWarehouseId).name
                        : null,
                    onTap: () => _showWarehouseFilter(),
                  ),
                  const SizedBox(width: 8),
                ],

                // Status filter
                _buildFilterChip(
                  label: 'الحالة',
                  value: _selectedStatus != 'all' ? _getStatusText(_selectedStatus) : null,
                  onTap: () => _showStatusFilter(),
                ),
                const SizedBox(width: 8),

                // Date range filter
                _buildFilterChip(
                  label: 'التاريخ',
                  value: _selectedDateRange != null
                      ? '${AppUtils.formatDate(_selectedDateRange!.start)} - ${AppUtils.formatDate(_selectedDateRange!.end)}'
                      : null,
                  onTap: _selectDateRange,
                ),
                const SizedBox(width: 8),

                // Clear filters
                if (_hasActiveFilters()) ...[
                  ElevatedButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح الفلاتر'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: AppConstants.smallPadding),

          // Results count
          Text(
            'عدد النتائج: ${_filteredInvoices.length} من ${_allInvoices.length}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    String? value,
    required VoidCallback onTap,
  }) {
    return FilterChip(
      label: Text(value != null ? '$label: $value' : label),
      selected: value != null,
      onSelected: (_) => onTap(),
      backgroundColor: Colors.white,
      selectedColor: Theme.of(context).primaryColor.withAlpha(51),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  bool _hasActiveFilters() {
    return _searchQuery.isNotEmpty ||
           _selectedAgentId != null ||
           _selectedWarehouseId != null ||
           _selectedStatus != 'all' ||
           _selectedDateRange != null;
  }

  Widget _buildInvoicesList() {
    if (_filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              _hasActiveFilters() ? 'لا توجد فواتير تطابق الفلاتر المحددة' : 'لا توجد فواتير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _filteredInvoices[index];
        return _buildInvoiceCard(invoice);
      },
    );
  }

  Widget _buildInvoiceCard(InvoiceModel invoice) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(invoice.status),
          child: Icon(
            _getStatusIcon(invoice.status),
            color: Colors.white,
          ),
        ),
        title: Text(
          'فاتورة رقم: ${invoice.invoiceNumber}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('العميل: ${invoice.customerName ?? 'غير محدد'}'),
            Text('المبلغ: ${AppUtils.formatCurrency(invoice.sellingPrice)}'),
            Text('التاريخ: ${AppUtils.formatDate(invoice.createdAt)}'),
            Text('الحالة: ${_getStatusText(invoice.status)}'),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showInvoiceDetails(invoice),
      ),
    );
  }

  Widget _buildStatsCard(String title, Map<String, dynamic> stats, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي المبيعات',
                    AppUtils.formatCurrency(stats['totalSales'] ?? 0),
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الأرباح',
                    AppUtils.formatCurrency(stats['totalProfit'] ?? 0),
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'عدد الفواتير',
                    '${stats['totalCount'] ?? 0}',
                    Icons.receipt,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'متوسط البيع',
                    AppUtils.formatCurrency(stats['averageSale'] ?? 0),
                    Icons.analytics,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'مدفوعة',
                    '${stats['paidCount'] ?? 0}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'معلقة',
                    '${stats['pendingCount'] ?? 0}',
                    Icons.pending,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.pending;
      case 'paid':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.receipt;
    }
  }

  void _showAgentFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر الوكيل'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('جميع الوكلاء'),
                leading: Radio<String?>(
                  value: null,
                  groupValue: _selectedAgentId,
                  onChanged: (value) {
                    setState(() {
                      _selectedAgentId = value;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                ),
              ),
              ..._agents.map((agent) => ListTile(
                title: Text(agent.fullName),
                leading: Radio<String?>(
                  value: agent.id,
                  groupValue: _selectedAgentId,
                  onChanged: (value) {
                    setState(() {
                      _selectedAgentId = value;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void _showWarehouseFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر المخزن'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('جميع المخازن'),
                leading: Radio<String?>(
                  value: null,
                  groupValue: _selectedWarehouseId,
                  onChanged: (value) {
                    setState(() {
                      _selectedWarehouseId = value;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                ),
              ),
              ..._warehouses.map((warehouse) => ListTile(
                title: Text(warehouse.name),
                leading: Radio<String?>(
                  value: warehouse.id,
                  groupValue: _selectedWarehouseId,
                  onChanged: (value) {
                    setState(() {
                      _selectedWarehouseId = value;
                    });
                    Navigator.pop(context);
                    _applyFilters();
                  },
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void _showStatusFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر الحالة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: _statusOptions.map((status) => ListTile(
              title: Text(status == 'all' ? 'جميع الحالات' : _getStatusText(status)),
              leading: Radio<String>(
                value: status,
                groupValue: _selectedStatus,
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                  Navigator.pop(context);
                  _applyFilters();
                },
              ),
            )).toList(),
          ),
        ),
      ),
    );
  }

  void _showInvoiceDetails(InvoiceModel invoice) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoiceDetailsScreen(invoice: invoice),
      ),
    );
  }
}
