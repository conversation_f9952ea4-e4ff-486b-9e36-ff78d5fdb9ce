import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(AppConstants.primaryColorValue),
        brightness: Brightness.light,
      ),
      // fontFamily: 'Cairo', // تعطيل الخطوط العربية مؤقتاً
      textTheme: ThemeData.light().textTheme.copyWith(
          // تحسين ألوان النصوص لتكون أكثر وضوحاً
          bodyLarge: const TextStyle(
            color: Color(0xFF212121), // رمادي غامق بدلاً من الأسود
            fontWeight: FontWeight.w400, // وزن عادي
          ),
          bodyMedium: const TextStyle(
            color: Color(0xFF424242), // رمادي متوسط
            fontWeight: FontWeight.w400,
          ),
          bodySmall: const TextStyle(
            color: Color(0xFF616161), // رمادي فاتح
            fontWeight: FontWeight.w400,
          ),
          titleLarge: const TextStyle(
            color: Color(0xFF212121),
            fontWeight: FontWeight.w500, // وزن متوسط للعناوين
          ),
          titleMedium: const TextStyle(
            color: Color(0xFF212121),
            fontWeight: FontWeight.w500,
          ),
          titleSmall: const TextStyle(
            color: Color(0xFF424242),
            fontWeight: FontWeight.w500,
          ),
          headlineLarge: const TextStyle(
            color: Color(0xFF212121),
            fontWeight: FontWeight.w600,
          ),
          headlineMedium: const TextStyle(
            color: Color(0xFF212121),
            fontWeight: FontWeight.w600,
          ),
          headlineSmall: const TextStyle(
            color: Color(0xFF212121),
            fontWeight: FontWeight.w600,
          ),
          labelLarge: const TextStyle(
            color: Color(0xFF424242),
            fontWeight: FontWeight.w500,
          ),
          labelMedium: const TextStyle(
            color: Color(0xFF616161),
            fontWeight: FontWeight.w400,
          ),
          labelSmall: const TextStyle(
            color: Color(0xFF757575),
            fontWeight: FontWeight.w400,
          ),
        ),
      
      // AppBar Theme
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: AppConstants.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          borderSide: BorderSide(
            color: Colors.grey.shade300,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          borderSide: const BorderSide(
            color: Color(AppConstants.primaryColorValue),
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          borderSide: const BorderSide(
            color: Color(AppConstants.errorColorValue),
          ),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largePadding,
            vertical: AppConstants.defaultPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultPadding,
            vertical: AppConstants.smallPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        elevation: AppConstants.cardElevation,
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(AppConstants.primaryColorValue),
        brightness: Brightness.dark,
      ),
      // fontFamily: 'Cairo', // تعطيل الخطوط العربية مؤقتاً
      textTheme: ThemeData.dark().textTheme.copyWith(
          // تحسين ألوان النصوص للوضع المظلم
          bodyLarge: const TextStyle(
            color: Color(0xFFE0E0E0), // أبيض مائل للرمادي
            fontWeight: FontWeight.w400,
          ),
          bodyMedium: const TextStyle(
            color: Color(0xFFBDBDBD), // رمادي فاتح
            fontWeight: FontWeight.w400,
          ),
          bodySmall: const TextStyle(
            color: Color(0xFF9E9E9E), // رمادي متوسط
            fontWeight: FontWeight.w400,
          ),
          titleLarge: const TextStyle(
            color: Color(0xFFFFFFFF),
            fontWeight: FontWeight.w500,
          ),
          titleMedium: const TextStyle(
            color: Color(0xFFFFFFFF),
            fontWeight: FontWeight.w500,
          ),
          titleSmall: const TextStyle(
            color: Color(0xFFE0E0E0),
            fontWeight: FontWeight.w500,
          ),
          headlineLarge: const TextStyle(
            color: Color(0xFFFFFFFF),
            fontWeight: FontWeight.w600,
          ),
          headlineMedium: const TextStyle(
            color: Color(0xFFFFFFFF),
            fontWeight: FontWeight.w600,
          ),
          headlineSmall: const TextStyle(
            color: Color(0xFFFFFFFF),
            fontWeight: FontWeight.w600,
          ),
          labelLarge: const TextStyle(
            color: Color(0xFFBDBDBD),
            fontWeight: FontWeight.w500,
          ),
          labelMedium: const TextStyle(
            color: Color(0xFF9E9E9E),
            fontWeight: FontWeight.w400,
          ),
          labelSmall: const TextStyle(
            color: Color(0xFF757575),
            fontWeight: FontWeight.w400,
          ),
        ),
      
      // AppBar Theme
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: AppConstants.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largePadding,
            vertical: AppConstants.defaultPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        elevation: AppConstants.cardElevation,
      ),
    );
  }
}
