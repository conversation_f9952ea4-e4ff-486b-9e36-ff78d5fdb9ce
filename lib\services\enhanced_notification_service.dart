import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'dart:convert';
import 'dart:io';

import '../core/constants/app_constants.dart';
import '../core/utils/app_utils.dart';
import '../models/notification_model.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';
import 'data_service.dart';
import 'auth_service.dart';

class EnhancedNotificationService {
  static EnhancedNotificationService? _instance;
  static EnhancedNotificationService get instance => _instance ??= EnhancedNotificationService._();
  
  EnhancedNotificationService._();

  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final DataService _dataService = DataService.instance;
  final AuthService _authService = AuthService.instance;


  bool _isInitialized = false;
  String? _fcmToken;

  // In-app notifications
  final List<NotificationModel> _inAppNotifications = [];
  int _unreadCount = 0;

  // Notification settings
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  Set<String> _enabledNotificationTypes = {
    'transfer',
    'sale',
    'payment',
    'inventory_add',
    'document_update',
  };

  // Callbacks for navigation
  Function(NotificationModel)? onNotificationReceived;
  Function(String)? onNotificationTapped;
  
  // Notification channels
  static const String _invoiceChannelId = 'invoice_notifications';
  static const String _paymentChannelId = 'payment_notifications';
  static const String _documentChannelId = 'document_notifications';
  static const String _generalChannelId = 'general_notifications';

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      
      // Setup message handlers
      _setupMessageHandlers();
      
      // Subscribe to topics
      await _subscribeToTopics();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Enhanced notification service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing notification service: $e');
      }
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    // Android initialization
    const androidInitialization = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // iOS initialization
    const iosInitialization = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initializationSettings = InitializationSettings(
      android: androidInitialization,
      iOS: iosInitialization,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    final androidPlugin = _localNotifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    
    if (androidPlugin != null) {
      // Invoice notifications channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          _invoiceChannelId,
          'إشعارات الفواتير',
          description: 'إشعارات إنشاء وتحديث الفواتير',
          importance: Importance.high,
        ),
      );

      // Payment notifications channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          _paymentChannelId,
          'إشعارات الدفعات',
          description: 'إشعارات الدفعات والمعاملات المالية',
          importance: Importance.high,
        ),
      );

      // Document notifications channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          _documentChannelId,
          'إشعارات الجوابات',
          description: 'إشعارات تحديث حالة الجوابات',
          importance: Importance.high,
        ),
      );

      // General notifications channel
      await androidPlugin.createNotificationChannel(
        const AndroidNotificationChannel(
          _generalChannelId,
          'إشعارات عامة',
          description: 'إشعارات عامة ومعلومات النظام',
          importance: Importance.defaultImportance,
        ),
      );
    }
  }

  /// Initialize Firebase messaging
  Future<void> _initializeFirebaseMessaging() async {
    // Request permissions
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      // Get FCM token
      _fcmToken = await _firebaseMessaging.getToken();
      
      if (kDebugMode && _fcmToken != null) {
        debugPrint('📱 FCM Token: $_fcmToken');
      }

      // Save token to user profile
      await _saveFCMTokenToProfile();
    }
  }

  /// Setup message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('📨 Received foreground message: ${message.messageId}');
      print('Title: ${message.notification?.title}');
      print('Body: ${message.notification?.body}');
      print('Data: ${message.data}');
    }

    // Show local notification
    await _showLocalNotification(
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      payload: jsonEncode(message.data),
      channelId: _getChannelIdFromType(message.data['type']),
    );

    // Save to database
    await _saveNotificationToDatabase(message);
  }

  /// Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('📨 Received background message: ${message.messageId}');
    }
    // Background message handling is limited, just log for now
  }

  /// Handle notification tap
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    if (kDebugMode) {
      print('🔔 Notification tapped: ${message.messageId}');
    }
    
    // Handle navigation based on notification type
    await _handleNotificationNavigation(message.data);
  }

  /// Handle local notification tap
  Future<void> _onNotificationTapped(NotificationResponse response) async {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        await _handleNotificationNavigation(data);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error handling notification tap: $e');
        }
      }
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
    String channelId = _generalChannelId,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      _getChannelName(channelId),
      channelDescription: _getChannelDescription(channelId),
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      styleInformation: BigTextStyleInformation(body),
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  /// Send invoice created notification (agent sale → notify managers)
  Future<void> sendInvoiceCreatedNotification({
    required InvoiceModel invoice,
    required UserModel agent,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'فاتورة جديدة - ${invoice.invoiceNumber}',
        message: 'تم إنشاء فاتورة جديدة بواسطة ${agent.fullName}\n'
               'العميل: ${invoice.customerName}\n'
               'المبلغ: ${AppUtils.formatCurrency(invoice.sellingPrice)}',
        type: 'invoice_created',
        targetRole: 'manager', // Send to managers only
        relatedId: invoice.id,
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'agentId': agent.id,
          'agentName': agent.fullName,
          'customerName': invoice.customerName,
          'amount': invoice.sellingPrice,
        },
        createdAt: DateTime.now(),
        createdBy: agent.id,
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification to current user if they're a manager
      final currentUser = _authService.currentUser;
      if (currentUser != null &&
          (currentUser.role == 'manager' || currentUser.role == 'super_admin')) {
        await _showLocalNotification(
          title: notification.title,
          body: notification.message,
          payload: jsonEncode(notification.data ?? {}),
          channelId: _invoiceChannelId,
        );
      }

      if (kDebugMode) {
        print('✅ Invoice created notification sent to managers');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send invoice created notification: $e');
      }
    }
  }

  /// Send payment received notification
  Future<void> sendPaymentReceivedNotification({
    required PaymentModel payment,
    required UserModel agent,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'دفعة جديدة مستلمة',
        message: 'تم استلام دفعة من ${agent.fullName}\n'
               'المبلغ: ${AppUtils.formatCurrency(payment.amount)}\n'
               'طريقة الدفع: ${payment.paymentMethod}',
        type: 'payment_received',
        targetRole: AppConstants.adminRole,
        relatedId: payment.id,
        data: {
          'paymentId': payment.id,
          'agentId': agent.id,
          'agentName': agent.fullName,
          'amount': payment.amount,
          'paymentMethod': payment.paymentMethod,
        },
        createdAt: DateTime.now(),
        createdBy: payment.createdBy,
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification to current user if they're a manager
      final currentUser = _authService.currentUser;
      if (currentUser != null && 
          (currentUser.role == AppConstants.adminRole || currentUser.role == AppConstants.superAdminRole)) {
        await _showLocalNotification(
          title: notification.title,
          body: notification.message,
          payload: jsonEncode(notification.data ?? {}),
          channelId: _paymentChannelId,
        );
      }

      if (kDebugMode) {
        print('✅ Payment received notification sent successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send payment received notification: $e');
      }
    }
  }

  /// Send document status update notification
  Future<void> sendDocumentStatusUpdateNotification({
    required String itemId,
    required String itemDescription,
    required String newStatus,
    required String agentId,
  }) async {
    try {
      final statusText = _getDocumentStatusText(newStatus);

      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تحديث حالة الجواب',
        message: 'تم تحديث حالة جواب $itemDescription\n'
               'الحالة الجديدة: $statusText',
        type: 'document_status_update',
        targetUserId: agentId,
        relatedId: itemId,
        data: {
          'itemId': itemId,
          'itemDescription': itemDescription,
          'newStatus': newStatus,
          'statusText': statusText,
        },
        createdAt: DateTime.now(),
        createdBy: 'system',
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification to the specific agent
      final currentUser = _authService.currentUser;
      if (currentUser != null && currentUser.id == agentId) {
        await _showLocalNotification(
          title: notification.title,
          body: notification.message,
          payload: jsonEncode(notification.data ?? {}),
          channelId: _documentChannelId,
        );
      }

      if (kDebugMode) {
        print('✅ Document status update notification sent successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send document status update notification: $e');
      }
    }
  }

  /// Send agent transfer notification (agent → agent transfer → notify receiving agent)
  Future<void> sendAgentTransferNotification({
    required String fromAgentId,
    required String fromAgentName,
    required String toAgentId,
    required String toAgentName,
    required List<String> itemNames,
    required String transferId,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تحويل بضاعة جديد',
        message: 'تم تحويل ${itemNames.length} صنف إليك من الوكيل $fromAgentName\n'
               'الأصناف: ${itemNames.take(3).join(', ')}${itemNames.length > 3 ? '...' : ''}',
        type: 'agent_transfer',
        targetUserId: toAgentId, // Send to receiving agent only
        relatedId: transferId,
        data: {
          'transferId': transferId,
          'fromAgentId': fromAgentId,
          'fromAgentName': fromAgentName,
          'toAgentId': toAgentId,
          'toAgentName': toAgentName,
          'itemCount': itemNames.length,
          'itemNames': itemNames,
        },
        createdAt: DateTime.now(),
        createdBy: fromAgentId,
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification if current user is the receiving agent
      final currentUser = _authService.currentUser;
      if (currentUser != null && currentUser.id == toAgentId) {
        await _showLocalNotification(
          title: notification.title,
          body: notification.message,
          payload: jsonEncode(notification.data ?? {}),
          channelId: _generalChannelId,
        );
      }

      if (kDebugMode) {
        print('✅ Agent transfer notification sent to receiving agent $toAgentName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send agent transfer notification: $e');
      }
    }
  }

  /// Send payment recorded notification (manager records payment → notify agent)
  Future<void> sendPaymentRecordedNotification({
    required String agentId,
    required String agentName,
    required double amount,
    required String paymentMethod,
    required String recordedBy,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تم تسجيل دفعة',
        message: 'تم تسجيل دفعة بمبلغ ${AppUtils.formatCurrency(amount)}\n'
               'طريقة الدفع: $paymentMethod\n'
               'تم التسجيل بواسطة: $recordedBy',
        type: 'payment_recorded',
        targetUserId: agentId, // Send to the agent whose payment was recorded
        data: {
          'agentId': agentId,
          'agentName': agentName,
          'amount': amount,
          'paymentMethod': paymentMethod,
          'recordedBy': recordedBy,
        },
        createdAt: DateTime.now(),
        createdBy: recordedBy,
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification if current user is the agent
      final currentUser = _authService.currentUser;
      if (currentUser != null && currentUser.id == agentId) {
        await _showLocalNotification(
          title: notification.title,
          body: notification.message,
          payload: jsonEncode(notification.data ?? {}),
          channelId: _generalChannelId,
        );
      }

      if (kDebugMode) {
        print('✅ Payment recorded notification sent to agent $agentName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send payment recorded notification: $e');
      }
    }
  }

  /// Send general notification
  Future<void> sendGeneralNotification({
    required String title,
    required String message,
    String? targetUserId,
    String? targetRole,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: title,
        message: message,
        type: 'general',
        targetUserId: targetUserId,
        targetRole: targetRole,
        data: data,
        createdAt: DateTime.now(),
        createdBy: _authService.currentUser?.id ?? 'system',
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification if targeting current user
      final currentUser = _authService.currentUser;
      if (currentUser != null &&
          (targetUserId == null || targetUserId == currentUser.id) &&
          (targetRole == null || targetRole == currentUser.role)) {
        await _showLocalNotification(
          title: title,
          body: message,
          payload: jsonEncode(data ?? {}),
          channelId: _generalChannelId,
        );
      }

      if (kDebugMode) {
        print('✅ General notification sent successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send general notification: $e');
      }
    }
  }

  /// Get notifications for current user
  Future<List<NotificationModel>> getUserNotifications({
    bool unreadOnly = false,
    int limit = 50,
  }) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return [];

      return await _dataService.getUserNotifications(
        currentUser.id,
        userRole: currentUser.role,
        unreadOnly: unreadOnly,
        limit: limit,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user notifications: $e');
      }
      return [];
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _dataService.markNotificationAsRead(notificationId);

      if (kDebugMode) {
        print('✅ Notification marked as read: $notificationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
    }
  }

  /// Mark all notifications as read for current user
  Future<void> markAllNotificationsAsRead() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      await _dataService.markAllNotificationsAsRead(
        currentUser.id,
        userRole: currentUser.role,
      );

      if (kDebugMode) {
        print('✅ All notifications marked as read');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking all notifications as read: $e');
      }
    }
  }

  /// Get unread notifications count
  Future<int> getUnreadNotificationsCount() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return 0;

      return await _dataService.getUnreadNotificationsCount(
        currentUser.id,
        userRole: currentUser.role,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unread notifications count: $e');
      }
      return 0;
    }
  }

  /// Helper methods
  String _getChannelIdFromType(String? type) {
    switch (type) {
      case 'invoice_created':
        return _invoiceChannelId;
      case 'payment_received':
        return _paymentChannelId;
      case 'document_status_update':
        return _documentChannelId;
      default:
        return _generalChannelId;
    }
  }

  String _getChannelName(String channelId) {
    switch (channelId) {
      case _invoiceChannelId:
        return 'إشعارات الفواتير';
      case _paymentChannelId:
        return 'إشعارات الدفعات';
      case _documentChannelId:
        return 'إشعارات الجوابات';
      default:
        return 'إشعارات عامة';
    }
  }

  String _getChannelDescription(String channelId) {
    switch (channelId) {
      case _invoiceChannelId:
        return 'إشعارات إنشاء وتحديث الفواتير';
      case _paymentChannelId:
        return 'إشعارات الدفعات والمعاملات المالية';
      case _documentChannelId:
        return 'إشعارات تحديث حالة الجوابات';
      default:
        return 'إشعارات عامة ومعلومات النظام';
    }
  }

  String _getDocumentStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'processing':
        return 'قيد المعالجة';
      case 'completed':
        return 'مكتمل';
      case 'rejected':
        return 'مرفوض';
      default:
        return status;
    }
  }

  Future<void> _saveFCMTokenToProfile() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser != null && _fcmToken != null) {
        // Save FCM token to user profile in Firebase
        // TODO: Implement updateUserFCMToken in FirebaseService
        if (kDebugMode) {
          print('📱 FCM Token ready for user ${currentUser.id}: $_fcmToken');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving FCM token: $e');
      }
    }
  }

  Future<void> _subscribeToTopics() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        // Subscribe to role-based topics
        await _firebaseMessaging.subscribeToTopic('role_${currentUser.role}');
        await _firebaseMessaging.subscribeToTopic('user_${currentUser.id}');

        if (kDebugMode) {
          print('✅ Subscribed to notification topics');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error subscribing to topics: $e');
      }
    }
  }

  Future<void> _saveNotificationToDatabase(RemoteMessage message) async {
    try {
      final notification = NotificationModel(
        id: message.messageId ?? AppUtils.generateId(),
        title: message.notification?.title ?? 'إشعار جديد',
        message: message.notification?.body ?? '',
        type: message.data['type'] ?? 'general',
        targetUserId: message.data['targetUserId'],
        targetRole: message.data['targetRole'],
        relatedId: message.data['relatedId'],
        data: message.data.isNotEmpty ? message.data : null,
        createdAt: DateTime.now(),
        createdBy: message.data['createdBy'] ?? 'system',
        isRead: false,
      );

      await _dataService.createNotification(notification);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving notification to database: $e');
      }
    }
  }

  Future<void> _handleNotificationNavigation(Map<String, dynamic> data) async {
    // This would be implemented based on your navigation structure
    // For now, just log the navigation intent
    if (kDebugMode) {
      print('🔄 Navigation requested for notification data: $data');
    }

    // Trigger navigation callback
    final route = data['route'] as String?;
    if (route != null) {
      onNotificationTapped?.call(route);
    }
  }

  // In-app notification management
  List<NotificationModel> get inAppNotifications => List.unmodifiable(_inAppNotifications);
  int get unreadCount => _unreadCount;

  void addInAppNotification(NotificationModel notification) {
    _inAppNotifications.insert(0, notification);
    if (!notification.isRead) {
      _unreadCount++;
    }
    onNotificationReceived?.call(notification);
  }

  void markAsRead(String notificationId) {
    final index = _inAppNotifications.indexWhere((n) => n.id == notificationId);
    if (index != -1 && !_inAppNotifications[index].isRead) {
      _inAppNotifications[index] = _inAppNotifications[index].copyWith(isRead: true);
      _unreadCount = (_unreadCount - 1).clamp(0, _inAppNotifications.length);
    }
  }

  void markAllAsRead() {
    for (int i = 0; i < _inAppNotifications.length; i++) {
      if (!_inAppNotifications[i].isRead) {
        _inAppNotifications[i] = _inAppNotifications[i].copyWith(isRead: true);
      }
    }
    _unreadCount = 0;
  }

  void clearNotifications() {
    _inAppNotifications.clear();
    _unreadCount = 0;
  }

  // Notification settings
  bool get notificationsEnabled => _notificationsEnabled;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  Set<String> get enabledNotificationTypes => Set.from(_enabledNotificationTypes);

  void updateNotificationSettings({
    bool? enabled,
    bool? sound,
    bool? vibration,
    Set<String>? enabledTypes,
  }) {
    if (enabled != null) _notificationsEnabled = enabled;
    if (sound != null) _soundEnabled = sound;
    if (vibration != null) _vibrationEnabled = vibration;
    if (enabledTypes != null) _enabledNotificationTypes = Set.from(enabledTypes);
  }

  // General notification creation method
  Future<void> createNotification({
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Create in-app notification
      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        message: body,
        type: type,
        data: data,
        createdAt: DateTime.now(),
        createdBy: 'system',
        isRead: false,
      );

      // Add to in-app notifications
      addInAppNotification(notification);

      // Show local notification
      await _showLocalNotification(
        title: title,
        body: body,
        payload: jsonEncode({
          'id': notification.id,
          'type': type,
          'route': data?['route'],
        }),
        channelId: '${type}_channel',
      );

      // Save to database
      await _dataService.createNotification(notification);

      if (kDebugMode) {
        print('📨 Notification created: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
    }
  }

  // Enhanced notification creation methods
  Future<void> notifyTransferCreated({
    required String fromWarehouse,
    required String toWarehouse,
    required int itemCount,
    required String transferId,
  }) async {
    if (!_enabledNotificationTypes.contains('transfer')) return;

    await createNotification(
      title: 'تحويل مخزن جديد',
      body: 'تم تحويل $itemCount صنف من $fromWarehouse إلى $toWarehouse',
      type: 'transfer',
      data: {
        'route': '/transfer_details/$transferId',
        'transferId': transferId,
        'fromWarehouse': fromWarehouse,
        'toWarehouse': toWarehouse,
        'itemCount': itemCount,
      },
    );
  }

  Future<void> notifySaleCreated({
    required String agentName,
    required String customerName,
    required double amount,
    required String invoiceId,
  }) async {
    if (!_enabledNotificationTypes.contains('sale')) return;

    await createNotification(
      title: 'فاتورة مبيعات جديدة',
      body: 'فاتورة بقيمة ${amount.toStringAsFixed(0)} ج.م للعميل $customerName بواسطة $agentName',
      type: 'sale',
      data: {
        'route': '/invoice_details/$invoiceId',
        'invoiceId': invoiceId,
        'agentName': agentName,
        'customerName': customerName,
        'amount': amount,
      },
    );
  }

  Future<void> notifyPaymentReceived({
    required String agentName,
    required double amount,
    required String paymentId,
  }) async {
    if (!_enabledNotificationTypes.contains('payment')) return;

    await createNotification(
      title: 'دفعة جديدة مستلمة',
      body: 'تم استلام دفعة بقيمة ${amount.toStringAsFixed(0)} ج.م من الوكيل $agentName',
      type: 'payment',
      data: {
        'route': '/payment_details/$paymentId',
        'paymentId': paymentId,
        'agentName': agentName,
        'amount': amount,
      },
    );
  }

  Future<void> notifyInventoryAdded({
    required String itemName,
    required int quantity,
    required String warehouseName,
  }) async {
    if (!_enabledNotificationTypes.contains('inventory_add')) return;

    await createNotification(
      title: 'إضافة مخزون جديد',
      body: 'تم إضافة $quantity من $itemName إلى مخزن $warehouseName',
      type: 'inventory_add',
      data: {
        'route': '/inventory',
        'itemName': itemName,
        'quantity': quantity,
        'warehouseName': warehouseName,
      },
    );
  }

  Future<void> notifyDocumentStatusUpdate({
    required String customerName,
    required String status,
    required String documentId,
  }) async {
    if (!_enabledNotificationTypes.contains('document_update')) return;

    await createNotification(
      title: 'تحديث حالة الوثيقة',
      body: 'تم تحديث حالة وثيقة العميل $customerName إلى: $status',
      type: 'document_update',
      data: {
        'route': '/document_details/$documentId',
        'documentId': documentId,
        'customerName': customerName,
        'status': status,
      },
    );
  }
}
