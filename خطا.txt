Launching lib\main.dart on RMX2170 in debug mode...
Running Gradle task 'assembleDebug'...
I/flutter (27173): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
I/flutter (27173): Firestore configured for offline persistence
I/flutter (27173): User granted permission for notifications
I/flutter (27173): FCM Token: ePCO69jWRNisNJv-q7vuHT:APA91bH3q79PuX9Ui6j95utNSoV4PASANq2P5RCVpayicKkiiklQuCOiR7hnSOlkKuy5hNOdBjswyhlGo8GV4t-OiGfk_yZ7jtJiTIXfChlgAos1Ys4KegI
I/flutter (27173): Firebase initialized successfully
I/flutter (27173): LocalDatabaseService initialized successfully
I/flutter (27173): Image service initialized
I/flutter (27173): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
I/flutter (27173): Notification permission status: AuthorizationStatus.authorized
I/flutter (27173): Notification service initialized
I/flutter (27173): Firebase user creation failed: [cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
I/flutter (27173): Please create the admin user manually in Firebase Console
I/flutter (27173): Email: <EMAIL>, Password: admin123
I/flutter (27173): === DEFAULT ADMIN USER CREATED ===
I/flutter (27173): No existing users found in database.
I/flutter (27173): Created default admin user for first-time setup:
I/flutter (27173): Username: ahmed
I/flutter (27173): Password: admin123
I/flutter (27173): Role: Super Admin
I/flutter (27173): يمكنك الآن تسجيل الدخول باستخدام هذه البيانات
I/flutter (27173): =====================================
I/flutter (27173): All services initialized successfully
I/flutter (27173): User session saved successfully
I/flutter (27173): Admin user signed in locally: أحمد محمد - المدير الأعلى
I/flutter (27173): Subscribed to notification topics for user: أحمد محمد - المدير الأعلى
I/flutter (27173): Error updating FCM token: [cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
I/flutter (27173): User session setup completed for: أحمد محمد - المدير الأعلى
I/flutter (27173): Error querying notifications: DatabaseException(no such column: targetUserId (code 1 SQLITE_ERROR): , while compiling: SELECT * FROM notifications WHERE targetUserId = ? OR targetUserId IS NULL ORDER BY createdAt DESC) sql 'SELECT * FROM notifications WHERE targetUserId = ? OR targetUserId IS NULL ORDER BY createdAt DESC' args [admin_001]
I/flutter (27173): Error getting user notifications: DatabaseException(no such column: targetUserId (code 1 SQLITE_ERROR): , while compiling: SELECT * FROM notifications WHERE targetUserId = ? OR targetUserId IS NULL ORDER BY createdAt DESC) sql 'SELECT * FROM notifications WHERE targetUserId = ? OR targetUserId IS NULL ORDER BY createdAt DESC' args [admin_001]
√ Built build\app\outputs\flutter-apk\app-debug.apk
I/flutter (27173): Sample items created successfully
I/flutter (27173): 📱 App paused at 2025-06-25 01:40:15.695034
I/flutter (27173): ! Timer not found for: app_session
I/flutter (27173): 📱 App detached at 2025-06-25 01:40:15.785636
I/flutter (27173): 📊 No performance data available
Installing build\app\outputs\flutter-apk\app-debug.apk...
I/flutter (28071): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
I/flutter (28071): Firestore configured for offline persistence
I/flutter (28071): User granted permission for notifications
I/flutter (28071): FCM Token: ePCO69jWRNisNJv-q7vuHT:APA91bH3q79PuX9Ui6j95utNSoV4PASANq2P5RCVpayicKkiiklQuCOiR7hnSOlkKuy5hNOdBjswyhlGo8GV4t-OiGfk_yZ7jtJiTIXfChlgAos1Ys4KegI
I/flutter (28071): Firebase initialized successfully
I/flutter (28071): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
I/flutter (28071): LocalDatabaseService initialized successfully
I/flutter (28071): Image service initialized
I/flutter (28071): Notification permission status: AuthorizationStatus.authorized
I/flutter (28071): Notification service initialized
I/flutter (28071): Firebase user creation failed: [cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
I/flutter (28071): Please create the admin user manually in Firebase Console
I/flutter (28071): Email: <EMAIL>, Password: admin123
I/flutter (28071): === DEFAULT ADMIN USER CREATED ===
I/flutter (28071): No existing users found in database.
I/flutter (28071): Created default admin user for first-time setup:
I/flutter (28071): Username: ahmed
I/flutter (28071): Password: admin123
I/flutter (28071): Role: Super Admin
I/flutter (28071): يمكنك الآن تسجيل الدخول باستخدام هذه البيانات
I/flutter (28071): =====================================
I/flutter (28071): All services initialized successfully
I/flutter (28595): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
Debug service listening on ws://127.0.0.1:49670/PSIiwlpB0OQ=/ws
Syncing files to device RMX2170...
I/Quality (28595): Skipped: false 1 cost 29.445944 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 18.254087 refreshRate 0 processName com.alfarhan.transport
D/ProfileInstaller(28595): Installing profile for com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 19.361471 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 7 cost 119.19795 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 18.66862 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 8 cost 143.51622 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 22 cost 374.07333 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 3 cost 58.406704 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 13 cost 225.65392 refreshRate 0 processName com.alfarhan.transport
E/BLASTBufferQueue(28595): BLASTBufferItemConsumer::onDisconnect()
E/ViewRootImpl[MainActivity](28595): setWindowStopped mStopped=true,wait blast:false,1
E/BLASTBufferQueue(28595): BLASTBufferItemConsumer::onDisconnect()
E/ViewRootImpl[MainActivity](28595): setWindowStopped mStopped=false,wait blast:false,1
E/BLASTBufferQueue(28595): BLASTBufferItemConsumer::onDisconnect()
W/DynamiteModule(28595): Local module descriptor class for com.google.android.gms.providerinstaller.dynamite not found.
I/DynamiteModule(28595): Considering local module com.google.android.gms.providerinstaller.dynamite:0 and remote module com.google.android.gms.providerinstaller.dynamite:0
W/ProviderInstaller(28595): Failed to load providerinstaller module: No acceptable module com.google.android.gms.providerinstaller.dynamite found. Local version is 0 and remote version is 0.
D/nativeloader(28595): Configuring clns-5 for other apk /system/framework/org.apache.http.legacy.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/lib/arm64:/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
D/nativeloader(28595): Extending system_exposed_libraries: libvraudio_client.qti.so:libbinauralrenderer_wrapper.qti.so:libhoaeffects.qti.so:libQOC.qti.so:libSloganJni.oplus.so:libsuperNight_mtk.oplus.so:libupdateprof.qti.so:libQOC.qti.so:libdiag_system.qti.so:libqape.qti.so:libqesdk_ndk_platform.qti.so:liblistenjni.qti.so
D/nativeloader(28595): Configuring clns-6 for other apk /apex/com.android.extservices/javalib/android.ext.adservices.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/lib/arm64:/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
D/nativeloader(28595): Extending system_exposed_libraries: libvraudio_client.qti.so:libbinauralrenderer_wrapper.qti.so:libhoaeffects.qti.so:libQOC.qti.so:libSloganJni.oplus.so:libsuperNight_mtk.oplus.so:libupdateprof.qti.so:libQOC.qti.so:libdiag_system.qti.so:libqape.qti.so:libqesdk_ndk_platform.qti.so:liblistenjni.qti.so
D/nativeloader(28595): InitApexLibraries:
D/nativeloader(28595):   com_android_appsearch: libicing.so
D/nativeloader(28595):   com_android_art: libartservice.so
D/nativeloader(28595):   com_android_conscrypt: libjavacrypto.so
D/nativeloader(28595):   com_android_extservices: libtflite_support_classifiers_native.so
D/nativeloader(28595):   com_android_mediaprovider: libpdfclient.so
D/nativeloader(28595):   com_android_os_statsd: libstats_jni.so
D/nativeloader(28595):   com_android_tethering: libandroid_net_connectivity_com_android_net_module_util_jni.so:libframework-connectivity-jni.so:libframework-connectivity-tiramisu-jni.so:libmainlinecronet.133.0.6876.3.so:libservice-connectivity.so:libservice-thread-jni.so:stable_cronet_libcrypto.so:stable_cronet_libssl.so
D/nativeloader(28595): Configuring clns-7 for other apk /system/framework/com.android.media.remotedisplay.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/lib/arm64:/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
D/nativeloader(28595): Extending system_exposed_libraries: libvraudio_client.qti.so:libbinauralrenderer_wrapper.qti.so:libhoaeffects.qti.so:libQOC.qti.so:libSloganJni.oplus.so:libsuperNight_mtk.oplus.so:libupdateprof.qti.so:libQOC.qti.so:libdiag_system.qti.so:libqape.qti.so:libqesdk_ndk_platform.qti.so:liblistenjni.qti.so
D/nativeloader(28595): Configuring clns-8 for other apk /system/framework/com.android.location.provider.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/lib/arm64:/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
D/nativeloader(28595): Extending system_exposed_libraries: libvraudio_client.qti.so:libbinauralrenderer_wrapper.qti.so:libhoaeffects.qti.so:libQOC.qti.so:libSloganJni.oplus.so:libsuperNight_mtk.oplus.so:libupdateprof.qti.so:libQOC.qti.so:libdiag_system.qti.so:libqape.qti.so:libqesdk_ndk_platform.qti.so:liblistenjni.qti.so
D/nativeloader(28595): Configuring clns-9 for other apk /data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk. target_sdk_version=36, uses_libraries=, library_path=/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/lib/arm64:/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
I/flutter (28595): Firestore configured for offline persistence
I/flutter (28595): User granted permission for notifications
W/ProviderInstaller(28595): Failed to report request stats: com.google.android.gms.common.security.ProviderInstallerImpl.reportRequestStats [class android.content.Context, long, long]
I/flutter (28595): FCM Token: ePCO69jWRNisNJv-q7vuHT:APA91bH3q79PuX9Ui6j95utNSoV4PASANq2P5RCVpayicKkiiklQuCOiR7hnSOlkKuy5hNOdBjswyhlGo8GV4t-OiGfk_yZ7jtJiTIXfChlgAos1Ys4KegI
I/flutter (28595): Firebase initialized successfully
I/arhan.transpor(28595): hiddenapi: Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (runtime_flags=0, domain=core-platform, api=unsupported) from Lfwyb; (domain=app) using reflection: allowed
I/FLTFireBGExecutor(28595): Creating background FlutterEngine instance, with args: [--start-paused, --enable-dart-profiling]
I/AdrenoVK-0(28595): QUALCOMM build          : c5f0e23, I9972db7aac
I/AdrenoVK-0(28595): Build Date              : 04/28/21
I/AdrenoVK-0(28595): Shader Compiler Version : EV031.32.02.10
I/AdrenoVK-0(28595): Local Branch            : 
I/AdrenoVK-0(28595): Remote Branch           : 
I/AdrenoVK-0(28595): Remote Branch           : 
I/AdrenoVK-0(28595): Reconstruct Branch      : 
I/AdrenoVK-0(28595): Build Config            : S P 10.0.7 AArch64
I/AdrenoVK-0(28595): Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
I/flutter (28595): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(61)] Using the Impeller rendering backend (Vulkan).
D/FLTFireContextHolder(28595): received application context.
D/nativeloader(28595): Load /data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk!/lib/arm64-v8a/libconscrypt_gmscore_jni.so using class loader ns clns-9 (caller=/data/app/~~KvTcueNRBgXEG6rRcy5nQg==/com.google.android.gms-j6vdhfqyk5M0S80CIlZTyA==/base.apk): ok
V/NativeCrypto(28595): Registering com/google/android/gms/org/conscrypt/NativeCrypto's 315 native methods...
I/arhan.transpor(28595): hiddenapi: Accessing hidden method Ljava/security/spec/ECParameterSpec;->getCurveName()Ljava/lang/String; (runtime_flags=0, domain=core-platform, api=unsupported) from Lcom/google/android/gms/org/conscrypt/Platform; (domain=app) using reflection: allowed
I/ProviderInstaller(28595): Installed default security provider GmsCore_OpenSSL
I/arhan.transpor(28595): Background concurrent copying GC freed 1972KB AllocSpace bytes, 3(544KB) LOS objects, 49% free, 5797KB/11MB, paused 156us,139us total 137.605ms
I/Choreographer(28595): Skipped 43 frames!  The application may be doing too much work on its main thread.
I/flutter (28595): LocalDatabaseService initialized successfully
I/flutter (28595): Image service initialized
I/flutter (28595): Notification permission status: AuthorizationStatus.authorized
E/GoogleApiManager(28595): Failed to get service from broker. 
E/GoogleApiManager(28595): java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
E/GoogleApiManager(28595): 	at android.os.Parcel.createExceptionOrNull(Parcel.java:2442)
E/GoogleApiManager(28595): 	at android.os.Parcel.createException(Parcel.java:2426)
E/GoogleApiManager(28595): 	at android.os.Parcel.readException(Parcel.java:2409)
E/GoogleApiManager(28595): 	at android.os.Parcel.readException(Parcel.java:2351)
E/GoogleApiManager(28595): 	at axtl.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):36)
E/GoogleApiManager(28595): 	at axrs.z(:com.google.android.gms@252234029@25.22.34 (190400-769260661):143)
E/GoogleApiManager(28595): 	at awyv.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):42)
E/GoogleApiManager(28595): 	at android.os.Handler.handleCallback(Handler.java:938)
E/GoogleApiManager(28595): 	at android.os.Handler.dispatchMessage(Handler.java:99)
E/GoogleApiManager(28595): 	at chad.mH(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
E/GoogleApiManager(28595): 	at chad.dispatchMessage(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
E/GoogleApiManager(28595): 	at android.os.Looper.loopOnce(Looper.java:233)
E/GoogleApiManager(28595): 	at android.os.Looper.loop(Looper.java:344)
E/GoogleApiManager(28595): 	at android.os.HandlerThread.run(HandlerThread.java:67)
I/flutter (28595): Notification service initialized
W/FlagRegistrar(28595): Failed to register com.google.android.gms.providerinstaller#com.alfarhan.transport
W/FlagRegistrar(28595): fghm: 17: 17: API: Phenotype.API is not available on this device. Connection failed with: ConnectionResult{statusCode=DEVELOPER_ERROR, resolution=null, message=null}
W/FlagRegistrar(28595): 	at fgho.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):13)
W/FlagRegistrar(28595): 	at gbtj.d(:com.google.android.gms@252234029@25.22.34 (190400-769260661):3)
W/FlagRegistrar(28595): 	at gbtl.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):130)
W/FlagRegistrar(28595): 	at gbvs.execute(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
W/FlagRegistrar(28595): 	at gbtt.f(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
W/FlagRegistrar(28595): 	at gbtt.m(:com.google.android.gms@252234029@25.22.34 (190400-769260661):99)
W/FlagRegistrar(28595): 	at gbtt.r(:com.google.android.gms@252234029@25.22.34 (190400-769260661):17)
W/FlagRegistrar(28595): 	at eyqj.hX(:com.google.android.gms@252234029@25.22.34 (190400-769260661):35)
W/FlagRegistrar(28595): 	at emvm.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):12)
W/FlagRegistrar(28595): 	at gbvs.execute(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
W/FlagRegistrar(28595): 	at emvn.b(:com.google.android.gms@252234029@25.22.34 (190400-769260661):18)
W/FlagRegistrar(28595): 	at emwc.b(:com.google.android.gms@252234029@25.22.34 (190400-769260661):36)
W/FlagRegistrar(28595): 	at emwe.d(:com.google.android.gms@252234029@25.22.34 (190400-769260661):25)
W/FlagRegistrar(28595): 	at awwd.c(:com.google.android.gms@252234029@25.22.34 (190400-769260661):9)
W/FlagRegistrar(28595): 	at awyt.q(:com.google.android.gms@252234029@25.22.34 (190400-769260661):48)
W/FlagRegistrar(28595): 	at awyt.d(:com.google.android.gms@252234029@25.22.34 (190400-769260661):10)
W/FlagRegistrar(28595): 	at awyt.g(:com.google.android.gms@252234029@25.22.34 (190400-769260661):185)
W/FlagRegistrar(28595): 	at awyt.onConnectionFailed(:com.google.android.gms@252234029@25.22.34 (190400-769260661):2)
W/FlagRegistrar(28595): 	at awyv.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):70)
W/FlagRegistrar(28595): 	at android.os.Handler.handleCallback(Handler.java:938)
W/FlagRegistrar(28595): 	at android.os.Handler.dispatchMessage(Handler.java:99)
W/FlagRegistrar(28595): 	at chad.mH(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
W/FlagRegistrar(28595): 	at chad.dispatchMessage(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
W/FlagRegistrar(28595): 	at android.os.Looper.loopOnce(Looper.java:233)
W/FlagRegistrar(28595): 	at android.os.Looper.loop(Looper.java:344)
W/FlagRegistrar(28595): 	at android.os.HandlerThread.run(HandlerThread.java:67)
W/FlagRegistrar(28595): Caused by: awur: 17: API: Phenotype.API is not available on this device. Connection failed with: ConnectionResult{statusCode=DEVELOPER_ERROR, resolution=null, message=null}
W/FlagRegistrar(28595): 	at axre.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):15)
W/FlagRegistrar(28595): 	at awwg.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
W/FlagRegistrar(28595): 	at awwd.c(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
W/FlagRegistrar(28595): 	... 12 more
I/arhan.transpor(28595): hiddenapi: Accessing hidden field Ljava/net/Socket;->impl:Ljava/net/SocketImpl; (runtime_flags=0, domain=core-platform, api=unsupported) from Lcom/google/android/gms/org/conscrypt/Platform; (domain=app) using reflection: allowed
I/arhan.transpor(28595): hiddenapi: Accessing hidden method Ljava/security/spec/ECParameterSpec;->setCurveName(Ljava/lang/String;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lcom/google/android/gms/org/conscrypt/Platform; (domain=app) using reflection: allowed
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(users order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/flutter (28595): Firebase user creation failed: [cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
I/flutter (28595): Please create the admin user manually in Firebase Console
I/flutter (28595): Email: <EMAIL>, Password: admin123
I/flutter (28595): === DEFAULT ADMIN USER CREATED ===
I/flutter (28595): No existing users found in database.
I/flutter (28595): Created default admin user for first-time setup:
I/flutter (28595): Username: ahmed
I/flutter (28595): Password: admin123
I/flutter (28595): Role: Super Admin
I/flutter (28595): يمكنك الآن تسجيل الدخول باستخدام هذه البيانات
I/flutter (28595): =====================================
I/flutter (28595): All services initialized successfully
I/Choreographer(28595): Skipped 74 frames!  The application may be doing too much work on its main thread.
D/SurfaceComposerClient(28595): VRR [FRTC] client handle [bufferId:18446744073709551615 framenumber:0] [ffffffff, ffffffff]
D/SurfaceComposerClient(28595): VRR [FRTC] client handle [bufferId:18446744073709551615 framenumber:0] [ffffffff, ffffffff]
D/ViewRootImpl[MainActivity](28595):  debugCancelDraw  cancelDraw=false,count = 841,android.view.ViewRootImpl@f79dee2
I/FLTFireMsgService(28595): FlutterFirebaseMessagingBackgroundService started!
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F....ID 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
E/GoogleApiManager(28595): Failed to get service from broker. 
E/GoogleApiManager(28595): java.lang.SecurityException: Unknown calling package name 'com.google.android.gms'.
E/GoogleApiManager(28595): 	at android.os.Parcel.createExceptionOrNull(Parcel.java:2442)
E/GoogleApiManager(28595): 	at android.os.Parcel.createException(Parcel.java:2426)
E/GoogleApiManager(28595): 	at android.os.Parcel.readException(Parcel.java:2409)
E/GoogleApiManager(28595): 	at android.os.Parcel.readException(Parcel.java:2351)
E/GoogleApiManager(28595): 	at axtl.a(:com.google.android.gms@252234029@25.22.34 (190400-769260661):36)
E/GoogleApiManager(28595): 	at axrs.z(:com.google.android.gms@252234029@25.22.34 (190400-769260661):143)
E/GoogleApiManager(28595): 	at awyv.run(:com.google.android.gms@252234029@25.22.34 (190400-769260661):42)
E/GoogleApiManager(28595): 	at android.os.Handler.handleCallback(Handler.java:938)
E/GoogleApiManager(28595): 	at android.os.Handler.dispatchMessage(Handler.java:99)
E/GoogleApiManager(28595): 	at chad.mH(:com.google.android.gms@252234029@25.22.34 (190400-769260661):1)
E/GoogleApiManager(28595): 	at chad.dispatchMessage(:com.google.android.gms@252234029@25.22.34 (190400-769260661):5)
E/GoogleApiManager(28595): 	at android.os.Looper.loopOnce(Looper.java:233)
E/GoogleApiManager(28595): 	at android.os.Looper.loop(Looper.java:344)
E/GoogleApiManager(28595): 	at android.os.HandlerThread.run(HandlerThread.java:67)
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
D/InsetsController(28595): show(ime(), fromIme=true)
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] swipe from 0
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] Hit Gesture Region !
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] trigger!
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
I/flutter (28595): User session saved successfully
I/flutter (28595): Admin user signed in locally: أحمد محمد - المدير الأعلى
I/flutter (28595): Subscribed to notification topics for user: أحمد محمد - المدير الأعلى
W/Firestore(28595): (25.1.3) [WriteStream]: (fff7cac) Stream closed with status: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}.
W/Firestore(28595): (25.1.3) [Firestore]: Write failed at users/admin_001: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/flutter (28595): Error updating FCM token: [cloud_firestore/permission-denied] The caller does not have permission to execute the specified operation.
I/flutter (28595): User session setup completed for: أحمد محمد - المدير الأعلى

======== Exception caught by rendering library =====================================================
The following assertion was thrown during layout:
A RenderFlex overflowed by 214 pixels on the right.

The relevant error-causing widget was: 
  Row Row:file:///C:/Users/<USER>/Documents/augment-projects/el_farhan_app/lib/screens/home/<USER>
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/permissions_handler(28595): No permissions found in manifest for: []9
I/flutter (28595): Camera permission: true
I/flutter (28595): Storage/Photos permission: true
I/CameraManagerExtImpl(28595): getInstance success!
I/CameraManagerExtImpl(28595): packagename is com.alfarhan.transport
I/OplusCameraManager(28595): saveOpPackageName, mOpPackageName: com.alfarhan.transport
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraManagerGlobal(28595): Connecting to camera service
I/CameraManagerGlobal(28595): Connecting to camera service
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
D/OplusCameraUtils(28595): new OplusCameraUtils!
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
I/flutter (28595): [IMPORTANT:flutter/shell/platform/android/platform_view_android.cc(334)] Flutter recommends migrating plugins that create and register surface textures to the new surface producer API. See https://docs.flutter.dev/release/breaking-changes/android-surface-plugins
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
D/InsetsController(28595): show(ime(), fromIme=true)
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/CameraDeviceImplExtImpl(28595): getInstance success!
W/libc    (28595): Access denied finding property "persist.vendor.camera.privapp.list"
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraUtils(28595): current activityName: com.alfarhan.transport.MainActivity
I/OplusCameraUtils(28595): getComponentName, componentName: com.alfarhan.transport/com.alfarhan.transport.MainActivity, packageName:com.alfarhan.transport, activityName:com.alfarhan.transport.MainActivity
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/Camera  (28595): startPreview
D/OplusCamera2StatisticsManager(28595): addInfo, eventMap: {halLevel=3, cameraId=0, pkgName=com.alfarhan.transport, connentTime=1750804906086, apLevel=2}
I/Camera  (28595): CameraCaptureSession onConfigured
I/Camera  (28595): Updating builder settings
D/Camera  (28595): Updating builder with feature: ExposureLockFeature
D/Camera  (28595): Updating builder with feature: ExposurePointFeature
D/Camera  (28595): Updating builder with feature: ZoomLevelFeature
D/Camera  (28595): Updating builder with feature: AutoFocusFeature
D/Camera  (28595): Updating builder with feature: NoiseReductionFeature
I/Camera  (28595): updateNoiseReduction | currentSetting: fast
D/Camera  (28595): Updating builder with feature: FocusPointFeature
D/Camera  (28595): Updating builder with feature: ResolutionFeature
D/Camera  (28595): Updating builder with feature: SensorOrientationFeature
D/Camera  (28595): Updating builder with feature: FlashFeature
D/Camera  (28595): Updating builder with feature: ExposureOffsetFeature
D/Camera  (28595): Updating builder with feature: FpsRangeFeature
I/Camera  (28595): refreshPreviewCaptureSession
I/arhan.transpor(28595): NativeAlloc concurrent copying GC freed 3991KB AllocSpace bytes, 18(680KB) LOS objects, 49% free, 6619KB/12MB, paused 266us,57us total 118.925ms
The overflowing RenderFlex has an orientation of Axis.horizontal.
The edge of the RenderFlex that is overflowing has been marked in the rendering with a yellow and black striped pattern. This is usually caused by the contents being too big for the RenderFlex.

Consider applying a flex factor (e.g. using an Expanded widget) to force the children of the RenderFlex to fit within the available space instead of being sized to their natural size.
This is considered an error condition because it indicates that there is content that cannot be seen. If the content is legitimately bigger than the available space, consider clipping it with a ClipRect widget before putting it in the flex, or using a scrollable container rather than a Flex, like a ListView.

The specific RenderFlex in question is: RenderFlex#5cdb3 relayoutBoundary=up14 OVERFLOWING
...  parentData: offset=Offset(0.0, 0.0) (can use size)
...  constraints: BoxConstraints(0.0<=w<=8.3, 0.0<=h<=Infinity)
...  size: Size(8.3, 32.0)
...  direction: horizontal
...  mainAxisAlignment: start
...  mainAxisSize: max
...  crossAxisAlignment: center
...  textDirection: ltr
...  verticalDirection: down
...  spacing: 0.0
◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤
====================================================================================================
I/Camera  (28595): runPictureAutoFocus
I/Camera  (28595): lockAutoFocus
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 4 | aeState: 4
I/Camera  (28595): runPrecaptureSequence
I/Camera  (28595): refreshPreviewCaptureSession
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_PRECAPTURE_START | afState: 2 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_PRECAPTURE_DONE | afState: 2 | aeState: 4
I/Camera  (28595): captureStillPicture
D/Camera  (28595): Updating builder with feature: ExposureLockFeature
D/Camera  (28595): Updating builder with feature: ExposurePointFeature
D/Camera  (28595): Updating builder with feature: ZoomLevelFeature
D/Camera  (28595): Updating builder with feature: AutoFocusFeature
D/Camera  (28595): Updating builder with feature: NoiseReductionFeature
I/Camera  (28595): updateNoiseReduction | currentSetting: fast
D/Camera  (28595): Updating builder with feature: FocusPointFeature
D/Camera  (28595): Updating builder with feature: ResolutionFeature
D/Camera  (28595): Updating builder with feature: SensorOrientationFeature
D/Camera  (28595): Updating builder with feature: FlashFeature
D/Camera  (28595): Updating builder with feature: ExposureOffsetFeature
D/Camera  (28595): Updating builder with feature: FpsRangeFeature
I/Camera  (28595): sending capture request
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 5
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 5
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 5
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 5
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 5
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 5
D/OplusCamera2StatisticsManager(28595): addCaptureInfo, eventMap: {exp_value=50000000, halLevel=3, iso_value=6400, flash_trigger=0, face_count=0, cameraId=0, rear_front=rear, touchxy_value=1734,1302, pkgName=com.alfarhan.transport, zoom_value=0.0, apLevel=2}
I/Camera  (28595): onImageAvailable
I/Camera  (28595): unlockAutoFocus
I/Camera  (28595): refreshPreviewCaptureSession
D/qdgralloc(28595): GetYUVPlaneInfo: Invalid format passed: 0x21
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
E/flutter (28595): [ERROR:flutter/shell/platform/android/surface_texture_external_texture_vk_impeller.cc(122)] Break on 'ImpellerValidationBreak' to inspect point of failure: Invalid external texture.
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
I/Camera  (28595): dispose
I/Camera  (28595): close
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/Camera  (28595): open | onClosed
D/OplusCamera2StatisticsManager(28595): addPreviewInfo, eventMap: {halLevel=3, preview_time=8543, face_count=0, cameraId=0, touchxy_value=1734,1302, pkgName=com.alfarhan.transport, apLevel=2}
D/OplusCamera2StatisticsManager(28595): addInfo, eventMap: {halLevel=3, cameraId=0, disconnectTime=1750804914629, pkgName=com.alfarhan.transport, connentTime=1750804906086, apLevel=2, timeCost=8543}
D/InsetsController(28595): show(ime(), fromIme=true)
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
E/ActivityThread(28595): Failed to find provider info for com.oplus.statistics.provider
E/OplusStatistics--(28595): IllegalArgumentException:java.lang.IllegalArgumentException: Unknown URL content://com.oplus.statistics.provider/track_event
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
E/BLASTBufferQueue(28595): BLASTBufferItemConsumer::onDisconnect()
E/BLASTBufferQueue(28595): BLASTBufferItemConsumer::onDisconnect()
I/flutter (28595): 📱 App paused at 2025-06-25 01:42:00.047647
I/flutter (28595): ! Timer not found for: app_session
E/BLASTBufferQueue(28595): BLASTBufferItemConsumer::onDisconnect()
D/SurfaceComposerClient(28595): VRR [FRTC] client handle [bufferId:18446744073709551615 framenumber:0] [ffffffff, ffffffff]
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/Quality (28595): Skipped: false 23 cost 377.93716 refreshRate 0 processName com.alfarhan.transport
D/SurfaceComposerClient(28595): VRR [FRTC] client handle [bufferId:18446744073709551615 framenumber:0] [ffffffff, ffffffff]
I/flutter (28595): 📱 App resumed at 2025-06-25 01:42:01.979779
I/flutter (28595): ⏱️ Started timer for: app_session
I/Quality (28595): Skipped: true 1 cost 26.536413 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 16.554268 refreshRate 0 processName com.alfarhan.transport
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F....ID 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/permissions_handler(28595): No permissions found in manifest for: []9
I/flutter (28595): Camera permission: true
I/flutter (28595): Storage/Photos permission: true
I/Quality (28595): Skipped: false 1 cost 31.8043 refreshRate 0 processName com.alfarhan.transport
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/Camera  (28595): close
I/flutter (28595): [IMPORTANT:flutter/shell/platform/android/platform_view_android.cc(334)] Flutter recommends migrating plugins that create and register surface textures to the new surface producer API. See https://docs.flutter.dev/release/breaking-changes/android-surface-plugins
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
W/libc    (28595): Access denied finding property "persist.vendor.camera.privapp.list"
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraUtils(28595): current activityName: com.alfarhan.transport.MainActivity
I/OplusCameraUtils(28595): getComponentName, componentName: com.alfarhan.transport/com.alfarhan.transport.MainActivity, packageName:com.alfarhan.transport, activityName:com.alfarhan.transport.MainActivity
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/Camera  (28595): startPreview
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/OplusCamera2StatisticsManager(28595): addInfo, eventMap: {halLevel=3, cameraId=0, pkgName=com.alfarhan.transport, connentTime=1750804925155, apLevel=2}
I/Camera  (28595): CameraCaptureSession onConfigured
I/Camera  (28595): Updating builder settings
D/Camera  (28595): Updating builder with feature: ExposureLockFeature
D/Camera  (28595): Updating builder with feature: ExposurePointFeature
D/Camera  (28595): Updating builder with feature: ZoomLevelFeature
D/Camera  (28595): Updating builder with feature: AutoFocusFeature
D/Camera  (28595): Updating builder with feature: NoiseReductionFeature
I/Camera  (28595): updateNoiseReduction | currentSetting: fast
D/Camera  (28595): Updating builder with feature: FocusPointFeature
D/Camera  (28595): Updating builder with feature: ResolutionFeature
D/Camera  (28595): Updating builder with feature: SensorOrientationFeature
D/Camera  (28595): Updating builder with feature: FlashFeature
D/Camera  (28595): Updating builder with feature: ExposureOffsetFeature
D/Camera  (28595): Updating builder with feature: FpsRangeFeature
I/Camera  (28595): refreshPreviewCaptureSession
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
W/System  (28595): A resource failed to call release. 
I/Camera  (28595): CameraCaptureSession onClosed
E/ActivityThread(28595): Failed to find provider info for com.oplus.statistics.provider
E/OplusStatistics--(28595): IllegalArgumentException:java.lang.IllegalArgumentException: Unknown URL content://com.oplus.statistics.provider/track_event
I/Camera  (28595): runPictureAutoFocus
I/Camera  (28595): lockAutoFocus
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 4 | aeState: 2
I/Camera  (28595): captureStillPicture
D/Camera  (28595): Updating builder with feature: ExposureLockFeature
D/Camera  (28595): Updating builder with feature: ExposurePointFeature
D/Camera  (28595): Updating builder with feature: ZoomLevelFeature
D/Camera  (28595): Updating builder with feature: AutoFocusFeature
D/Camera  (28595): Updating builder with feature: NoiseReductionFeature
I/Camera  (28595): updateNoiseReduction | currentSetting: fast
D/Camera  (28595): Updating builder with feature: FocusPointFeature
D/Camera  (28595): Updating builder with feature: ResolutionFeature
D/Camera  (28595): Updating builder with feature: SensorOrientationFeature
D/Camera  (28595): Updating builder with feature: FlashFeature
D/Camera  (28595): Updating builder with feature: ExposureOffsetFeature
D/Camera  (28595): Updating builder with feature: FpsRangeFeature
I/Camera  (28595): sending capture request
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/OplusCamera2StatisticsManager(28595): addCaptureInfo, eventMap: {exp_value=40000000, halLevel=3, iso_value=4367, flash_trigger=0, face_count=0, cameraId=0, rear_front=rear, touchxy_value=1928,2278, pkgName=com.alfarhan.transport, zoom_value=0.0, apLevel=2}
I/Camera  (28595): onImageAvailable
I/Camera  (28595): unlockAutoFocus
I/Camera  (28595): refreshPreviewCaptureSession
D/qdgralloc(28595): GetYUVPlaneInfo: Invalid format passed: 0x21
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
E/flutter (28595): [ERROR:flutter/shell/platform/android/surface_texture_external_texture_vk_impeller.cc(122)] Break on 'ImpellerValidationBreak' to inspect point of failure: Invalid external texture.
I/arhan.transpor(28595): NativeAlloc concurrent copying GC freed 586KB AllocSpace bytes, 1(104KB) LOS objects, 49% free, 6727KB/13MB, paused 68us,81us total 284.293ms
I/Camera  (28595): dispose
I/Camera  (28595): close
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/Camera  (28595): open | onClosed
D/OplusCamera2StatisticsManager(28595): addPreviewInfo, eventMap: {halLevel=3, preview_time=11506, face_count=0, cameraId=0, touchxy_value=1936,2276, pkgName=com.alfarhan.transport, apLevel=2}
D/OplusCamera2StatisticsManager(28595): addInfo, eventMap: {halLevel=3, cameraId=0, disconnectTime=1750804936661, pkgName=com.alfarhan.transport, connentTime=1750804925155, apLevel=2, timeCost=11506}
D/InsetsController(28595): show(ime(), fromIme=true)
I/Quality (28595): Skipped: false 1 cost 17.389517 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 2 cost 45.486855 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 1 cost 31.509989 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 1 cost 26.396906 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 2 cost 42.67864 refreshRate 0 processName com.alfarhan.transport
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
I/Quality (28595): Skipped: true 1 cost 28.464388 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 2 cost 33.19236 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 1 cost 22.29181 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 3 cost 56.86394 refreshRate 0 processName com.alfarhan.transport
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/permissions_handler(28595): No permissions found in manifest for: []9
I/flutter (28595): Camera permission: true
I/flutter (28595): Storage/Photos permission: true
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/Camera  (28595): close
I/flutter (28595): [IMPORTANT:flutter/shell/platform/android/platform_view_android.cc(334)] Flutter recommends migrating plugins that create and register surface textures to the new surface producer API. See https://docs.flutter.dev/release/breaking-changes/android-surface-plugins
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
W/libc    (28595): Access denied finding property "persist.vendor.camera.privapp.list"
I/OplusCameraManagerGlobal(28595): setClientInfo, packageName: com.alfarhan.transport, uid: 10998, pid: 28595
I/OplusCameraUtils(28595): current activityName: com.alfarhan.transport.MainActivity
I/OplusCameraUtils(28595): getComponentName, componentName: com.alfarhan.transport/com.alfarhan.transport.MainActivity, packageName:com.alfarhan.transport, activityName:com.alfarhan.transport.MainActivity
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
I/Camera  (28595): startPreview
D/InsetsController(28595): show(ime(), fromIme=true)
I/Quality (28595): Skipped: false 2 cost 43.57145 refreshRate 0 processName com.alfarhan.transport
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
I/Quality (28595): Skipped: true 1 cost 31.839096 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 1 cost 16.930424 refreshRate 0 processName com.alfarhan.transport
D/OplusCamera2StatisticsManager(28595): addInfo, eventMap: {halLevel=3, cameraId=0, pkgName=com.alfarhan.transport, connentTime=1750804939397, apLevel=2}
I/Camera  (28595): CameraCaptureSession onConfigured
I/Camera  (28595): Updating builder settings
D/Camera  (28595): Updating builder with feature: ExposureLockFeature
D/Camera  (28595): Updating builder with feature: ExposurePointFeature
D/Camera  (28595): Updating builder with feature: ZoomLevelFeature
D/Camera  (28595): Updating builder with feature: AutoFocusFeature
D/Camera  (28595): Updating builder with feature: NoiseReductionFeature
I/Camera  (28595): updateNoiseReduction | currentSetting: fast
D/Camera  (28595): Updating builder with feature: FocusPointFeature
D/Camera  (28595): Updating builder with feature: ResolutionFeature
D/Camera  (28595): Updating builder with feature: SensorOrientationFeature
D/Camera  (28595): Updating builder with feature: FlashFeature
D/Camera  (28595): Updating builder with feature: ExposureOffsetFeature
D/Camera  (28595): Updating builder with feature: FpsRangeFeature
I/Camera  (28595): refreshPreviewCaptureSession
I/Quality (28595): Skipped: true 1 cost 16.447016 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 2 cost 38.01037 refreshRate 0 processName com.alfarhan.transport
I/Camera  (28595): runPictureAutoFocus
I/Camera  (28595): lockAutoFocus
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_WAITING_FOCUS | afState: 4 | aeState: 2
I/Camera  (28595): captureStillPicture
D/Camera  (28595): Updating builder with feature: ExposureLockFeature
D/Camera  (28595): Updating builder with feature: ExposurePointFeature
D/Camera  (28595): Updating builder with feature: ZoomLevelFeature
D/Camera  (28595): Updating builder with feature: AutoFocusFeature
D/Camera  (28595): Updating builder with feature: NoiseReductionFeature
I/Camera  (28595): updateNoiseReduction | currentSetting: fast
D/Camera  (28595): Updating builder with feature: FocusPointFeature
D/Camera  (28595): Updating builder with feature: ResolutionFeature
D/Camera  (28595): Updating builder with feature: SensorOrientationFeature
D/Camera  (28595): Updating builder with feature: FlashFeature
D/Camera  (28595): Updating builder with feature: ExposureOffsetFeature
D/Camera  (28595): Updating builder with feature: FpsRangeFeature
I/Camera  (28595): sending capture request
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 2 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 2
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/CameraCaptureCallback(28595): CameraCaptureCallback | state: STATE_CAPTURING | afState: 4 | aeState: 4
D/OplusCamera2StatisticsManager(28595): addCaptureInfo, eventMap: {exp_value=40000000, halLevel=3, iso_value=3499, flash_trigger=0, face_count=0, cameraId=0, rear_front=rear, touchxy_value=1734,1302, pkgName=com.alfarhan.transport, zoom_value=0.0, apLevel=2}
I/Camera  (28595): onImageAvailable
I/Camera  (28595): unlockAutoFocus
I/Camera  (28595): refreshPreviewCaptureSession
D/qdgralloc(28595): GetYUVPlaneInfo: Invalid format passed: 0x21
D/DecoupledTextDelegate(28595): Start loading thick OCR module.
I/DynamiteModule(28595): Considering local module com.google.mlkit.dynamite.text.latin:10000 and remote module com.google.mlkit.dynamite.text.latin:0
I/DynamiteModule(28595): Selected local version of com.google.mlkit.dynamite.text.latin
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
W/arhan.transpor(28595): Verification of void com.google.android.gms.internal.mlkit_vision_text_common.zzmq.configure(com.google.firebase.encoders.config.EncoderConfig) took 124.120ms (11287.38 bytecodes/s) (0B arena alloc)
E/flutter (28595): [ERROR:flutter/shell/platform/android/surface_texture_external_texture_vk_impeller.cc(122)] Break on 'ImpellerValidationBreak' to inspect point of failure: Invalid external texture.
D/nativeloader(28595): Load /data/app/~~hxAQ7f80b-ixHhJMn82viQ==/com.alfarhan.transport-Ux4kwK-CjPhDUZvbo2WHWg==/base.apk!/lib/arm64-v8a/libmlkit_google_ocr_pipeline.so using class loader ns clns-4 (caller=/data/app/~~hxAQ7f80b-ixHhJMn82viQ==/com.alfarhan.transport-Ux4kwK-CjPhDUZvbo2WHWg==/base.apk!classes7.dex): ok
I/native  (28595): I0000 00:00:1750804943.433441   29809 asset_manager_util.cc:61] Created global reference to asset manager.
D/InsetsController(28595): show(ime(), fromIme=true)
I/Camera  (28595): dispose
I/Camera  (28595): close
I/Manager (28595): DeviceManager::DeviceManager
I/Manager (28595): findAvailableDevices
I/Manager (28595): Found interface qti-default (version = 1.3-11:build_atoll)
I/Manager (28595): Found interface qti-dsp (version = 1.3-11:build_atoll)
I/Manager (28595): Found interface qti-gpu (version = 1.3-11:build_atoll)
I/Manager (28595): Found interface qti-hta (version = 1.3-11:build_atoll)
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
W/libc    (28595): Access denied finding property "ro.hardware.chipname"
I/native  (28595): I0000 00:00:1750804943.641359   29814 resource_manager.cc:23] Number of optimal threads: 2
I/native  (28595): I0000 00:00:1750804943.643066   29813 text_classifier.cc:32] Creating classifier TfliteTextClassifier
I/native  (28595): I0000 00:00:1750804943.643253   29814 text_detector_thread_pool_context.cc:38] Compute manager max in flight region detector overwrite: 1
I/native  (28595): I0000 00:00:1750804943.643314   29813 common_util.h:41] Resizing Thread Pool: ocr_segm to 3
I/native  (28595): I0000 00:00:1750804943.643457   29814 common_util.h:41] Resizing Thread Pool: ocr_det_0 to 3
I/native  (28595): I0000 00:00:1750804943.646509   29813 tflite_lstm_client_base.cc:371] Resizing interpreter pool to 4
W/libc    (28595): Access denied finding property "vendor.camera.aux.packagelist"
I/Camera  (28595): open | onClosed
D/OplusCamera2StatisticsManager(28595): addPreviewInfo, eventMap: {halLevel=3, preview_time=4254, face_count=0, cameraId=0, touchxy_value=1734,1302, pkgName=com.alfarhan.transport, apLevel=2}
D/OplusCamera2StatisticsManager(28595): addInfo, eventMap: {halLevel=3, cameraId=0, disconnectTime=1750804943651, pkgName=com.alfarhan.transport, connentTime=1750804939397, apLevel=2, timeCost=4254}
I/Quality (28595): Skipped: false 9 cost 153.03229 refreshRate 0 processName com.alfarhan.transport
I/native  (28595): I0000 00:00:1750804943.656171   29814 tflite_detector_client_with_shape_cache.cc:76] Interpreter threads: 2
I/tflite  (28595): Initialized TensorFlow Lite runtime.
I/tflite  (28595): Created TensorFlow Lite XNNPACK delegate for CPU.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/native  (28595): I0000 00:00:1750804943.676794   29813 multi_pass_line_recognition_mutator.cc:342] Preloading recognizers.
I/native  (28595): I0000 00:00:1750804943.678449   29814 tflite_detector_client_with_shape_cache.cc:105] Caching size: 10
I/native  (28595): I0000 00:00:1750804943.679705   29822 tflite_model_pooled_runner.cc:625] Loading mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb
I/native  (28595): I0000 00:00:1750804943.680198   29814 tflite_model_pooled_runner.cc:625] Loading mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite
I/native  (28595): I0000 00:00:1750804943.681655   29822 tflite_model_pooled_runner.cc:636] Loading mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb
I/native  (28595): I0000 00:00:1750804943.682489   29814 tflite_model_pooled_runner.cc:836] Resizing interpreter pool to 1
I/native  (28595): I0000 00:00:1750804943.683191   29814 tflite_model_pooled_runner.cc:625] Loading mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite
I/native  (28595): I0000 00:00:1750804943.685188   29814 tflite_model_pooled_runner.cc:836] Resizing interpreter pool to 1
I/native  (28595): I0000 00:00:1750804943.688042   29822 tflite_model_pooled_runner.cc:836] Resizing interpreter pool to 4
I/native  (28595): I0000 00:00:1750804943.691774   29822 mobile_langid_v2.cc:58] MobileLangID V2 initialized.
I/native  (28595): I0000 00:00:1750804943.691903   29822 multi_pass_line_recognition_mutator.cc:364] Finished preloading a recognizer for "Latn"
I/native  (28595): I0000 00:00:1750804943.691981   29813 multi_pass_line_recognition_mutator.cc:398] Finished preloading recognizers.
D/PipelineManager(28595): Start process bitmap
I/native  (28595): I0000 00:00:1750804943.695989   29809 scheduler.cc:693] ImageMetadata: 480x720
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
D/PipelineManager(28595): OCR process succeeded via visionkit pipeline.
I/flutter (28595): Extracted text: 1445 KN2095692
D/DecoupledTextDelegate(28595): Start loading thick OCR module.
I/DynamiteModule(28595): Considering local module com.google.mlkit.dynamite.text.latin:10000 and remote module com.google.mlkit.dynamite.text.latin:0
I/DynamiteModule(28595): Selected local version of com.google.mlkit.dynamite.text.latin
I/native  (28595): I0000 00:00:1750804943.914910   29832 asset_manager_util.cc:61] Created global reference to asset manager.
I/native  (28595): I0000 00:00:1750804943.927150   29835 resource_manager.cc:23] Number of optimal threads: 2
I/native  (28595): I0000 00:00:1750804943.927668   29835 text_detector_thread_pool_context.cc:38] Compute manager max in flight region detector overwrite: 1
I/native  (28595): I0000 00:00:1750804943.928087   29835 common_util.h:41] Resizing Thread Pool: ocr_det_0 to 3
I/native  (28595): I0000 00:00:1750804943.929386   29835 tflite_detector_client_with_shape_cache.cc:76] Interpreter threads: 2
I/native  (28595): I0000 00:00:1750804943.931366   29834 text_classifier.cc:32] Creating classifier TfliteTextClassifier
I/native  (28595): I0000 00:00:1750804943.931783   29834 common_util.h:41] Resizing Thread Pool: ocr_segm to 3
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/native  (28595): I0000 00:00:1750804943.936890   29834 tflite_lstm_client_base.cc:371] Resizing interpreter pool to 4
I/native  (28595): I0000 00:00:1750804943.937875   29835 tflite_detector_client_with_shape_cache.cc:105] Caching size: 10
I/native  (28595): I0000 00:00:1750804943.938957   29835 tflite_model_pooled_runner.cc:625] Loading mlkit-google-ocr-models/gocr/layout/line_splitting_custom_ops/model.tflite
I/native  (28595): I0000 00:00:1750804943.938937   29834 multi_pass_line_recognition_mutator.cc:342] Preloading recognizers.
I/native  (28595): I0000 00:00:1750804943.939545   29835 tflite_model_pooled_runner.cc:836] Resizing interpreter pool to 1
I/native  (28595): I0000 00:00:1750804943.940218   29835 tflite_model_pooled_runner.cc:625] Loading mlkit-google-ocr-models/gocr/layout/line_clustering_custom_ops/model.tflite
I/native  (28595): I0000 00:00:1750804943.940724   29835 tflite_model_pooled_runner.cc:836] Resizing interpreter pool to 1
I/native  (28595): I0000 00:00:1750804943.941339   29829 tflite_model_pooled_runner.cc:625] Loading mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/conv_model.fb
I/native  (28595): I0000 00:00:1750804943.941997   29829 tflite_model_pooled_runner.cc:636] Loading mlkit-google-ocr-models/gocr/gocr_models/line_recognition_legacy_mobile/Latn_ctc/optical/lstm_model.fb
I/native  (28595): I0000 00:00:1750804943.945259   29829 tflite_model_pooled_runner.cc:836] Resizing interpreter pool to 4
I/native  (28595): I0000 00:00:1750804943.948279   29829 mobile_langid_v2.cc:58] MobileLangID V2 initialized.
I/native  (28595): I0000 00:00:1750804943.948397   29829 multi_pass_line_recognition_mutator.cc:364] Finished preloading a recognizer for "Latn"
I/native  (28595): I0000 00:00:1750804943.948495   29834 multi_pass_line_recognition_mutator.cc:398] Finished preloading recognizers.
D/PipelineManager(28595): Start process bitmap
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
I/tflite  (28595): Replacing 44 out of 46 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 3 partitions for the whole graph.
D/PipelineManager(28595): OCR process succeeded via visionkit pipeline.
I/flutter (28595): Extracted text: 4gllallal
I/flutter (28595): ID card data extracted: {}
E/ActivityThread(28595): Failed to find provider info for com.oplus.statistics.provider
E/OplusStatistics--(28595): IllegalArgumentException:java.lang.IllegalArgumentException: Unknown URL content://com.oplus.statistics.provider/track_event
I/Quality (28595): Skipped: false 3 cost 52.970985 refreshRate 0 processName com.alfarhan.transport
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
D/InsetsController(28595): show(ime(), fromIme=true)
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
I/Quality (28595): Skipped: false 3 cost 49.18801 refreshRate 0 processName com.alfarhan.transport
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
D/InsetsController(28595): show(ime(), fromIme=true)
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
D/InsetsController(28595): show(ime(), fromIme=true)
I/Quality (28595): Skipped: false 5 cost 93.81323 refreshRate 0 processName com.alfarhan.transport
I/flutter (28595): Image uploaded successfully: https://res.cloudinary.com/dzh4fpnnw/image/upload/v1750804956/customer_ids/wg6tyhlneoqzwnw0frm0.jpg
I/flutter (28595): Image uploaded successfully: https://res.cloudinary.com/dzh4fpnnw/image/upload/v1750804957/customer_ids/xgevltukjgimf4jg3oqa.jpg
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/flutter (28595): Document tracking created for item: item_002
E/SQLiteLog(28595): (1) table notifications has no column named data in "INSERT OR REPLACE INTO notifications (id, userId, title, message, type, data, isRead, createdAt, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
I/flutter (28595): Error inserting into notifications: DatabaseException(table notifications has no column named data (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO notifications (id, userId, title, message, type, data, isRead, createdAt, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)) sql 'INSERT OR REPLACE INTO notifications (id, userId, title, message, type, data, isRead, createdAt, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)' args [1750804960232_0232, admin_001, فاتورة جديدة, تم إنشاء فاتورة جديدة رقم INV-CUSTOMER-250625-0142..., new_invoice, {invoice_id: 1750804959801_9801, invoice_number: I..., 0, 2025-06-25T01:42:40.233048, 0]
I/flutter (28595): Failed to notify managers about new invoice: DatabaseException(table notifications has no column named data (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO notifications (id, userId, title, message, type, data, isRead, createdAt, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)) sql 'INSERT OR REPLACE INTO notifications (id, userId, title, message, type, data, isRead, createdAt, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)' args [1750804960232_0232, admin_001, فاتورة جديدة, تم إنشاء فاتورة جديدة رقم INV-CUSTOMER-250625-0142..., new_invoice, {invoice_id: 1750804959801_9801, invoice_number: I..., 0, 2025-06-25T01:42:40.233048, 0]
I/flutter (28595): Invoice created with ID: 1750804959801_9801
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/flutter (28595): Item updated: item_002
I/flutter (28595): Sending notification to role admin: فاتورة جديدة - تم إنشاء فاتورة جديدة رقم INV-CUSTOMER-250625-014239 بمبلغ 19000 ج.م بواسطة أحمد محمد - المدير الأعلى
I/flutter (28595): Sending notification to role super_admin: فاتورة جديدة - تم إنشاء فاتورة جديدة رقم INV-CUSTOMER-250625-014239 بمبلغ 19000 ج.م بواسطة أحمد محمد - المدير الأعلى
I/flutter (28595): Sending notification to role admin: تم بيع صنف - تم بيع ياماها YZF-R15 بمبلغ 19000 ج.م بواسطة أحمد محمد - المدير الأعلى
I/flutter (28595): Sending notification to role super_admin: تم بيع صنف - تم بيع ياماها YZF-R15 بمبلغ 19000 ج.م بواسطة أحمد محمد - المدير الأعلى
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
I/Quality (28595): Skipped: false 5 cost 82.95542 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 1 cost 18.807604 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 2 cost 34.78633 refreshRate 0 processName com.alfarhan.transport
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/Quality (28595): Skipped: false 1 cost 23.56502 refreshRate 0 processName com.alfarhan.transport
I/TRuntime.CctTransportBackend(28595): Making request to: https://firebaselogging.googleapis.com/v0cc/log/batch?format=json_proto3
I/TRuntime.CctTransportBackend(28595): Status Code: ٢٠٠
I/Quality (28595): Skipped: false 1 cost 21.030214 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 30.919476 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 2 cost 38.035786 refreshRate 0 processName com.alfarhan.transport
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/Quality (28595): Skipped: false 3 cost 57.26002 refreshRate 0 processName com.alfarhan.transport
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] swipe from 0
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] Hit Gesture Region !
I/Quality (28595): Skipped: false 2 cost 47.084732 refreshRate 0 processName com.alfarhan.transport
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796086516_6516_1750805004761, 1750796086516_6516, mo, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.761245, 2025-06-25T01:43:24.761246, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796086516_6516_1750805004761, 1750796086516_6516, mo, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.761245, 2025-06-25T01:43:24.761246, admin_001, 0]
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796246235_6235_1750805004770, 1750796246235_6235, mm, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.770053, 2025-06-25T01:43:24.770055, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796246235_6235_1750805004770, 1750796246235_6235, mm, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.770053, 2025-06-25T01:43:24.770055, admin_001, 0]
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796823174_3174_1750805004776, 1750796823174_3174, محمد, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.776219, 2025-06-25T01:43:24.776220, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796823174_3174_1750805004776, 1750796823174_3174, محمد, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.776219, 2025-06-25T01:43:24.776220, admin_001, 0]
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750797325820_5820_1750805004804, 1750797325820_5820, uu, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.804091, 2025-06-25T01:43:24.804092, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750797325820_5820_1750805004804, 1750797325820_5820, uu, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:43:24.804091, 2025-06-25T01:43:24.804092, admin_001, 0]
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/Quality (28595): Skipped: false 1 cost 31.664171 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 2 cost 35.738792 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 16.97191 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 24.645704 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 22.315128 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 16.634493 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 24.794516 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 31.171915 refreshRate 0 processName com.alfarhan.transport
D/InputMethodManager(28595): showSoftInput() view=io.flutter.embedding.android.FlutterView{51c3b5 VFE...... .F...... 0,0-1080,2400 #1 aid=**********} flags=0 reason=SHOW_SOFT_INPUT
I/AssistStructure(28595): Flattened final assist data: 516 bytes, containing 1 windows, 3 views
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring on
D/InsetsController(28595): show(ime(), fromIme=true)
I/Quality (28595): Skipped: true 1 cost 22.508648 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: true 1 cost 17.860067 refreshRate 0 processName com.alfarhan.transport
D/InputConnectionAdaptor(28595): The input method toggled cursor monitoring off
I/Quality (28595): Skipped: false 1 cost 23.399956 refreshRate 0 processName com.alfarhan.transport
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] swipe from 0
D/OplusSystemUINavigationGesture(28595): [GESTURE_BUTTON] Hit Gesture Region !
D/Activity(28595): dispatchKeyEvent to com.alfarhan.transport.MainActivity@32d22cd will call onBackPressed
I/Quality (28595): Skipped: false 1 cost 28.624037 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 29.383064 refreshRate 0 processName com.alfarhan.transport
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796086516_6516_1750805045801, 1750796086516_6516, mo, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.801271, 2025-06-25T01:44:05.801275, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796086516_6516_1750805045801, 1750796086516_6516, mo, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.801271, 2025-06-25T01:44:05.801275, admin_001, 0]
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796246235_6235_1750805045811, 1750796246235_6235, mm, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.811718, 2025-06-25T01:44:05.811722, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796246235_6235_1750805045811, 1750796246235_6235, mm, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.811718, 2025-06-25T01:44:05.811722, admin_001, 0]
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796823174_3174_1750805045848, 1750796823174_3174, محمد, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.848117, 2025-06-25T01:44:05.848119, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750796823174_3174_1750805045848, 1750796823174_3174, محمد, **********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.848117, 2025-06-25T01:44:05.848119, admin_001, 0]
E/SQLiteLog(28595): (1) table agent_accounts has no column named agentPhone in "INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, crea
I/flutter (28595): Error inserting into agent_accounts: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750797325820_5820_1750805045856, 1750797325820_5820, uu, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.856197, 2025-06-25T01:44:05.856199, admin_001, 0]
I/flutter (28595): Error creating/updating agent account: DatabaseException(table agent_accounts has no column named agentPhone (code 1 SQLITE_ERROR): , while compiling: INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)) sql 'INSERT OR REPLACE INTO agent_accounts (id, agentId, agentName, agentPhone, totalDebt, totalPaid, currentBalance, transactions, createdAt, updatedAt, createdBy, additionalData, syncStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, ?)' args [agent_1750797325820_5820_1750805045856, 1750797325820_5820, uu, ***********, 0.0, 0.0, 0.0, [], 2025-06-25T01:44:05.856197, 2025-06-25T01:44:05.856199, admin_001, 0]
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
I/Quality (28595): Skipped: false 2 cost 37.96405 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 1 cost 19.908428 refreshRate 0 processName com.alfarhan.transport
I/Quality (28595): Skipped: false 2 cost 36.177032 refreshRate 0 processName com.alfarhan.transport
W/Firestore(28595): (25.1.3) [Firestore]: Listen for Query(target=Query(_connectivity_test order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
