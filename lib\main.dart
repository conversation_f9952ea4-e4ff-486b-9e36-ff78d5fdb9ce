import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'services/local_database_service.dart';
import 'providers/auth_provider.dart';
import 'services/firebase_service.dart';
import 'services/image_service.dart';
import 'services/enhanced_notification_service.dart';
import 'services/enhanced_pdf_service.dart';
import 'services/data_service.dart';
import 'services/auto_sync_service.dart';

import 'screens/splash/splash_screen.dart';
import 'core/performance/performance_monitor.dart';
import 'core/routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize services
  await _initializeServices();

  // Initialize performance monitoring
  PerformanceMonitor.instance.setEnabled(kDebugMode);
  PerformanceMonitor.instance.monitorAppLifecycle();

  runApp(const ElFarhanApp());
}

Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🚀 Starting app initialization...');
    }

    // Initialize only critical services first
    await FirebaseService.instance.initialize();
    await LocalDatabaseService.instance.initialize();

    if (kDebugMode) {
      print('✅ Critical services initialized successfully');
      print('📱 App will use only Firebase data - no default data creation');
    }

    // Initialize other services in background after app starts
    _initializeBackgroundServices();

  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing critical services: $e');
    }
  }
}

// Initialize non-critical services in background
void _initializeBackgroundServices() {
  Future.microtask(() async {
    try {
      if (kDebugMode) {
        print('🔄 Initializing background services...');
      }

      // Initialize image service
      await ImageService.instance.initialize();

      // Initialize enhanced notification service
      await EnhancedNotificationService.instance.initialize();

      // Initialize enhanced PDF service
      await EnhancedPdfService.instance.initialize();

      // Initialize auto sync service
      await AutoSyncService.instance.initialize();

      if (kDebugMode) {
        print('✅ Background services initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error initializing background services: $e');
      }
    }
  });
}








class ElFarhanApp extends StatelessWidget {
  const ElFarhanApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize navigation key for notifications
    final navigatorKey = GlobalKey<NavigatorState>();
    EnhancedNotificationService.navigatorKey = navigatorKey;

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            navigatorKey: navigatorKey,
            title: AppConstants.appName,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            home: const SplashScreen(),
            onGenerateRoute: AppRoutes.generateRoute,
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}


