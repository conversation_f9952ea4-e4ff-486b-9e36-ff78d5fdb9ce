org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Kotlin daemon settings
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G
kotlin.incremental=true
kotlin.caching.enabled=true

# Gradle daemon settings
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
