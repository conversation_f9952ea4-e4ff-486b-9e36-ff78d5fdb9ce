import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../services/data_service.dart';
import '../../core/utils/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/warehouse_model.dart';
import '../../models/user_model.dart';

class WarehouseManagementScreen extends StatefulWidget {
  const WarehouseManagementScreen({super.key});

  @override
  State<WarehouseManagementScreen> createState() => _WarehouseManagementScreenState();
}

class _WarehouseManagementScreenState extends State<WarehouseManagementScreen> {
  List<WarehouseModel> _warehouses = [];
  List<UserModel> _agents = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Delay loading data until after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dataService = Provider.of<DataService>(context, listen: false);
      
      // Force refresh from Firebase to get latest data
      final results = await Future.wait([
        dataService.getWarehouses(forceFromFirebase: true),
        dataService.getUsers(forceFromFirebase: true),
      ]);

      if (kDebugMode) {
        print('📦 Loaded ${(results[0] as List<WarehouseModel>).length} warehouses from Firebase');
        print('👥 Loaded ${(results[1] as List<UserModel>).length} users from Firebase');
      }

      setState(() {
        _warehouses = results[0] as List<WarehouseModel>;
        _agents = (results[1] as List<UserModel>).where((user) => user.role == 'agent').toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (kDebugMode) {
        print('❌ Error loading warehouse data: $e');
      }
      // Show error message after the widget is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
        }
      });
    }
  }

  List<WarehouseModel> get _filteredWarehouses {
    if (_searchQuery.isEmpty) {
      return _warehouses;
    }
    return _warehouses.where((warehouse) {
      return warehouse.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             warehouse.address.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المخازن'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'البحث في المخازن...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // Statistics cards
          if (!_isLoading) _buildStatisticsCards(),
          
          // Warehouses list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredWarehouses.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.warehouse_outlined,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty ? 'لا توجد مخازن' : 'لا توجد نتائج للبحث',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredWarehouses.length,
                          itemBuilder: (context, index) {
                            final warehouse = _filteredWarehouses[index];
                            return _buildWarehouseCard(warehouse);
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddWarehouseDialog,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildStatisticsCards() {
    final totalWarehouses = _warehouses.length;
    final activeWarehouses = _warehouses.where((w) => w.isActive).length;
    final assignedWarehouses = _warehouses.where((w) => w.ownerId != null).length;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المخازن',
              totalWarehouses.toString(),
              Icons.warehouse,
              AppColors.primary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'المخازن النشطة',
              activeWarehouses.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'المخازن المُعيَّنة',
              assignedWarehouses.toString(),
              Icons.person,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseCard(WarehouseModel warehouse) {
    final assignedAgent = _agents.firstWhere(
      (agent) => agent.id == warehouse.ownerId,
      orElse: () => UserModel(
        id: '',
        username: '',
        fullName: 'غير مُعيَّن',
        email: '',
        phone: '',
        role: '',
        isActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        warehouse.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        warehouse.address,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(warehouse.isActive),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Details
            _buildDetailRow('المدير المُعيَّن:', assignedAgent.fullName),
            _buildDetailRow('تاريخ الإنشاء:', AppUtils.formatDate(warehouse.createdAt)),
            _buildDetailRow('آخر تحديث:', AppUtils.formatDate(warehouse.updatedAt)),
            
            const SizedBox(height: 16),
            
            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showAssignManagerDialog(warehouse),
                  icon: const Icon(Icons.person_add),
                  label: const Text('تعيين مدير'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showEditWarehouseDialog(warehouse),
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _toggleWarehouseStatus(warehouse),
                  icon: Icon(warehouse.isActive ? Icons.pause : Icons.play_arrow),
                  label: Text(warehouse.isActive ? 'إيقاف' : 'تفعيل'),
                  style: TextButton.styleFrom(
                    foregroundColor: warehouse.isActive ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(bool isActive) {
    return Chip(
      label: Text(
        isActive ? 'نشط' : 'متوقف',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: isActive ? Colors.green : Colors.red,
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  void _showAddWarehouseDialog() {
    final nameController = TextEditingController();
    final addressController = TextEditingController();
    final phoneController = TextEditingController();
    final emailController = TextEditingController();
    String selectedType = WarehouseModel.typeMain;
    String? selectedAgentId;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إضافة مخزن جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // نوع المخزن
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المخزن',
                    border: OutlineInputBorder(),
                  ),
                  items: WarehouseModel.warehouseTypes.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(WarehouseModel.typeNames[type] ?? type),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedType = value!;
                      selectedAgentId = null; // Reset agent selection
                    });
                  },
                ),
                const SizedBox(height: 16),

                // اسم المخزن
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المخزن',
                    hintText: 'أدخل اسم المخزن',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // العنوان
                TextFormField(
                  controller: addressController,
                  decoration: const InputDecoration(
                    labelText: 'العنوان',
                    hintText: 'أدخل عنوان المخزن',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // رقم الهاتف
                TextFormField(
                  controller: phoneController,
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف (اختياري)',
                    hintText: 'أدخل رقم الهاتف',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),

                // البريد الإلكتروني
                TextFormField(
                  controller: emailController,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني (اختياري)',
                    hintText: 'أدخل البريد الإلكتروني',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),

                // اختيار الوكيل (للمخازن من نوع agent فقط)
                if (selectedType == WarehouseModel.typeAgent) ...[
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: selectedAgentId,
                    decoration: const InputDecoration(
                      labelText: 'الوكيل المسؤول',
                      border: OutlineInputBorder(),
                    ),
                    hint: const Text('اختر الوكيل'),
                    items: _agents.map((agent) {
                      return DropdownMenuItem(
                        value: agent.id,
                        child: Text(agent.fullName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedAgentId = value;
                      });
                    },
                    validator: (value) {
                      if (selectedType == WarehouseModel.typeAgent && value == null) {
                        return 'يجب اختيار الوكيل المسؤول';
                      }
                      return null;
                    },
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (nameController.text.trim().isNotEmpty &&
                    addressController.text.trim().isNotEmpty &&
                    (selectedType != WarehouseModel.typeAgent || selectedAgentId != null)) {
                  Navigator.pop(context);
                  _addWarehouse(
                    nameController.text.trim(),
                    addressController.text.trim(),
                    selectedType,
                    phoneController.text.trim().isEmpty ? null : phoneController.text.trim(),
                    emailController.text.trim().isEmpty ? null : emailController.text.trim(),
                    selectedAgentId,
                  );
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditWarehouseDialog(WarehouseModel warehouse) {
    final nameController = TextEditingController(text: warehouse.name);
    final addressController = TextEditingController(text: warehouse.address);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل المخزن'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المخزن',
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: addressController,
              decoration: const InputDecoration(
                labelText: 'العنوان',
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty &&
                  addressController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                _updateWarehouse(warehouse, nameController.text.trim(), addressController.text.trim());
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showAssignManagerDialog(WarehouseModel warehouse) {
    String? selectedAgentId = warehouse.ownerId;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعيين مدير المخزن'),
        content: StatefulBuilder(
          builder: (context, setState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: selectedAgentId,
                decoration: const InputDecoration(
                  labelText: 'اختر المدير',
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('بدون مدير'),
                  ),
                  ..._agents.map((agent) {
                    return DropdownMenuItem(
                      value: agent.id,
                      child: Text(agent.fullName),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    selectedAgentId = value;
                  });
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _assignManager(warehouse, selectedAgentId);
            },
            child: const Text('تعيين'),
          ),
        ],
      ),
    );
  }

  Future<void> _addWarehouse(
    String name,
    String address,
    String type,
    String? phone,
    String? email,
    String? ownerId,
  ) async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final warehouse = WarehouseModel(
        id: AppUtils.generateId(),
        name: name,
        type: type,
        address: address,
        phone: phone,
        email: email,
        ownerId: ownerId,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await dataService.createWarehouse(warehouse);
      await _loadData();
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إضافة المخزن بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إضافة المخزن: $e', isError: true);
      }
    }
  }

  Future<void> _updateWarehouse(WarehouseModel warehouse, String name, String address) async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      await dataService.updateWarehouse(warehouse.id, {
        'name': name,
        'address': address,
      });
      await _loadData();
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث المخزن بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحديث المخزن: $e', isError: true);
      }
    }
  }

  Future<void> _assignManager(WarehouseModel warehouse, String? managerId) async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      await dataService.updateWarehouseManager(warehouse.id, managerId ?? '');
      await _loadData();
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تعيين مدير المخزن بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تعيين المدير: $e', isError: true);
      }
    }
  }

  Future<void> _toggleWarehouseStatus(WarehouseModel warehouse) async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      await dataService.updateWarehouse(warehouse.id, {
        'isActive': !warehouse.isActive,
      });
      await _loadData();
      if (mounted) {
        AppUtils.showSnackBar(
          context, 
          warehouse.isActive ? 'تم إيقاف المخزن' : 'تم تفعيل المخزن'
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تغيير حالة المخزن: $e', isError: true);
      }
    }
  }
}
