import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:cloudinary_public/cloudinary_public.dart';
import 'package:image/image.dart' as img;
import 'package:permission_handler/permission_handler.dart';
import '../screens/camera/simple_camera_screen.dart';
import 'ocr_service.dart';

class ImageService {
  static ImageService? _instance;
  static ImageService get instance => _instance ??= ImageService._();
  
  ImageService._();

  final ImagePicker _picker = ImagePicker();
  late CloudinaryPublic _cloudinary;
  
  // Initialize the service
  Future<void> initialize() async {
    try {
      // Initialize Cloudinary (replace with your cloud name)
      _cloudinary = CloudinaryPublic('dzh4fpnnw', 'farhan', cache: false);
      
      if (kDebugMode) {
        print('Image service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing image service: $e');
      }
    }
  }

  // Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting camera permission: $e');
      }
      return false;
    }
  }

  // Take photo with camera
  Future<File?> takePhoto() async {
    try {
      // Check camera permission only (storage not needed for app directories in Android 13+)
      if (!await requestCameraPermission()) {
        throw 'إذن الكاميرا مطلوب لالتقاط الصور';
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error taking photo: $e');
      }
      rethrow;
    }
  }

  // Take photo with smart camera for motor fingerprint
  Future<File?> takeMotorFingerprintPhoto(BuildContext context) async {
    try {
      // Try smart camera first, fallback to regular camera
      File? capturedImage;

      // Show choice dialog
      final useSmartCamera = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تصوير بصمة الموتور'),
          content: const Text('اختر طريقة التصوير:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('الكاميرا العادية'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('الكاميرا مع الإطار المرئي'),
            ),
          ],
        ),
      );

      if (useSmartCamera == true && context.mounted) {
        try {
          await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => SimpleCameraScreen(
                title: 'تصوير بصمة الموتور',
                subtitle: 'التقط صورة واضحة لبصمة الموتور',
                onImageCaptured: (File image) {
                  capturedImage = image;
                },
              ),
            ),
          );

          if (capturedImage != null) {
            return capturedImage;
          }
        } catch (e) {
          if (kDebugMode) {
            print('Smart camera failed: $e');
          }
          // Show fallback message
          if (context.mounted) {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('فشل في الكاميرا الذكية'),
                content: const Text('سيتم استخدام الكاميرا العادية بدلاً من ذلك.'),
                actions: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('موافق'),
                  ),
                ],
              ),
            );
          }
        }
      }

      // Use regular camera (either chosen or fallback)
      return await takePhoto();

    } catch (e) {
      if (kDebugMode) {
        print('Error in takeMotorFingerprintPhoto: $e');
      }
      return await takePhoto();
    }
  }

  // Take photo with smart camera for ID card
  Future<File?> takeIdCardPhoto(BuildContext context, {required bool isFront}) async {
    try {
      // Try smart camera first, fallback to regular camera
      File? capturedImage;

      // Show choice dialog
      final useSmartCamera = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(isFront ? 'تصوير الوجه الأمامي' : 'تصوير الوجه الخلفي'),
          content: const Text('اختر طريقة التصوير:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('الكاميرا العادية'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('الكاميرا مع الإطار المرئي'),
            ),
          ],
        ),
      );

      if (useSmartCamera == true && context.mounted) {
        try {
          await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => SimpleCameraScreen(
                title: isFront ? 'تصوير الوجه الأمامي لبطاقة الهوية' : 'تصوير الوجه الخلفي لبطاقة الهوية',
                subtitle: isFront ? 'التقط صورة واضحة للوجه الأمامي لبطاقة الهوية' : 'التقط صورة واضحة للوجه الخلفي لبطاقة الهوية',
                onImageCaptured: (File image) {
                  capturedImage = image;
                },
              ),
            ),
          );

          if (capturedImage != null) {
            return capturedImage;
          }
        } catch (e) {
          if (kDebugMode) {
            print('Smart camera failed: $e');
          }
          // Show fallback message
          if (context.mounted) {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('فشل في الكاميرا الذكية'),
                content: const Text('سيتم استخدام الكاميرا العادية بدلاً من ذلك.'),
                actions: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('موافق'),
                  ),
                ],
              ),
            );
          }
        }
      }

      // Use regular camera (either chosen or fallback)
      return await takePhoto();

    } catch (e) {
      if (kDebugMode) {
        print('Error in takeIdCardPhoto: $e');
      }
      return await takePhoto();
    }
  }

  // Take photo with smart camera for chassis number
  Future<File?> takeChassisPhoto(BuildContext context) async {
    try {
      // Try smart camera first, fallback to regular camera
      File? capturedImage;

      // Show choice dialog
      final useSmartCamera = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تصوير رقم الشاسيه'),
          content: const Text('اختر طريقة التصوير:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('الكاميرا العادية'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('الكاميرا مع الإطار المرئي'),
            ),
          ],
        ),
      );

      if (useSmartCamera == true && context.mounted) {
        try {
          capturedImage = await Navigator.of(context).push<File>(
            MaterialPageRoute(
              builder: (context) => SimpleCameraScreen(
                title: 'تصوير رقم الشاسيه',
                subtitle: 'التقط صورة واضحة لرقم الشاسيه',
                onImageCaptured: (File image) {
                  Navigator.of(context).pop(image);
                },
              ),
            ),
          );
        } catch (e) {
          if (kDebugMode) {
            print('Smart camera failed for chassis, using regular camera: $e');
          }
        }
      }

      // If smart camera was successful, return the image
      if (capturedImage != null) {
        return capturedImage;
      }

      // Use regular camera (either chosen or fallback)
      return await takePhoto();

    } catch (e) {
      if (kDebugMode) {
        print('Error in takeChassisPhoto: $e');
      }
      return await takePhoto();
    }
  }

  // Pick image from gallery
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
      rethrow;
    }
  }

  // Compress image
  Future<File> compressImage(File imageFile, {int quality = 85}) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) throw 'فشل في قراءة الصورة';

      // Resize if too large
      img.Image resized = image;
      if (image.width > 1920 || image.height > 1080) {
        resized = img.copyResize(
          image,
          width: image.width > image.height ? 1920 : null,
          height: image.height > image.width ? 1080 : null,
        );
      }

      // Compress
      final compressedBytes = img.encodeJpg(resized, quality: quality);
      
      // Save compressed image
      final compressedFile = File('${imageFile.path}_compressed.jpg');
      await compressedFile.writeAsBytes(compressedBytes);
      
      return compressedFile;
    } catch (e) {
      if (kDebugMode) {
        print('Error compressing image: $e');
      }
      rethrow;
    }
  }

  // Upload image to Cloudinary
  Future<String> uploadImage(File imageFile, {String? folder}) async {
    try {
      // Compress image first
      final compressedImage = await compressImage(imageFile);
      
      final response = await _cloudinary.uploadFile(
        CloudinaryFile.fromFile(
          compressedImage.path,
          folder: folder,
          resourceType: CloudinaryResourceType.Image,
        ),
      );

      // Clean up compressed file
      if (await compressedImage.exists()) {
        await compressedImage.delete();
      }

      if (kDebugMode) {
        print('Image uploaded successfully: ${response.secureUrl}');
      }

      return response.secureUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      throw 'فشل في رفع الصورة';
    }
  }

  // Extract text from image using OCR
  Future<String> extractTextFromImage(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final textRecognizer = TextRecognizer();
      
      final RecognizedText recognizedText = await textRecognizer.processImage(inputImage);
      
      // Close the recognizer
      textRecognizer.close();
      
      // Extract and clean text
      String extractedText = recognizedText.text;
      
      // Clean up the text (remove extra spaces, newlines, etc.)
      extractedText = _cleanExtractedText(extractedText);
      
      if (kDebugMode) {
        print('Extracted text: $extractedText');
      }

      return extractedText;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting text from image: $e');
      }
      throw 'فشل في استخراج النص من الصورة';
    }
  }

  // Extract motor fingerprint with enhanced OCR
  Future<Map<String, String>> extractMotorFingerprintFromImage(File imageFile) async {
    try {
      // Use enhanced OCR service
      final ocrService = OCRService.instance;
      final ocrResult = await ocrService.extractTextFromImage(imageFile);

      if (kDebugMode) {
        print('OCR Result - Is Motor Fingerprint: ${ocrResult.isMotorFingerprint}');
        print('OCR Confidence: ${ocrResult.confidence}');
        print('Extracted Data: ${ocrResult.extractedData}');
      }

      // If OCR detected it as motor fingerprint, use the extracted data
      if (ocrResult.isMotorFingerprint && ocrResult.extractedData.isNotEmpty) {
        return ocrResult.extractedData;
      }

      // Fallback to old method if OCR didn't detect motor fingerprint
      String extractedText = ocrResult.text;
      if (extractedText.isEmpty) {
        extractedText = await extractTextFromImage(imageFile);
      }

      // Clean and return as simple text
      final cleanedText = _cleanMotorFingerprintText(extractedText);

      if (kDebugMode) {
        print('Fallback motor fingerprint text: $cleanedText');
      }

      return {
        'motorFingerprint': cleanedText,
        'confidence': '0.5', // Lower confidence for fallback
        'method': 'fallback'
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting motor fingerprint: $e');
      }
      rethrow;
    }
  }

  // Legacy method for backward compatibility
  Future<String> extractMotorFingerprintText(File imageFile) async {
    try {
      final result = await extractMotorFingerprintFromImage(imageFile);
      return result['motorFingerprint'] ?? '';
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting motor fingerprint text: $e');
      }
      rethrow;
    }
  }

  // Extract data from ID card using enhanced OCR
  Future<Map<String, String>> extractIdCardData(File frontImage, File backImage) async {
    try {
      // Use OCR service for better extraction
      final ocrService = OCRService.instance;

      // Extract from front image (main data)
      final frontResult = await ocrService.extractTextFromImage(frontImage);

      // Extract from back image (additional data)
      final backResult = await ocrService.extractTextFromImage(backImage);

      // Combine and process data
      Map<String, String> extractedData = {};

      // Process front image data (usually contains name, national ID, address)
      if (frontResult.isIdCard && frontResult.extractedData.isNotEmpty) {
        extractedData.addAll(frontResult.extractedData);
      }

      // Process back image data (additional info)
      if (backResult.isIdCard && backResult.extractedData.isNotEmpty) {
        // Merge back data, giving priority to front data
        for (final entry in backResult.extractedData.entries) {
          if (!extractedData.containsKey(entry.key) || extractedData[entry.key]!.isEmpty) {
            extractedData[entry.key] = entry.value;
          }
        }
      }

      // Fallback to old method if OCR didn't detect ID card
      if (extractedData.isEmpty) {
        extractedData = _parseIdCardData(frontResult.text, backResult.text);
      }

      // Map to expected field names for the form
      final formData = <String, String>{};

      if (extractedData.containsKey('name')) {
        formData['fullName'] = extractedData['name']!;
      }
      if (extractedData.containsKey('nationalId')) {
        formData['nationalId'] = extractedData['nationalId']!;
      }
      if (extractedData.containsKey('address')) {
        formData['address'] = extractedData['address']!;
      }
      if (extractedData.containsKey('birthDate')) {
        formData['birthDate'] = extractedData['birthDate']!;
      }

      if (kDebugMode) {
        print('Enhanced ID card data extracted: $formData');
        print('Front OCR confidence: ${frontResult.confidence}');
        print('Back OCR confidence: ${backResult.confidence}');
      }

      return formData;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting ID card data: $e');
      }
      throw 'فشل في استخراج بيانات بطاقة الهوية: $e';
    }
  }

  // Clean extracted text
  String _cleanExtractedText(String text) {
    // Remove extra whitespaces and newlines
    text = text.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Remove special characters that might interfere
    text = text.replaceAll(RegExp(r'[^\w\s\u0600-\u06FF]'), '');
    
    return text;
  }

  // Clean motor fingerprint text specifically
  String _cleanMotorFingerprintText(String text) {
    // Remove all spaces and special characters
    text = text.replaceAll(RegExp(r'[^\w\u0600-\u06FF0-9]'), '');
    
    // Convert to uppercase for consistency
    text = text.toUpperCase();
    
    return text;
  }

  // Parse ID card data from extracted text
  Map<String, String> _parseIdCardData(String frontText, String backText) {
    final Map<String, String> data = {};
    
    try {
      // Combine front and back text
      final fullText = '$frontText $backText';
      
      // Extract national ID (14 digits)
      final nationalIdMatch = RegExp(r'\b\d{14}\b').firstMatch(fullText);
      if (nationalIdMatch != null) {
        data['nationalId'] = nationalIdMatch.group(0)!;
      }

      // Extract name (Arabic text patterns)
      final nameMatch = RegExp(r'[\u0600-\u06FF\s]{10,50}').firstMatch(frontText);
      if (nameMatch != null) {
        data['fullName'] = nameMatch.group(0)!.trim();
      }

      // Extract address (usually longer Arabic text)
      final addressMatch = RegExp(r'[\u0600-\u06FF\s]{20,100}').firstMatch(backText);
      if (addressMatch != null) {
        data['address'] = addressMatch.group(0)!.trim();
      }

      // Extract dates (DD/MM/YYYY format)
      final dateMatches = RegExp(r'\b\d{2}/\d{2}/\d{4}\b').allMatches(fullText);
      if (dateMatches.isNotEmpty) {
        final dates = dateMatches.map((m) => m.group(0)!).toList();
        if (dates.isNotEmpty) data['issueDate'] = dates.first;
        if (dates.length > 1) data['expiryDate'] = dates.last;
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error parsing ID card data: $e');
      }
    }

    return data;
  }

  // Validate image quality for OCR
  Future<bool> validateImageQuality(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) return false;
      
      // Check minimum resolution
      if (image.width < 800 || image.height < 600) {
        return false;
      }
      
      // Check file size (not too small, not too large)
      final fileSizeKB = bytes.length / 1024;
      if (fileSizeKB < 100 || fileSizeKB > 5120) { // 100KB to 5MB
        return false;
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error validating image quality: $e');
      }
      return false;
    }
  }

  // Get image capture guidelines
  Map<String, String> getImageCaptureGuidelines() {
    return {
      'lighting': 'تأكد من وجود إضاءة جيدة ومتساوية',
      'angle': 'التقط الصورة من زاوية مستقيمة',
      'distance': 'اقترب بما فيه الكفاية لرؤية التفاصيل بوضوح',
      'stability': 'تأكد من ثبات اليد أثناء التصوير',
      'background': 'استخدم خلفية واضحة ومتباينة',
      'focus': 'تأكد من وضوح التركيز على النص',
    };
  }
}
