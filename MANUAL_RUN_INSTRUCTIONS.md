# تعليمات تشغيل التطبيق على الهاتف يدوياً 📱

## 🔧 الخطوات المطلوبة

### **الخطوة 1: إعداد الهاتف**

#### **لهواتف Android:**
1. **تفعيل خيارات المطور:**
   - اذهب إلى `الإعدادات` → `حول الهاتف`
   - اضغط على `رقم البناء` **7 مرات متتالية**
   - ستظهر رسالة "أصبحت مطوراً"

2. **تفعيل USB Debugging:**
   - اذهب إلى `الإعدادات` → `خيارات المطور`
   - فعّل `USB debugging`
   - فعّل `Install via USB` (إذا كان متاحاً)

3. **توصيل الهاتف:**
   - وصل الهاتف بالكمبيوتر عبر كابل USB **جيد**
   - اختر `نقل الملفات` أو `MTP` عند ظهور الخيارات
   - **اقبل تصريح USB Debugging** عند ظهوره على الهاتف

---

### **الخطوة 2: فتح Command Prompt**

1. **اضغط** `Windows + R`
2. **اكتب** `cmd` واضغط Enter
3. **انتقل لمجلد المشروع:**
   ```cmd
   cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"
   ```

---

### **الخطوة 3: التحقق من الاتصال**

**شغل هذا الأمر للتحقق من الأجهزة:**
```cmd
flutter devices
```

**يجب أن ترى شيئاً مثل:**
```
Found 2 connected devices:
  SM-G973F (mobile) • RZ8M802WY0X • android-arm64 • Android 11 (API 30)
  Chrome (web)      • chrome      • web-javascript • Google Chrome 98.0.4758.102
```

**إذا لم تر هاتفك:**
```cmd
adb devices
```

**إذا ظهر "unauthorized":**
- تحقق من قبول تصريح USB Debugging على الهاتف
- جرب كابل USB مختلف

---

### **الخطوة 4: تنظيف وإعداد المشروع**

**شغل هذه الأوامر بالترتيب:**

```cmd
flutter clean
```
```cmd
flutter pub get
```
```cmd
flutter analyze
```

---

### **الخطوة 5: تشغيل التطبيق**

**شغل هذا الأمر:**
```cmd
flutter run --debug
```

**أو للحصول على تفاصيل أكثر:**
```cmd
flutter run --debug --verbose
```

---

## 🚨 حل المشاكل الشائعة

### **مشكلة: الهاتف غير ظاهر**

**الحل:**
```cmd
adb kill-server
adb start-server
flutter devices
```

### **مشكلة: "No devices found"**

**تحقق من:**
- ✅ كابل USB يعمل للبيانات (ليس للشحن فقط)
- ✅ USB Debugging مفعل
- ✅ تم قبول التصريح على الهاتف
- ✅ تعريفات الهاتف مثبتة على الكمبيوتر

### **مشكلة: "Gradle build failed"**

**الحل:**
```cmd
cd android
gradlew clean
cd ..
flutter clean
flutter pub get
flutter run
```

### **مشكلة: "Firebase configuration"**

**تحقق من وجود:**
- ✅ `android/app/google-services.json`
- ✅ اتصال بالإنترنت

---

## 📱 بديل: بناء APK وتثبيته

**إذا لم يعمل التشغيل المباشر:**

### **1. بناء APK:**
```cmd
flutter build apk --debug
```

### **2. تثبيت APK:**
```cmd
adb install build\app\outputs\flutter-apk\app-debug.apk
```

### **3. أو نسخ APK للهاتف:**
- انسخ الملف من: `build\app\outputs\flutter-apk\app-debug.apk`
- انقله للهاتف وثبته يدوياً

---

## 🎯 معلومات تسجيل الدخول

**عند تشغيل التطبيق:**
- **اسم المستخدم:** `ahmed`
- **كلمة المرور:** `admin123`

---

## 📋 قائمة فحص سريعة

**قبل التشغيل، تأكد من:**

- [ ] الهاتف متصل بكابل USB جيد
- [ ] USB Debugging مفعل
- [ ] تم قبول تصريح USB Debugging
- [ ] الهاتف يظهر في `flutter devices`
- [ ] اتصال بالإنترنت متاح
- [ ] مساحة كافية على الهاتف (500 ميجا على الأقل)

---

## 🔄 خطوات التشغيل السريع

**انسخ والصق هذه الأوامر:**

```cmd
cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"
flutter devices
flutter clean
flutter pub get
flutter run --debug
```

---

## 📞 إذا واجهت مشاكل

**اتصل بـ:**
- **المطور:** معتصم سالم
- **واتساب:** 01062606098

**أو أرسل لي:**
- لقطة شاشة من رسالة الخطأ
- نتيجة أمر `flutter devices`
- نتيجة أمر `adb devices`

---

## 🎉 ما ستراه عند النجاح

1. **شاشة التحميل** بلوجو المؤسسة
2. **شاشة تسجيل الدخول** 
3. **الشاشة الرئيسية** مع جميع المميزات

**التطبيق جاهز للاستخدام!** 🚀
