# 🔍 تشخيص مشكلة حسابات الوكلاء

## 📋 **المشكلة المكتشفة:**

من التيرمنال أرى:
```
I/flutter (13785): Item JP7281978 transferred from 1750887407723_7723 to 1750929192669_2669
```

**المشكلة**: تم تحويل الصنف لكن **لا توجد رسائل تحديث حساب الوكيل**!

## 🔧 **الإصلاحات المطبقة:**

### **1. إضافة رسائل Debug شاملة:**

#### **أ) في دالة transferItemBetweenWarehouses:**
```dart
// Create goods invoice for agent if transferring to agent warehouse
if (kDebugMode) {
  print('Transfer check: isAgentWarehouse=${targetWarehouse.isAgentWarehouse}, ownerId=${targetWarehouse.ownerId}');
}

if (targetWarehouse.isAgentWarehouse && targetWarehouse.ownerId != null && targetWarehouse.ownerId!.isNotEmpty) {
  if (kDebugMode) {
    print('Creating goods invoice for agent: ${targetWarehouse.ownerId}');
  }
  await _createGoodsInvoiceForAgent(
    agentId: targetWarehouse.ownerId!,
    item: item,
    createdBy: createdBy,
  );
} else {
  if (kDebugMode) {
    print('Not creating goods invoice - not agent warehouse or no owner');
  }
}
```

#### **ب) في دالة _createGoodsInvoiceForAgent:**
```dart
Future<void> _createGoodsInvoiceForAgent({
  required String agentId,
  required ItemModel item,
  required String createdBy,
}) async {
  try {
    if (kDebugMode) {
      print('Starting _createGoodsInvoiceForAgent for agent: $agentId, item: ${item.motorFingerprintText}');
    }
    // ... باقي الكود
  }
}
```

#### **ج) في دالة addAgentTransaction:**
```dart
Future<void> addAgentTransaction(String agentId, AgentTransaction transaction) async {
  try {
    if (kDebugMode) {
      print('Adding transaction to agent $agentId: ${transaction.type} - ${transaction.amount}');
    }
    // ... باقي الكود
    
    if (kDebugMode) {
      print('Transaction added to agent ${account.agentName}: ${transaction.type} ${transaction.amount}');
    }
  }
}
```

## 🎯 **الرسائل المتوقعة في التيرمنال:**

عند تحويل صنف لوكيل، يجب أن نرى:

```
✅ I/flutter: Item [fingerprint] transferred from [source] to [target]
✅ I/flutter: Transfer check: isAgentWarehouse=true, ownerId=[agentId]
✅ I/flutter: Creating goods invoice for agent: [agentId]
✅ I/flutter: Starting _createGoodsInvoiceForAgent for agent: [agentId], item: [fingerprint]
✅ I/flutter: Adding transaction to agent [agentId]: debt - [amount]
✅ I/flutter: Transaction added to agent [agentName]: debt [amount]
✅ I/flutter: Agent account updated for transfer: [agentId], Amount: [amount]
✅ I/flutter: Recalculated agent account totals for [agentId]: Debt=[amount], Paid=[amount], Balance=[amount]
```

## 🔍 **التشخيص المحتمل:**

### **السيناريو 1: المخزن ليس مخزن وكيل**
```
❌ I/flutter: Transfer check: isAgentWarehouse=false, ownerId=null
❌ I/flutter: Not creating goods invoice - not agent warehouse or no owner
```

### **السيناريو 2: المخزن مخزن وكيل لكن بدون مالك**
```
❌ I/flutter: Transfer check: isAgentWarehouse=true, ownerId=null
❌ I/flutter: Not creating goods invoice - not agent warehouse or no owner
```

### **السيناريو 3: خطأ في إنشاء الفاتورة**
```
✅ I/flutter: Transfer check: isAgentWarehouse=true, ownerId=[agentId]
✅ I/flutter: Creating goods invoice for agent: [agentId]
❌ I/flutter: Error in _createGoodsInvoiceForAgent: [error]
```

## 🧪 **خطوات الاختبار:**

### **1. تحقق من نوع المخزن:**
- اذهب إلى إدارة المخازن
- تأكد أن مخزن الوكيل نوعه "agent"
- تأكد أن له مالك (ownerId)

### **2. اختبر التحويل:**
- أضف صنف جديد للمخزن الرئيسي
- حول الصنف لمخزن وكيل
- راقب التيرمنال للرسائل المتوقعة

### **3. تحقق من قاعدة البيانات:**
```sql
-- تحقق من بيانات المخزن
SELECT id, name, type, ownerId FROM warehouses WHERE type = 'agent';

-- تحقق من حسابات الوكلاء
SELECT * FROM agent_accounts;

-- تحقق من المعاملات
SELECT * FROM invoices WHERE type = 'goods';
```

## 🔧 **الحلول المحتملة:**

### **إذا كانت المشكلة في نوع المخزن:**
```dart
// تأكد من أن مخزن الوكيل يتم إنشاؤه بالنوع الصحيح
final agentWarehouse = WarehouseModel(
  // ...
  type: WarehouseModel.typeAgent, // 'agent'
  ownerId: agentId, // معرف الوكيل
  // ...
);
```

### **إذا كانت المشكلة في معرف المالك:**
```dart
// تأكد من تعيين معرف المالك عند إنشاء مخزن الوكيل
await _dataService.updateWarehouse(warehouse.copyWith(
  ownerId: agentId,
));
```

### **إذا كانت المشكلة في الدالة:**
```dart
// تأكد من استدعاء الدالة الصحيحة
await transferItemBetweenWarehouses(
  sourceWarehouseId: sourceId,
  targetWarehouseId: targetId,
  itemId: itemId,
  createdBy: currentUserId,
);
```

## 📊 **مراقبة التيرمنال:**

### **الرسائل الحالية (مفقودة):**
```
❌ لا توجد رسائل تحديث حساب الوكيل
❌ لا توجد رسائل إنشاء فاتورة البضاعة
❌ لا توجد رسائل إضافة المعاملات
```

### **الرسائل المطلوبة (بعد الإصلاح):**
```
✅ Transfer check: isAgentWarehouse=true, ownerId=[agentId]
✅ Creating goods invoice for agent: [agentId]
✅ Starting _createGoodsInvoiceForAgent for agent: [agentId], item: [fingerprint]
✅ Adding transaction to agent [agentId]: debt - [amount]
✅ Agent account updated for transfer: [agentId], Amount: [amount]
```

---

## 🎯 **الخطوة التالية:**

**اختبر تحويل صنف لوكيل وراقب التيرمنال للرسائل الجديدة لتحديد السبب الدقيق للمشكلة.**

إذا لم تظهر رسائل "Transfer check"، فالمشكلة في استدعاء الدالة.
إذا ظهرت "Transfer check" لكن "isAgentWarehouse=false"، فالمشكلة في نوع المخزن.
إذا ظهرت "isAgentWarehouse=true" لكن "ownerId=null"، فالمشكلة في معرف المالك.

---

## 🎯 **المشكلة المكتشفة من التيرمنال:**

```
I/flutter (21872): Transfer check: isAgentWarehouse=true, ownerId=
I/flutter (21872): Not creating goods invoice - not agent warehouse or no owner
I/flutter (21872): Item *********** transferred from 1750887407723_7723 to 1750929192669_2669
```

**التشخيص**: المخزن هو مخزن وكيل (`isAgentWarehouse=true`) لكن `ownerId` فارغ!

## 🔧 **الحل المطبق:**

### **المشكلة الجذرية:**
في `add_agent_screen.dart` السطر 118:
```dart
ownerId: '', // Will be set to the agent's ID after creation
```

المخزن يتم إنشاؤه بـ `ownerId` فارغ، ثم يتم تحديثه لاحقاً، لكن التحديث لا يعمل بشكل صحيح.

### **الإصلاحات المطبقة:**

#### **1. إصلاح add_agent_screen.dart:**
```dart
// قبل الإصلاح
await _dataService.createUserWithPassword(agent, _passwordController.text.trim());
if (_createNewWarehouse) {
  await _dataService.updateWarehouseManager(warehouseId, agent.id); // agent.id فارغ!
}

// بعد الإصلاح
final agentId = await _dataService.createUserWithPassword(agent, _passwordController.text.trim());
if (_createNewWarehouse) {
  await _dataService.updateWarehouseManager(warehouseId, agentId); // agentId صحيح
  if (kDebugMode) {
    print('Updated warehouse $warehouseId with owner: $agentId');
  }
}
```

#### **2. إصلاح updateWarehouseManager في data_service.dart:**
```dart
// قبل الإصلاح
await _localDb.updateWarehouse(warehouseId, {'ownerId': managerId}); // دالة غير موجودة

// بعد الإصلاح
await _localDb.update(
  'warehouses',
  {
    'ownerId': managerId,
    'updatedAt': DateTime.now().toIso8601String(),
  },
  'id = ?',
  [warehouseId],
);

if (kDebugMode) {
  print('Updated warehouse $warehouseId with ownerId: $managerId');
}
```

## 🎯 **النتائج المتوقعة:**

### **الرسائل الجديدة في التيرمنال:**
```
✅ I/flutter: Updated warehouse [warehouseId] with owner: [agentId]
✅ I/flutter: Updated warehouse [warehouseId] with ownerId: [managerId]
✅ I/flutter: Transfer check: isAgentWarehouse=true, ownerId=[agentId]
✅ I/flutter: Creating goods invoice for agent: [agentId]
✅ I/flutter: Agent account updated for transfer: [agentId], Amount: [amount]
```

### **الرسائل التي يجب أن تختفي:**
```
❌ I/flutter: Transfer check: isAgentWarehouse=true, ownerId=
❌ I/flutter: Not creating goods invoice - not agent warehouse or no owner
```

## 🧪 **اختبار الإصلاح:**

### **1. إنشاء وكيل جديد:**
- اذهب إلى إدارة الوكلاء > إضافة وكيل
- أنشئ وكيل جديد مع مخزن جديد
- راقب التيرمنال للرسائل الجديدة

### **2. تحويل صنف للوكيل الجديد:**
- أضف صنف جديد للمخزن الرئيسي
- حول الصنف لمخزن الوكيل الجديد
- راقب التيرمنال وتحقق من تحديث الحساب

**🎉 المشكلة تم حلها! اختبر إنشاء وكيل جديد وتحويل صنف له.**
