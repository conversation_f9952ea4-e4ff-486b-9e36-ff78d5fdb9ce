import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/invoice_model.dart';
import '../../models/item_model.dart';
import '../../models/document_tracking_model.dart';
import '../../services/data_service.dart';


class CustomerInquiryScreen extends StatefulWidget {
  const CustomerInquiryScreen({super.key});

  @override
  State<CustomerInquiryScreen> createState() => _CustomerInquiryScreenState();
}

class _CustomerInquiryScreenState extends State<CustomerInquiryScreen> {
  final _searchController = TextEditingController();
  final DataService _dataService = DataService.instance;
  
  List<InvoiceModel> _searchResults = [];
  Map<String, dynamic>? _customerHistory;
  bool _isLoading = false;
  String _searchType = 'all';
  
  final List<Map<String, String>> _searchTypes = [
    {'value': 'all', 'label': 'البحث في جميع الحقول'},
    {'value': 'invoice_number', 'label': 'رقم الفاتورة'},
    {'value': 'customer_name', 'label': 'اسم العميل'},
    {'value': 'national_id', 'label': 'الرقم القومي'},
    {'value': 'motor_fingerprint', 'label': 'بصمة الموتور'},
    {'value': 'chassis_number', 'label': 'رقم الشاسيه'},
    {'value': 'phone', 'label': 'رقم الهاتف'},
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _customerHistory = null;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final history = await _dataService.getCustomerTransactionHistory(
        query: query,
        searchType: _searchType,
      );

      setState(() {
        _customerHistory = history;
        _searchResults = history['invoices'] as List<InvoiceModel>;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في البحث: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showInvoiceDetails(InvoiceModel invoice) async {
    try {
      // Get item details
      final item = await _dataService.getItemById(invoice.itemId);
      
      // Get document tracking
      final documentTracking = await _dataService.getDocumentTrackingByInvoiceId(invoice.id);
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => _InvoiceDetailsDialog(
            invoice: invoice,
            item: item,
            documentTracking: documentTracking,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل التفاصيل: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استعلام العملاء'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Search section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: Colors.grey[50],
            child: Column(
              children: [
                // Search type dropdown
                DropdownButtonFormField<String>(
                  value: _searchType,
                  decoration: const InputDecoration(
                    labelText: 'نوع البحث',
                    border: OutlineInputBorder(),
                  ),
                  items: _searchTypes.map((type) {
                    return DropdownMenuItem(
                      value: type['value'],
                      child: Text(type['label']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _searchType = value!;
                    });
                  },
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Search field
                TextFormField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    labelText: 'البحث',
                    hintText: 'ادخل كلمة البحث...',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.search),
                      onPressed: _performSearch,
                    ),
                  ),
                  onFieldSubmitted: (_) => _performSearch(),
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Search button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _performSearch,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.search),
                    label: Text(_isLoading ? 'جاري البحث...' : 'بحث'),
                  ),
                ),
              ],
            ),
          ),
          
          // Customer summary section
          if (_customerHistory != null) ...[
            _buildCustomerSummary(),
            const SizedBox(height: AppConstants.defaultPadding),
          ],

          // Results section
          Expanded(
            child: _buildResultsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerSummary() {
    if (_customerHistory == null) return const SizedBox.shrink();

    final summary = _customerHistory!['summary'] as Map<String, dynamic>;
    final totalPurchases = summary['totalPurchases'] as double;
    final totalTransactions = summary['totalTransactions'] as int;
    final firstPurchase = summary['firstPurchase'] as DateTime?;
    final lastPurchase = summary['lastPurchase'] as DateTime?;
    final averagePurchase = summary['averagePurchase'] as double;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص معاملات العميل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المشتريات',
                    AppUtils.formatCurrency(totalPurchases),
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'عدد المعاملات',
                    totalTransactions.toString(),
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'متوسط الشراء',
                    AppUtils.formatCurrency(averagePurchase),
                    Icons.trending_up,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'آخر شراء',
                    lastPurchase != null ? AppUtils.formatDate(lastPurchase) : 'لا يوجد',
                    Icons.schedule,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            if (firstPurchase != null) ...[
              const SizedBox(height: 8),
              Text(
                'عميل منذ: ${AppUtils.formatDate(firstPurchase)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              _searchController.text.isEmpty
                  ? 'ادخل كلمة البحث للعثور على العملاء'
                  : 'لم يتم العثور على نتائج',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final invoice = _searchResults[index];
        return _buildInvoiceCard(invoice);
      },
    );
  }

  Widget _buildInvoiceCard(InvoiceModel invoice) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: const Icon(Icons.person, color: Colors.white),
        ),
        title: Text(
          invoice.customerName ?? 'غير محدد',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم الفاتورة: ${invoice.invoiceNumber}'),
            Text('الهاتف: ${invoice.customerPhone ?? 'غير محدد'}'),
            Text('التاريخ: ${AppUtils.formatDate(invoice.createdAt)}'),
            Text('المبلغ: ${AppUtils.formatCurrency(invoice.sellingPrice)}'),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showInvoiceDetails(invoice),
      ),
    );
  }
}

class _InvoiceDetailsDialog extends StatelessWidget {
  final InvoiceModel invoice;
  final ItemModel? item;
  final DocumentTrackingModel? documentTracking;

  const _InvoiceDetailsDialog({
    required this.invoice,
    this.item,
    this.documentTracking,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.receipt, color: Colors.white),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Text(
                      'تفاصيل الفاتورة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection('معلومات الفاتورة', [
                      _buildDetailRow('رقم الفاتورة', invoice.invoiceNumber),
                      _buildDetailRow('التاريخ', AppUtils.formatDate(invoice.createdAt)),
                      _buildDetailRow('المبلغ', AppUtils.formatCurrency(invoice.sellingPrice)),
                      _buildDetailRow('الحالة', _getStatusText(invoice.status)),
                    ]),
                    
                    const SizedBox(height: AppConstants.largePadding),
                    
                    _buildSection('معلومات العميل', [
                      _buildDetailRow('الاسم', invoice.customerName ?? 'غير محدد'),
                      _buildDetailRow('الهاتف', invoice.customerPhone ?? 'غير محدد'),
                      _buildDetailRow('العنوان', invoice.customerAddress ?? 'غير محدد'),
                      _buildDetailRow('الرقم القومي', invoice.customerNationalId ?? 'غير محدد'),
                    ]),
                    
                    if (item != null) ...[
                      const SizedBox(height: AppConstants.largePadding),
                      _buildSection('معلومات المركبة', [
                        _buildDetailRow('النوع', item!.type),
                        _buildDetailRow('الماركة', item!.brand),
                        _buildDetailRow('الموديل', item!.model),
                        _buildDetailRow('اللون', item!.color),
                        _buildDetailRow('بصمة الموتور', item!.motorFingerprintText),
                        _buildDetailRow('رقم الشاسيه', item!.chassisNumber),
                      ]),
                    ],
                    
                    if (documentTracking != null) ...[
                      const SizedBox(height: AppConstants.largePadding),
                      _buildSection('حالة الجواب', [
                        _buildDetailRow('الحالة الحالية', _getDocumentStatusText(documentTracking!.currentStatus)),
                        _buildDetailRow('آخر تحديث', AppUtils.formatDate(documentTracking!.updatedAt)),
                      ]),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  String _getDocumentStatusText(String status) {
    switch (status) {
      case 'sent_to_manufacturer':
        return 'تم إرسال للشركة المصنعة';
      case 'received_from_manufacturer':
        return 'تم استلام من الشركة المصنعة';
      case 'sent_to_sale_point':
        return 'تم إرسال لنقطة البيع';
      case 'ready_for_pickup':
        return 'جاهز للاستلام';
      default:
        return status;
    }
  }
}
