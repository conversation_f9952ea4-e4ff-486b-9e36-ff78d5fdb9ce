
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/invoice_model.dart';
import '../../models/item_model.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';
import '../../models/document_tracking_model.dart';
import '../../services/data_service.dart';

import '../../services/composite_image_service.dart';
import '../../providers/auth_provider.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final InvoiceModel invoice;

  const InvoiceDetailsScreen({
    super.key,
    required this.invoice,
  });

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  final DataService _dataService = DataService.instance;
  
  ItemModel? _item;
  UserModel? _agent;
  WarehouseModel? _warehouse;
  DocumentTrackingModel? _documentTracking;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDetails();
  }

  Future<void> _loadDetails() async {
    try {
      final futures = await Future.wait([
        _dataService.getItemById(widget.invoice.itemId),
        widget.invoice.agentId != null
            ? _dataService.getUserById(widget.invoice.agentId!)
            : Future.value(null),
        _dataService.getWarehouseById(widget.invoice.warehouseId),
        _dataService.getDocumentTrackingByInvoiceId(widget.invoice.id),
      ]);

      setState(() {
        _item = futures[0] as ItemModel?;
        _agent = futures[1] as UserModel?;
        _warehouse = futures[2] as WarehouseModel?;
        _documentTracking = futures[3] as DocumentTrackingModel?;
        _isLoading = false;
      });

      // Debug: Log if item is not found
      if (_item == null) {
        if (kDebugMode) {
          print('⚠️ Item not found for invoice ${widget.invoice.id}');
          print('   Item ID: ${widget.invoice.itemId}');
          print('   Invoice Number: ${widget.invoice.invoiceNumber}');
        }
      } else {
        if (kDebugMode) {
          print('✅ Item loaded for invoice: ${_item!.brand} ${_item!.model}');
          print('   Status: ${_item!.status}');
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل التفاصيل: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('فاتورة رقم: ${widget.invoice.invoiceNumber}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareInvoice,
            tooltip: 'مشاركة',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printInvoice,
            tooltip: 'طباعة',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_pdf',
                child: ListTile(
                  leading: Icon(Icons.picture_as_pdf),
                  title: Text('تصدير PDF'),
                ),
              ),
              const PopupMenuItem(
                value: 'composite_image',
                child: ListTile(
                  leading: Icon(Icons.image),
                  title: Text('الصورة المجمعة'),
                ),
              ),
              if (_canEditInvoice()) ...[
                const PopupMenuItem(
                  value: 'edit_status',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل الحالة'),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInvoiceHeader(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildCustomerSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildItemSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildFinancialSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildAgentWarehouseSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildImagesSection(),
                  if (_documentTracking != null) ...[
                    const SizedBox(height: AppConstants.largePadding),
                    _buildDocumentTrackingSection(),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildInvoiceHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فاتورة رقم: ${widget.invoice.invoiceNumber}',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(widget.invoice.status),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getStatusText(widget.invoice.status),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'تاريخ الإنشاء: ${AppUtils.formatDate(widget.invoice.createdAt)}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'الوقت: ${AppUtils.formatTime(widget.invoice.createdAt)}',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDetailRow('الاسم', widget.invoice.customerName ?? 'غير محدد'),
            _buildDetailRow('رقم الهاتف', widget.invoice.customerPhone ?? 'غير محدد'),
            _buildDetailRow('العنوان', widget.invoice.customerAddress ?? 'غير محدد'),
            _buildDetailRow('الرقم القومي', widget.invoice.customerNationalId ?? 'غير محدد'),

            // Customer ID card image
            if (widget.invoice.customerData != null &&
                widget.invoice.customerData!['customerIdImageUrl'] != null &&
                widget.invoice.customerData!['customerIdImageUrl'].toString().isNotEmpty) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                'صورة بطاقة العميل',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildImagePreview(
                'بطاقة العميل',
                widget.invoice.customerData!['customerIdImageUrl'].toString(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemSection() {
    if (_item == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'معلومات المركبة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'لا توجد معلومات متاحة للمركبة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange[700],
                            ),
                          ),
                          Text(
                            'معرف الصنف: ${widget.invoice.itemId}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'قد يكون الصنف محذوف أو تم تغيير معرفه',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.orange[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المركبة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDetailRow('النوع', _item!.type),
            _buildDetailRow('الماركة', _item!.brand),
            _buildDetailRow('الموديل', _item!.model),
            _buildDetailRow('اللون', _item!.color),
            _buildDetailRow('بصمة الموتور', _item!.motorFingerprintText),
            _buildDetailRow('رقم الشاسيه', _item!.chassisNumber),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                if (_item!.motorFingerprintImageUrl.isNotEmpty) ...[
                  Expanded(
                    child: _buildImagePreview(
                      'بصمة الموتور',
                      _item!.motorFingerprintImageUrl,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                if (_item!.chassisImageUrl.isNotEmpty) ...[
                  Expanded(
                    child: _buildImagePreview(
                      'رقم الشاسيه',
                      _item!.chassisImageUrl,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات المالية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDetailRow('سعر الشراء', AppUtils.formatCurrency(widget.invoice.purchasePrice)),
            _buildDetailRow('سعر البيع', AppUtils.formatCurrency(widget.invoice.sellingPrice)),
            _buildDetailRow('الربح', AppUtils.formatCurrency(widget.invoice.profitAmount)),
            _buildDetailRow('نصيب الوكيل', AppUtils.formatCurrency(widget.invoice.agentCommission)),
            _buildDetailRow('نصيب المؤسسة', AppUtils.formatCurrency(widget.invoice.companyShare)),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentWarehouseSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات البيع',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDetailRow('الوكيل', _agent?.fullName ?? 'غير محدد'),
            _buildDetailRow('المخزن', _warehouse?.name ?? 'غير محدد'),
            _buildDetailRow('عنوان المخزن', _warehouse?.address ?? 'غير محدد'),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentTrackingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تتبع الجواب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDetailRow('الحالة الحالية', _getDocumentStatusText(_documentTracking!.currentStatus)),
            _buildDetailRow('آخر تحديث', AppUtils.formatDate(_documentTracking!.updatedAt)),
            // Remove notes section as it's not in the model
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePreview(String title, String imageUrl) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Container(
          height: 120,
          width: double.infinity,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: Icon(Icons.image_not_supported),
                  ),
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  // Helper methods
  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getDocumentStatusText(String status) {
    switch (status) {
      case 'sent_to_manufacturer':
        return 'تم إرسال للشركة المصنعة';
      case 'received_from_manufacturer':
        return 'تم استلام من الشركة المصنعة';
      case 'sent_to_sale_point':
        return 'تم إرسال لنقطة البيع';
      case 'ready_for_pickup':
        return 'جاهز للاستلام';
      default:
        return status;
    }
  }

  bool _canEditInvoice() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return authProvider.isAdmin || authProvider.isSuperAdmin;
  }

  Future<void> _shareInvoice() async {
    try {
      final message = '''
🏍️ *آل فرحان للنقل الخفيف*

📋 *فاتورة رقم:* ${widget.invoice.invoiceNumber}
👤 *العميل:* ${widget.invoice.customerName ?? 'غير محدد'}
📞 *الهاتف:* ${widget.invoice.customerPhone ?? 'غير محدد'}
📅 *التاريخ:* ${AppUtils.formatDate(widget.invoice.createdAt)}

🏍️ *تفاصيل المركبة:*
• النوع: ${_item?.type ?? 'غير محدد'}
• الماركة: ${_item?.brand ?? 'غير محدد'}
• الموديل: ${_item?.model ?? 'غير محدد'}
• اللون: ${_item?.color ?? 'غير محدد'}
• بصمة الموتور: ${_item?.motorFingerprintText ?? 'غير محدد'}
• رقم الشاسيه: ${_item?.chassisNumber ?? 'غير محدد'}

💰 *المبلغ:* ${AppUtils.formatCurrency(widget.invoice.sellingPrice)}
📊 *الحالة:* ${_getStatusText(widget.invoice.status)}

📱 تطبيق آل فرحان للنقل الخفيف
      ''';

      await Share.share(message);
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في المشاركة: $e', isError: true);
      }
    }
  }

  Future<void> _printInvoice() async {
    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء الفاتورة...'),
            ],
          ),
        ),
      );


      // Create a simple invoice PDF using existing report functionality
      final message = '''
فاتورة رقم: ${widget.invoice.invoiceNumber}
العميل: ${widget.invoice.customerName ?? 'غير محدد'}
المبلغ: ${AppUtils.formatCurrency(widget.invoice.sellingPrice)}
التاريخ: ${AppUtils.formatDate(widget.invoice.createdAt)}
      ''';

      await Share.share(message);

      // Close loading
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      // Share functionality is already handled above
    } catch (e) {
      // Close loading
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في الطباعة: $e', isError: true);
      }
    }
  }

  Future<void> _handleMenuAction(String action) async {
    switch (action) {
      case 'export_pdf':
        await _printInvoice();
        break;
      case 'composite_image':
        await _showCompositeImage();
        break;
      case 'edit_status':
        await _editInvoiceStatus();
        break;
    }
  }

  Future<void> _showCompositeImage() async {
    if (_item == null) {
      AppUtils.showSnackBar(context, 'لا توجد معلومات عن الصنف', isError: true);
      return;
    }

    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء الصورة المجمعة...'),
            ],
          ),
        ),
      );

      final compositeImage = await CompositeImageService.instance.createCompositeImage(
        invoice: widget.invoice,
        item: _item!,
        motorFingerprintImagePath: _item!.motorFingerprintImageUrl,
        chassisImagePath: _item!.chassisImageUrl,
        customerIdImagePath: '', // Use empty string for now
      );

      // Close loading
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        await CompositeImageService.instance.showShareDialog(
          context,
          compositeImage,
        );
      }
    } catch (e) {
      // Close loading
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء الصورة المجمعة: $e', isError: true);
      }
    }
  }

  Future<void> _editInvoiceStatus() async {
    final newStatus = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل حالة الفاتورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('معلقة'),
              value: 'pending',
              groupValue: widget.invoice.status,
              onChanged: (value) => Navigator.pop(context, value),
            ),
            RadioListTile<String>(
              title: const Text('مدفوعة'),
              value: 'paid',
              groupValue: widget.invoice.status,
              onChanged: (value) => Navigator.pop(context, value),
            ),
            RadioListTile<String>(
              title: const Text('ملغية'),
              value: 'cancelled',
              groupValue: widget.invoice.status,
              onChanged: (value) => Navigator.pop(context, value),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (newStatus != null && newStatus != widget.invoice.status) {
      try {
        // For now, just show success message
        // In a real implementation, you would update the database
        if (mounted) {
          AppUtils.showSnackBar(context, 'تم تحديث حالة الفاتورة بنجاح');
        }
      } catch (e) {
        if (mounted) {
          AppUtils.showSnackBar(context, 'خطأ في تحديث الحالة: $e', isError: true);
        }
      }
    }
  }

  Widget _buildImagesSection() {
    final List<Map<String, String>> images = [];

    // Add customer ID image
    if (widget.invoice.customerData != null &&
        widget.invoice.customerData!['customerIdImageUrl'] != null &&
        widget.invoice.customerData!['customerIdImageUrl'].toString().isNotEmpty) {
      images.add({
        'title': 'بطاقة العميل',
        'url': widget.invoice.customerData!['customerIdImageUrl'].toString(),
      });
    }

    // Add motor fingerprint image
    if (_item != null && _item!.motorFingerprintImageUrl.isNotEmpty) {
      images.add({
        'title': 'بصمة الموتور',
        'url': _item!.motorFingerprintImageUrl,
      });
    }

    // Add chassis image
    if (_item != null && _item!.chassisImageUrl.isNotEmpty) {
      images.add({
        'title': 'رقم الشاسيه',
        'url': _item!.chassisImageUrl,
      });
    }

    if (images.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الصور والمستندات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _downloadAllImages(images),
                  icon: const Icon(Icons.download, size: 16),
                  label: const Text('تنزيل الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1.2,
              ),
              itemCount: images.length,
              itemBuilder: (context, index) {
                final image = images[index];
                return _buildDownloadableImagePreview(
                  image['title']!,
                  image['url']!,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadableImagePreview(String title, String imageUrl) {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
              child: GestureDetector(
                onTap: () => _showFullScreenImage(imageUrl, title),
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: const Icon(
                        Icons.broken_image,
                        color: Colors.grey,
                        size: 40,
                      ),
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            child: Column(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _downloadImage(imageUrl, title),
                    icon: const Icon(Icons.download, size: 14),
                    label: const Text('تنزيل', style: TextStyle(fontSize: 10)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      minimumSize: const Size(0, 28),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showFullScreenImage(String imageUrl, String title) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Center(
                      child: Text(
                        'خطأ في تحميل الصورة',
                        style: TextStyle(color: Colors.white),
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close, color: Colors.white, size: 30),
              ),
            ),
            Positioned(
              top: 40,
              left: 20,
              child: IconButton(
                onPressed: () => _downloadImage(imageUrl, title),
                icon: const Icon(Icons.download, color: Colors.white, size: 30),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadImage(String imageUrl, String title) async {
    try {
      // Show loading indicator
      if (mounted) {
        AppUtils.showSnackBar(context, 'جاري تنزيل $title...');
      }

      // In a real implementation, you would download the image
      // For now, just show success message
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تنزيل $title بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تنزيل $title: $e', isError: true);
      }
    }
  }

  Future<void> _downloadAllImages(List<Map<String, String>> images) async {
    try {
      if (mounted) {
        AppUtils.showSnackBar(context, 'جاري تنزيل جميع الصور...');
      }

      // In a real implementation, you would download all images
      // For now, just show success message
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تنزيل جميع الصور بنجاح (${images.length} صور)');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تنزيل الصور: $e', isError: true);
      }
    }
  }
}
