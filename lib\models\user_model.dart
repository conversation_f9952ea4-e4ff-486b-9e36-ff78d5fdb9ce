import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String username;
  final String email;
  final String fullName;
  final String phone;
  final String role; // super_admin, admin, agent, showroom
  final String? warehouseId; // For agents and showroom users
  final String? passwordHash; // Encrypted password hash
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalData;

  UserModel({
    required this.id,
    required this.username,
    required this.email,
    required this.fullName,
    required this.phone,
    required this.role,
    this.warehouseId,
    this.passwordHash,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.additionalData,
  });

  // Convert from Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      username: data['username'] ?? '',
      email: data['email'] ?? '',
      fullName: data['fullName'] ?? '',
      phone: data['phone'] ?? '',
      role: data['role'] ?? '',
      warehouseId: data['warehouseId'],
      passwordHash: data['passwordHash'],
      isActive: _parseBool(data['isActive']) ?? true,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
      additionalData: _parseAdditionalData(data['additionalData']),
    );
  }

  // Helper method to parse bool from various formats
  static bool? _parseBool(dynamic value) {
    if (value == null) return null;

    if (value is bool) {
      return value;
    } else if (value is int) {
      return value == 1; // Convert 1 to true, 0 to false
    } else if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1') return true;
      if (lowerValue == 'false' || lowerValue == '0') return false;
    }

    return null;
  }



  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic dateValue) {
    if (dateValue == null) return DateTime.now();

    if (dateValue is Timestamp) {
      return dateValue.toDate();
    } else if (dateValue is String) {
      try {
        return DateTime.parse(dateValue);
      } catch (e) {
        return DateTime.now();
      }
    } else {
      return DateTime.now();
    }
  }

  // Convert from Map (for local database)
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      username: map['username'] ?? '',
      email: map['email'] ?? '',
      fullName: map['fullName'] ?? '',
      phone: map['phone'] ?? '',
      role: map['role'] ?? '',
      warehouseId: map['warehouseId'],
      passwordHash: map['passwordHash'],
      isActive: map['isActive'] is bool ? map['isActive'] : map['isActive'] == 1,
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      additionalData: _parseAdditionalData(map['additionalData']),
    );
  }

  // Helper method to parse additional data safely
  static Map<String, dynamic>? _parseAdditionalData(dynamic data) {
    if (data == null) return null;

    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is String) {
      try {
        // If it's a JSON string, try to parse it
        return Map<String, dynamic>.from(json.decode(data));
      } catch (e) {
        return null;
      }
    }

    return null;
  }

  // Convert to Map (for Firestore and local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'fullName': fullName,
      'phone': phone,
      'role': role,
      'warehouseId': warehouseId,
      'passwordHash': passwordHash,
      'isActive': isActive ? 1 : 0, // Convert bool to int for SQLite
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'additionalData': additionalData != null ? jsonEncode(additionalData) : null,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'username': username,
      'email': email,
      'fullName': fullName,
      'phone': phone,
      'role': role,
      'warehouseId': warehouseId,
      'passwordHash': passwordHash,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  UserModel copyWith({
    String? id,
    String? username,
    String? email,
    String? fullName,
    String? phone,
    String? role,
    String? warehouseId,
    String? passwordHash,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      warehouseId: warehouseId ?? this.warehouseId,
      passwordHash: passwordHash ?? this.passwordHash,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isSuperAdmin => role == 'super_admin';
  bool get isAdmin => role == 'admin';
  bool get isAgent => role == 'agent';
  bool get isShowroom => role == 'showroom';
  
  bool get canManageUsers => isSuperAdmin;
  bool get canManageInventory => isSuperAdmin || isAdmin;
  bool get canCreateInvoices => isSuperAdmin || isAdmin || isAgent || isShowroom;
  bool get canViewReports => isSuperAdmin || isAdmin;
  bool get canManageDocuments => isSuperAdmin || isAdmin;

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, fullName: $fullName, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
