# إرشادات بناء APK لتطبيق آل فرحان

## 🚀 طريقة سريعة - النسخة التجريبية

### الخطوات:

#### 1. **تشغيل ملف البناء التلقائي**
```bash
# في Windows
build_demo.bat

# في Mac/Linux
chmod +x build_demo.sh
./build_demo.sh
```

#### 2. **أو يدوياً:**
```bash
# نسخ الملفات التجريبية
copy pubspec_demo.yaml pubspec.yaml
copy lib\main_demo.dart lib\main.dart

# تحميل المكتبات
flutter pub get

# بناء APK
flutter build apk --release

# الملف سيكون في:
# build/app/outputs/flutter-apk/app-release.apk
```

### 📱 **النسخة التجريبية تحتوي على:**
- ✅ 4 حسابات تجريبية جاهزة
- ✅ واجهات المستخدم كاملة
- ✅ تصميم احترافي
- ✅ نظام صلاحيات
- ⚠️ بدون Firebase (بيانات وهمية)
- ⚠️ بدون OCR (محاكاة)

### 👥 **الحسابات التجريبية:**
| المستخدم | كلمة المرور | الدور |
|----------|-------------|-------|
| `admin` | `123456` | مدير أعلى |
| `manager` | `123456` | مدير إداري |
| `agent1` | `123456` | وكيل |
| `showroom` | `123456` | معرض |

---

## 🔥 النسخة الكاملة - مع Firebase

### المتطلبات:
- حساب Firebase
- حساب Cloudinary
- Android Studio مع SDK

### الخطوات:

#### 1. **إعداد Firebase**
```bash
# اذهب إلى https://console.firebase.google.com
# أنشئ مشروع جديد: "el-farhan-app"
# أضف تطبيق Android
# Package name: com.elfarhan.transport
# حمل google-services.json
```

#### 2. **إعداد ملفات Firebase**
```bash
# ضع الملف في:
android/app/google-services.json

# فعل الخدمات في Firebase Console:
# - Authentication (Email/Password)
# - Cloud Firestore
# - Cloud Messaging
# - Storage
```

#### 3. **إعداد Cloudinary**
```bash
# اذهب إلى https://cloudinary.com
# أنشئ حساب مجاني
# احصل على:
# - Cloud Name
# - Upload Preset (unsigned)
```

#### 4. **تحديث إعدادات التطبيق**
```dart
// في lib/services/image_service.dart
static const String cloudName = 'your-cloud-name';
static const String uploadPreset = 'your-upload-preset';
```

#### 5. **إعداد قواعد Firestore**
```bash
# انسخ محتوى firestore.rules إلى Firebase Console
# في قسم Firestore Database > Rules
```

#### 6. **بناء APK النهائي**
```bash
flutter clean
flutter pub get
flutter build apk --release --no-shrink
```

---

## 🛠️ حل المشاكل الشائعة

### مشكلة: "SDK not found"
```bash
# تأكد من تثبيت Flutter SDK
flutter doctor

# إعداد متغيرات البيئة
export PATH="$PATH:/path/to/flutter/bin"
```

### مشكلة: "Android SDK not found"
```bash
# تثبيت Android Studio
# إعداد Android SDK
flutter doctor --android-licenses
```

### مشكلة: "Gradle build failed"
```bash
# تنظيف المشروع
flutter clean
cd android
./gradlew clean
cd ..
flutter pub get
```

### مشكلة: "Firebase not configured"
```bash
# تأكد من وجود google-services.json
# تأكد من إضافة plugin في android/app/build.gradle:
# apply plugin: 'com.google.gms.google-services'
```

---

## 📦 أحجام APK المتوقعة

| النوع | الحجم التقريبي |
|-------|----------------|
| **النسخة التجريبية** | ~15 MB |
| **النسخة الكاملة** | ~25 MB |
| **مع ProGuard** | ~18 MB |

---

## 🔐 إعدادات الأمان للإنتاج

### 1. **توقيع التطبيق**
```bash
# إنشاء keystore
keytool -genkey -v -keystore el-farhan-key.jks -keyalg RSA -keysize 2048 -validity 10000 -alias elfarhan

# إعداد android/key.properties
storePassword=your-password
keyPassword=your-password
keyAlias=elfarhan
storeFile=../el-farhan-key.jks
```

### 2. **تحسين الحجم**
```bash
# بناء مع تحسين
flutter build apk --release --shrink --obfuscate --split-debug-info=debug-info/
```

### 3. **اختبار APK**
```bash
# تثبيت على جهاز
adb install build/app/outputs/flutter-apk/app-release.apk

# فحص الأداء
flutter analyze
flutter test
```

---

## 📱 نشر على متجر Google Play

### 1. **إعداد App Bundle**
```bash
flutter build appbundle --release
```

### 2. **رفع على Play Console**
- اذهب إلى https://play.google.com/console
- أنشئ تطبيق جديد
- ارفع app-release.aab

### 3. **معلومات التطبيق**
- **الاسم**: آل فرحان للنقل الخفيف
- **الوصف**: تطبيق إدارة شامل لأعمال النقل الخفيف
- **الفئة**: الأعمال
- **التقييم**: للجميع

---

## 🆘 الدعم

إذا واجهت أي مشاكل:

1. **تحقق من flutter doctor**
2. **راجع ملفات التكوين**
3. **تأكد من إعدادات Firebase**
4. **جرب تنظيف المشروع**

**ملاحظة**: النسخة التجريبية تعمل بدون إنترنت وتحتوي على بيانات وهمية للاختبار فقط.
