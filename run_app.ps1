Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    تشغيل تطبيق آل فرحان على الهاتف" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Flutter
Write-Host "[1/6] فحص Flutter..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version 2>&1
    Write-Host "Flutter متاح ✓" -ForegroundColor Green
} catch {
    Write-Host "خطأ: Flutter غير مثبت" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "[2/6] فحص الأجهزة المتصلة..." -ForegroundColor Yellow

# Check ADB devices
try {
    $adbDevices = adb devices 2>&1
    Write-Host "ADB Devices:" -ForegroundColor Cyan
    Write-Host $adbDevices
} catch {
    Write-Host "تحذير: ADB غير متاح" -ForegroundColor Yellow
}

# Check Flutter devices
try {
    $flutterDevices = flutter devices 2>&1
    Write-Host "Flutter Devices:" -ForegroundColor Cyan
    Write-Host $flutterDevices
} catch {
    Write-Host "خطأ في فحص الأجهزة" -ForegroundColor Red
}

Write-Host ""
Write-Host "[3/6] تنظيف المشروع..." -ForegroundColor Yellow
try {
    flutter clean
    Write-Host "تم تنظيف المشروع ✓" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تنظيف المشروع" -ForegroundColor Red
}

Write-Host ""
Write-Host "[4/6] تحميل التبعيات..." -ForegroundColor Yellow
try {
    flutter pub get
    Write-Host "تم تحميل التبعيات ✓" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تحميل التبعيات" -ForegroundColor Red
}

Write-Host ""
Write-Host "[5/6] فحص الأخطاء..." -ForegroundColor Yellow
try {
    $analyzeResult = flutter analyze --no-fatal-infos 2>&1
    Write-Host $analyzeResult
} catch {
    Write-Host "تحذير: فشل في فحص الأخطاء" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "[6/6] تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "ملاحظة: تأكد من أن هاتفك متصل ومفعل عليه USB Debugging" -ForegroundColor Cyan
Write-Host ""

try {
    flutter run --debug
} catch {
    Write-Host "خطأ في تشغيل التطبيق" -ForegroundColor Red
    Write-Host "تحقق من:" -ForegroundColor Yellow
    Write-Host "1. الهاتف متصل بكابل USB" -ForegroundColor White
    Write-Host "2. USB Debugging مفعل" -ForegroundColor White
    Write-Host "3. تم قبول تصريح USB Debugging على الهاتف" -ForegroundColor White
}

Write-Host ""
Write-Host "انتهى!" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
