import 'package:cloud_firestore/cloud_firestore.dart';

class WarehouseModel {
  // ثوابت أنواع المخازن
  static const String typeMain = 'main';           // المخزن الرئيسي
  static const String typeShowroom = 'showroom';   // مخزن المعرض
  static const String typeAgent = 'agent';         // مخزن الوكيل

  // قائمة الأنواع المتاحة
  static const List<String> warehouseTypes = [
    typeMain,
    typeShowroom,
    typeAgent,
  ];

  // أسماء الأنواع بالعربية
  static const Map<String, String> typeNames = {
    typeMain: 'المخزن الرئيسي',
    typeShowroom: 'مخزن المعرض',
    typeAgent: 'مخزن الوكيل',
  };
  final String id;
  final String name;
  final String type; // main (المخزن الرئيسي), showroom (مخزن المعرض), agent (مخزن الوكيل)
  final String? ownerId; // User ID for agent warehouses
  final String address;
  final String? phone;
  final String? email;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalData;

  WarehouseModel({
    required this.id,
    required this.name,
    required this.type,
    this.ownerId,
    required this.address,
    this.phone,
    this.email,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.additionalData,
  });

  // Convert from Firestore document
  factory WarehouseModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WarehouseModel(
      id: doc.id,
      name: data['name'] ?? '',
      type: data['type'] ?? '',
      ownerId: data['ownerId'],
      address: data['address'] ?? '',
      phone: data['phone'],
      email: data['email'],
      isActive: _parseBool(data['isActive']) ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      additionalData: data['additionalData'],
    );
  }

  // Convert from Map (for local database)
  factory WarehouseModel.fromMap(Map<String, dynamic> map) {
    return WarehouseModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: map['type'] ?? '',
      ownerId: map['ownerId'],
      address: map['address'] ?? '',
      phone: map['phone'],
      email: map['email'],
      isActive: _parseBool(map['isActive']) ?? true,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      additionalData: map['additionalData'] != null 
          ? Map<String, dynamic>.from(map['additionalData']) 
          : null,
    );
  }

  // Helper method to parse bool from various formats
  static bool? _parseBool(dynamic value) {
    if (value == null) return null;

    if (value is bool) {
      return value;
    } else if (value is int) {
      return value == 1;
    } else if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1') return true;
      if (lowerValue == 'false' || lowerValue == '0') return false;
    }

    return null;
  }

  // Convert to Map (for Firestore and local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'ownerId': ownerId,
      'address': address,
      'phone': phone,
      'email': email,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'type': type,
      'ownerId': ownerId,
      'address': address,
      'phone': phone,
      'email': email,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  WarehouseModel copyWith({
    String? id,
    String? name,
    String? type,
    String? ownerId,
    String? address,
    String? phone,
    String? email,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return WarehouseModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      ownerId: ownerId ?? this.ownerId,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isMainWarehouse => type == typeMain;
  bool get isShowroomWarehouse => type == typeShowroom;
  bool get isAgentWarehouse => type == typeAgent;

  // الحصول على اسم النوع بالعربية
  String get typeNameArabic => typeNames[type] ?? type;

  // التحقق من صحة نوع المخزن
  bool get isValidType => warehouseTypes.contains(type);

  // التحقق من إمكانية استقبال البضاعة
  bool get canReceiveGoods => isMainWarehouse;

  // التحقق من إمكانية البيع
  bool get canSellGoods => isShowroomWarehouse || isAgentWarehouse;

  // التحقق من الحاجة لمالك (الوكلاء فقط)
  bool get requiresOwner => isAgentWarehouse;

  @override
  String toString() {
    return 'WarehouseModel(id: $id, name: $name, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WarehouseModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
