# إعداد المستخدم الأساسي في Firebase - تطبيق آل فرحان

## 🔥 **الحل النهائي: استخدام Firebase للمستخدمين**

### ✅ **المزايا:**
- لا مشاكل في قاعدة البيانات المحلية
- مزامنة تلقائية بين جميع الأجهزة
- أمان عالي مع Firebase Authentication
- سهولة إدارة المستخدمين

---

## 📋 **الطريقة الأولى: إضافة المستخدم يدوياً (الأسهل)**

### **الخطوة 1: إضافة المستخدم في Firebase Authentication**

1. **اذهب إلى [Firebase Console](https://console.firebase.google.com)**
2. **اختر مشروعك**
3. **اذهب إلى Authentication → Users**
4. **اضغط "Add user"**
5. **أدخل البيانات:**
   ```
   Email: <EMAIL>
   Password: admin123
   ```
6. **اضغط "Add user"**

### **الخطوة 2: إضافة بيانات المستخدم في Firestore**

1. **اذهب إلى Firestore Database**
2. **اضغط "Start collection"**
3. **اسم المجموعة:** `users`
4. **Document ID:** `admin_001`
5. **أضف الحقول التالية:**

```json
{
  "id": "admin_001",
  "username": "ahmed",
  "email": "<EMAIL>",
  "fullName": "أحمد محمد - المدير الأعلى",
  "phone": "01234567890",
  "role": "super_admin",
  "warehouseId": null,
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

---

## 🚀 **الطريقة الثانية: إنشاء تلقائي من التطبيق**

### **تم تحديث الكود ليقوم بـ:**
1. فحص Firebase للمستخدمين الموجودين
2. إذا لم يوجد أي مستخدمين، ينشئ المستخدم الأساسي تلقائياً
3. إضافة المستخدم في Authentication و Firestore معاً

### **عند تشغيل التطبيق لأول مرة:**
```
I/flutter: Users already exist in Firebase. Skipping admin user creation.
```
أو
```
I/flutter: === DEFAULT ADMIN USER CREATED IN FIREBASE ===
I/flutter: Email: <EMAIL>
I/flutter: Password: admin123
I/flutter: Username: ahmed
I/flutter: Role: Super Admin
```

---

## 👤 **بيانات تسجيل الدخول:**

```
اسم المستخدم: ahmed
كلمة المرور: admin123
```

أو

```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
```

---

## 🔧 **إعدادات Firebase Security Rules**

### **للـ Firestore:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // Other collections
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### **للـ Firebase Storage:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

---

## 🎯 **اختبار النظام:**

### **1. تشغيل التطبيق:**
```bash
flutter run
```

### **2. تسجيل الدخول:**
- أدخل: `ahmed` / `admin123`
- يجب أن يعمل تسجيل الدخول بنجاح

### **3. فحص Firebase:**
- تحقق من وجود المستخدم في Authentication
- تحقق من وجود البيانات في Firestore

---

## 🛠️ **استكشاف الأخطاء:**

### **مشكلة: "اسم المستخدم غير موجود"**
```
الحل: تأكد من إضافة المستخدم في Firestore مع username = "ahmed"
```

### **مشكلة: "كلمة المرور غير صحيحة"**
```
الحل: تأكد من إضافة المستخدم في Authentication مع نفس كلمة المرور
```

### **مشكلة: "Firebase connection failed"**
```
الحل: 
1. تأكد من إعدادات google-services.json
2. تأكد من اتصال الإنترنت
3. تأكد من تفعيل Authentication في Firebase Console
```

---

## 📱 **إضافة مستخدمين جدد من التطبيق:**

### **بعد تسجيل الدخول كمدير أعلى:**
1. اذهب إلى **"إدارة المستخدمين"**
2. اضغط **"إضافة مستخدم جديد"**
3. أدخل البيانات المطلوبة
4. اختر الدور (مدير إداري، وكيل، معرض)
5. احفظ المستخدم

### **سيتم إنشاء المستخدم في:**
- ✅ Firebase Authentication
- ✅ Firestore Database
- ✅ قاعدة البيانات المحلية (للعمل بدون إنترنت)

---

## 🔐 **أنواع المستخدمين:**

### **super_admin (المدير الأعلى):**
- إدارة جميع المستخدمين
- إدارة المخزون الكامل
- عرض جميع التقارير
- إدارة إعدادات النظام

### **admin (المدير الإداري):**
- إدارة المخزون
- إنشاء فواتير البيع
- عرض التقارير
- إدارة حسابات الوكلاء

### **agent (الوكيل):**
- إدارة مخزنه الخاص
- إنشاء فواتير البيع
- عرض حسابه الشخصي

### **showroom (المعرض):**
- إدارة مخزن المعرض
- إنشاء فواتير البيع

---

## 🎉 **النتيجة النهائية:**

**نظام مستخدمين متكامل وآمن باستخدام Firebase! 🚀**

### **المزايا:**
- ✅ لا مشاكل في قاعدة البيانات المحلية
- ✅ مزامنة تلقائية بين الأجهزة
- ✅ أمان عالي مع Firebase
- ✅ سهولة إدارة المستخدمين
- ✅ العمل بدون إنترنت (مع المزامنة لاحقاً)

**التطبيق جاهز للتوزيع والاستخدام! 🎊**
