import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/advanced_report_service.dart';
import '../../services/data_service.dart';
import '../../widgets/loading_widget.dart';

class ComprehensiveReportsScreen extends StatefulWidget {
  const ComprehensiveReportsScreen({super.key});

  @override
  State<ComprehensiveReportsScreen> createState() => _ComprehensiveReportsScreenState();
}

class _ComprehensiveReportsScreenState extends State<ComprehensiveReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final AdvancedReportService _reportService = AdvancedReportService.instance;
  final DataService _dataService = DataService.instance;

  // Data
  List<UserModel> _agents = [];
  List<WarehouseModel> _warehouses = [];
  
  // Loading states
  bool _isLoading = true;
  bool _isGeneratingReport = false;
  
  // Filter variables
  DateTimeRange? _selectedDateRange;
  String? _selectedAgentId;
  String? _selectedWarehouseId;
  String _selectedStatus = 'all';
  
  // Report data
  Map<String, dynamic> _salesReportData = {};
  Map<String, dynamic> _inventoryReportData = {};
  Map<String, dynamic> _agentPerformanceData = {};
  Map<String, dynamic> _profitLossData = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final agents = await _dataService.getUsers();
      final warehouses = await _dataService.getWarehouses();
      
      setState(() {
        _agents = agents.where((user) => user.role == 'agent').toList();
        _warehouses = warehouses;
        _isLoading = false;
      });

      // Load default reports
      await _generateAllReports();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  Future<void> _generateAllReports() async {
    setState(() {
      _isGeneratingReport = true;
    });

    try {
      final futures = await Future.wait([
        _reportService.generateSalesReport(
          startDate: _selectedDateRange?.start,
          endDate: _selectedDateRange?.end,
          agentId: _selectedAgentId,
          warehouseId: _selectedWarehouseId,
          status: _selectedStatus,
        ),
        _reportService.generateInventoryReport(
          warehouseId: _selectedWarehouseId,
        ),
        _reportService.generateAgentPerformanceReport(
          startDate: _selectedDateRange?.start,
          endDate: _selectedDateRange?.end,
          agentId: _selectedAgentId,
        ),
        _reportService.generateProfitLossReport(
          startDate: _selectedDateRange?.start,
          endDate: _selectedDateRange?.end,
        ),
      ]);

      setState(() {
        _salesReportData = futures[0];
        _inventoryReportData = futures[1];
        _agentPerformanceData = futures[2];
        _profitLossData = futures[3];
        _isGeneratingReport = false;
      });
    } catch (e) {
      setState(() {
        _isGeneratingReport = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء التقارير: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingWidget(message: 'جاري تحميل البيانات...'),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير الشاملة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_alt),
            onPressed: _showFilterDialog,
            tooltip: 'فلترة التقارير',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateAllReports,
            tooltip: 'تحديث التقارير',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.trending_up), text: 'المبيعات'),
            Tab(icon: Icon(Icons.inventory), text: 'المخزون'),
            Tab(icon: Icon(Icons.people), text: 'الوكلاء'),
            Tab(icon: Icon(Icons.account_balance), text: 'الأرباح'),
          ],
        ),
      ),
      body: _isGeneratingReport
          ? const LoadingWidget(message: 'جاري إنشاء التقارير...')
          : TabBarView(
              controller: _tabController,
              children: [
                _buildSalesReportTab(),
                _buildInventoryReportTab(),
                _buildAgentPerformanceTab(),
                _buildProfitLossTab(),
              ],
            ),
    );
  }

  Widget _buildSalesReportTab() {
    if (_salesReportData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات مبيعات'));
    }

    final summary = _salesReportData['summary'] as Map<String, dynamic>;
    final agentPerformance = _salesReportData['agentPerformance'] as List<dynamic>;
    final dailySales = _salesReportData['dailySales'] as List<dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards([
            _buildSummaryCard(
              'إجمالي الفواتير',
              '${summary['totalInvoices']}',
              Icons.receipt_long,
              Colors.blue,
            ),
            _buildSummaryCard(
              'إجمالي المبيعات',
              AppUtils.formatCurrency(summary['totalSales']),
              Icons.attach_money,
              Colors.green,
            ),
            _buildSummaryCard(
              'إجمالي الأرباح',
              AppUtils.formatCurrency(summary['totalProfit']),
              Icons.trending_up,
              Colors.orange,
            ),
            _buildSummaryCard(
              'متوسط البيع',
              AppUtils.formatCurrency(summary['averageSale']),
              Icons.analytics,
              Colors.purple,
            ),
          ]),

          const SizedBox(height: 24),

          // Sales Chart
          _buildSalesChart(dailySales),

          const SizedBox(height: 24),

          // Top Agents
          _buildTopAgentsSection(agentPerformance),

          const SizedBox(height: 24),

          // Export Buttons
          _buildExportButtons('sales'),
        ],
      ),
    );
  }

  Widget _buildInventoryReportTab() {
    if (_inventoryReportData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات مخزون'));
    }

    final summary = _inventoryReportData['summary'] as Map<String, dynamic>;
    final warehouseInventory = _inventoryReportData['warehouseInventory'] as List<dynamic>;
    final categoryInventory = _inventoryReportData['categoryInventory'] as List<dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards([
            _buildSummaryCard(
              'إجمالي الأصناف',
              '${summary['totalItems']}',
              Icons.inventory_2,
              Colors.blue,
            ),
            _buildSummaryCard(
              'قيمة المخزون',
              AppUtils.formatCurrency(summary['totalValue']),
              Icons.monetization_on,
              Colors.green,
            ),
            _buildSummaryCard(
              'أصناف منخفضة',
              '${summary['lowStockItems']}',
              Icons.warning,
              Colors.red,
            ),
            _buildSummaryCard(
              'نسبة النقص',
              '${summary['lowStockPercentage'].toStringAsFixed(1)}%',
              Icons.trending_down,
              Colors.orange,
            ),
          ]),

          const SizedBox(height: 24),

          // Warehouse Inventory
          _buildWarehouseInventorySection(warehouseInventory),

          const SizedBox(height: 24),

          // Category Breakdown
          _buildCategoryInventorySection(categoryInventory),

          const SizedBox(height: 24),

          // Export Buttons
          _buildExportButtons('inventory'),
        ],
      ),
    );
  }

  Widget _buildAgentPerformanceTab() {
    if (_agentPerformanceData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات أداء الوكلاء'));
    }

    final summary = _agentPerformanceData['summary'] as Map<String, dynamic>;
    final agentPerformance = _agentPerformanceData['agentPerformance'] as List<dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards([
            _buildSummaryCard(
              'إجمالي الوكلاء',
              '${summary['totalAgents']}',
              Icons.people,
              Colors.blue,
            ),
            _buildSummaryCard(
              'الوكلاء النشطون',
              '${summary['activeAgents']}',
              Icons.person_add,
              Colors.green,
            ),
            _buildSummaryCard(
              'إجمالي المبيعات',
              AppUtils.formatCurrency(summary['totalSales']),
              Icons.trending_up,
              Colors.orange,
            ),
            _buildSummaryCard(
              'متوسط المبيعات',
              AppUtils.formatCurrency(summary['averageSalesPerAgent']),
              Icons.analytics,
              Colors.purple,
            ),
          ]),

          const SizedBox(height: 24),

          // Agent Performance List
          _buildAgentPerformanceList(agentPerformance),

          const SizedBox(height: 24),

          // Export Buttons
          _buildExportButtons('agents'),
        ],
      ),
    );
  }

  Widget _buildProfitLossTab() {
    if (_profitLossData.isEmpty) {
      return const Center(child: Text('لا توجد بيانات أرباح وخسائر'));
    }

    final summary = _profitLossData['summary'] as Map<String, dynamic>;
    final monthlyBreakdown = _profitLossData['monthlyBreakdown'] as Map<String, dynamic>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards([
            _buildSummaryCard(
              'إجمالي الإيرادات',
              AppUtils.formatCurrency(summary['totalRevenue']),
              Icons.attach_money,
              Colors.green,
            ),
            _buildSummaryCard(
              'إجمالي التكلفة',
              AppUtils.formatCurrency(summary['totalCost']),
              Icons.money_off,
              Colors.red,
            ),
            _buildSummaryCard(
              'الربح الإجمالي',
              AppUtils.formatCurrency(summary['grossProfit']),
              Icons.trending_up,
              Colors.blue,
            ),
            _buildSummaryCard(
              'هامش الربح',
              '${summary['grossProfitMargin'].toStringAsFixed(1)}%',
              Icons.percent,
              Colors.purple,
            ),
          ]),

          const SizedBox(height: 24),

          // Monthly Breakdown Chart
          _buildMonthlyProfitChart(monthlyBreakdown),

          const SizedBox(height: 24),

          // Export Buttons
          _buildExportButtons('profit_loss'),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(List<Widget> cards) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: cards,
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(51)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesChart(List<dynamic> dailySales) {
    if (dailySales.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'اتجاه المبيعات اليومية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: true),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 60,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          AppUtils.formatCurrency(value),
                          style: const TextStyle(fontSize: 10),
                        );
                      },
                    ),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      getTitlesWidget: (value, meta) {
                        if (value.toInt() < dailySales.length) {
                          final date = dailySales[value.toInt()]['date'] as String;
                          return Text(
                            date.substring(5), // Show MM-dd
                            style: const TextStyle(fontSize: 10),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: true),
                lineBarsData: [
                  LineChartBarData(
                    spots: dailySales.asMap().entries.map((entry) {
                      return FlSpot(
                        entry.key.toDouble(),
                        (entry.value['totalSales'] as double),
                      );
                    }).toList(),
                    isCurved: true,
                    color: Colors.blue,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopAgentsSection(List<dynamic> agentPerformance) {
    final topAgents = agentPerformance.take(5).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'أفضل 5 وكلاء',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...topAgents.map((agent) => _buildAgentPerformanceItem(agent)).toList(),
        ],
      ),
    );
  }
}
