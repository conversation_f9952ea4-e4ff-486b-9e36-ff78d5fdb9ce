# إضافة المستخدم الأساسي يدوياً في Firebase

## 🚨 **المشكلة الحالية:**
```
I/flutter: Username sign in error: اسم المستخدم غير موجود
```

**السبب:** المستخدم موجود في Firebase Authentication لكن بياناته غير محفوظة في Firestore بشكل صحيح.

---

## 🔧 **الحل: إضافة المستخدم يدوياً**

### **الخطوة 1: فتح Firebase Console**

1. **اذهب إلى:** [https://console.firebase.google.com](https://console.firebase.google.com)
2. **اختر مشروعك**
3. **تأكد من أن المستخدم موجود في Authentication**

### **الخطوة 2: إضافة بيانات المستخدم في Firestore**

#### **أ. اذهب إلى Firestore Database:**
1. **من القائمة الجانبية:** Firestore Database
2. **إذا لم تكن قد أنشأت قاعدة بيانات:** اضغط "Create database"
3. **اختر:** Start in test mode (للتطوير)
4. **اختر المنطقة:** أقرب منطقة لك

#### **ب. إنشاء مجموعة users:**
1. **اضغط:** "Start collection"
2. **Collection ID:** `users`
3. **اضغط:** Next

#### **ج. إضافة المستخدم الأساسي:**
1. **Document ID:** `admin_001`
2. **أضف الحقول التالية:**

```
Field: id
Type: string
Value: admin_001

Field: username  
Type: string
Value: ahmed

Field: email
Type: string
Value: <EMAIL>

Field: fullName
Type: string
Value: أحمد محمد - المدير الأعلى

Field: phone
Type: string
Value: 01234567890

Field: role
Type: string
Value: super_admin

Field: warehouseId
Type: null
Value: null

Field: isActive
Type: boolean
Value: true

Field: createdAt
Type: string
Value: 2024-01-01T00:00:00.000Z

Field: updatedAt
Type: string
Value: 2024-01-01T00:00:00.000Z
```

3. **اضغط:** Save

---

## 📱 **الخطوة 3: اختبار تسجيل الدخول**

### **بيانات تسجيل الدخول:**
```
اسم المستخدم: ahmed
كلمة المرور: admin123
```

### **أو:**
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
```

---

## 🔍 **التحقق من الإعداد:**

### **في Firebase Console:**

#### **Authentication → Users:**
```
✅ يجب أن تجد: <EMAIL>
```

#### **Firestore → users → admin_001:**
```
✅ يجب أن تجد جميع الحقول المذكورة أعلاه
✅ تأكد من أن username = "ahmed"
✅ تأكد من أن role = "super_admin"
```

---

## 🛡️ **إعدادات الأمان (Security Rules):**

### **للـ Firestore:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### **للـ Storage:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

---

## 🎯 **اختبار النظام:**

### **1. إعادة تشغيل التطبيق:**
```bash
flutter run
```

### **2. تسجيل الدخول:**
- أدخل: `ahmed` / `admin123`
- يجب أن يعمل بنجاح الآن

### **3. رسائل Console المتوقعة:**
```
I/flutter: User signed in: أحمد محمد - المدير الأعلى
```

---

## 🚨 **استكشاف الأخطاء:**

### **مشكلة: "اسم المستخدم غير موجود"**
```
✅ تأكد من وجود document في users مع username = "ahmed"
✅ تأكد من صحة اسم المجموعة "users"
✅ تأكد من إعدادات Security Rules
```

### **مشكلة: "كلمة المرور غير صحيحة"**
```
✅ تأكد من وجود المستخدم في Authentication
✅ تأكد من كلمة المرور: admin123
✅ جرب إعادة تعيين كلمة المرور من Authentication
```

### **مشكلة: "Firebase connection failed"**
```
✅ تأكد من اتصال الإنترنت
✅ تأكد من إعدادات google-services.json
✅ تأكد من تفعيل Authentication و Firestore
```

---

## 📋 **نموذج JSON كامل للمستخدم:**

```json
{
  "id": "admin_001",
  "username": "ahmed",
  "email": "<EMAIL>", 
  "fullName": "أحمد محمد - المدير الأعلى",
  "phone": "01234567890",
  "role": "super_admin",
  "warehouseId": null,
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

---

## 🎉 **بعد الإعداد الصحيح:**

### **ستتمكن من:**
- ✅ تسجيل الدخول بنجاح
- ✅ الوصول لجميع وظائف التطبيق
- ✅ إضافة مستخدمين جدد
- ✅ إدارة المخزون والفواتير
- ✅ عرض التقارير الشاملة

**بعد إتمام هذه الخطوات، التطبيق سيعمل بشكل مثالي! 🚀**
