import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/warehouse_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../services/enhanced_pdf_service.dart';

class WarehouseMovementReportsScreen extends StatefulWidget {
  const WarehouseMovementReportsScreen({super.key});

  @override
  State<WarehouseMovementReportsScreen> createState() => _WarehouseMovementReportsScreenState();
}

class _WarehouseMovementReportsScreenState extends State<WarehouseMovementReportsScreen>
    with SingleTickerProviderStateMixin {
  final DataService _dataService = DataService.instance;
  
  late TabController _tabController;
  
  List<WarehouseModel> _warehouses = [];
  final List<Map<String, dynamic>> _transferHistory = [];
  final List<Map<String, dynamic>> _inventoryMovements = [];
  
  bool _isLoading = false;
  
  // Filters
  DateTimeRange? _selectedDateRange;
  String? _selectedWarehouseId;
  String _selectedMovementType = 'all'; // all, in, out, transfer
  
  // Statistics
  Map<String, dynamic> _statistics = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load warehouses
      _warehouses = await _dataService.getAllWarehouses();

      // Load transfer history
      await _loadTransferHistory();

      // Load inventory movements
      await _loadInventoryMovements();

      // Calculate statistics
      await _calculateStatistics();

      if (kDebugMode) {
        print('✅ Loaded warehouse movement data:');
        print('   Warehouses: ${_warehouses.length}');
        print('   Transfers: ${_transferHistory.length}');
        print('   Movements: ${_inventoryMovements.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading warehouse movement data: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTransferHistory() async {
    try {
      // For now, create sample transfer data based on transferred items
      final transferredItems = await _dataService.getItems(status: 'transferred');
      _transferHistory.clear();

      for (final item in transferredItems) {
        // Create a sample transfer record
        final fromWarehouse = _warehouses.isNotEmpty ? _warehouses.first : null;
        final toWarehouse = _warehouses.length > 1 ? _warehouses[1] : null;

        if (fromWarehouse != null && toWarehouse != null) {
          _transferHistory.add({
            'id': '${item.id}_transfer',
            'itemId': item.id,
            'itemBrand': item.brand,
            'itemModel': item.model,
            'itemChassisNumber': item.chassisNumber,
            'fromWarehouseId': fromWarehouse.id,
            'fromWarehouseName': fromWarehouse.name,
            'toWarehouseId': toWarehouse.id,
            'toWarehouseName': toWarehouse.name,
            'transferDate': item.updatedAt,
            'transferredBy': item.createdBy,
            'notes': 'تحويل تلقائي',
            'status': 'completed',
          });
        }
      }

      // Sort by date (newest first)
      _transferHistory.sort((a, b) =>
        (b['transferDate'] as DateTime).compareTo(a['transferDate'] as DateTime)
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading transfer history: $e');
      }
    }
  }

  Future<void> _loadInventoryMovements() async {
    try {
      // Get all items and create movement records
      final allItems = await _dataService.getItems();
      _inventoryMovements.clear();

      for (final item in allItems) {
        // Add initial inventory entry
        final warehouse = _warehouses.firstWhere(
          (w) => w.id == item.currentWarehouseId,
          orElse: () => WarehouseModel(
            id: item.currentWarehouseId,
            name: 'مخزن غير معروف',
            type: 'unknown',
            address: '',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        _inventoryMovements.add({
          'id': '${item.id}_initial',
          'itemId': item.id,
          'itemBrand': item.brand,
          'itemModel': item.model,
          'itemChassisNumber': item.chassisNumber,
          'warehouseId': item.currentWarehouseId,
          'warehouseName': warehouse.name,
          'movementType': 'in',
          'movementDate': item.createdAt,
          'description': 'إدخال أولي للمخزن',
          'status': item.status,
        });

        // Add sale movement if sold
        if (item.status == 'sold') {
          _inventoryMovements.add({
            'id': '${item.id}_sold',
            'itemId': item.id,
            'itemBrand': item.brand,
            'itemModel': item.model,
            'itemChassisNumber': item.chassisNumber,
            'warehouseId': item.currentWarehouseId,
            'warehouseName': warehouse.name,
            'movementType': 'out',
            'movementDate': item.updatedAt,
            'description': 'بيع للعميل',
            'status': 'sold',
          });
        }
      }

      // Sort by date (newest first)
      _inventoryMovements.sort((a, b) =>
        (b['movementDate'] as DateTime).compareTo(a['movementDate'] as DateTime)
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading inventory movements: $e');
      }
    }
  }

  Future<void> _calculateStatistics() async {
    try {
      _statistics = {
        'totalWarehouses': _warehouses.length,
        'totalTransfers': _transferHistory.length,
        'totalMovements': _inventoryMovements.length,
        'warehouseStats': <String, Map<String, dynamic>>{},
      };

      // Calculate per-warehouse statistics
      for (final warehouse in _warehouses) {
        final warehouseMovements = _inventoryMovements.where(
          (movement) => movement['warehouseId'] == warehouse.id
        ).toList();

        final inMovements = warehouseMovements.where(
          (movement) => movement['movementType'] == 'in'
        ).length;

        final outMovements = warehouseMovements.where(
          (movement) => movement['movementType'] == 'out'
        ).length;

        final currentItems = await _dataService.getWarehouseItems(warehouse.id);

        _statistics['warehouseStats'][warehouse.id] = {
          'name': warehouse.name,
          'totalMovements': warehouseMovements.length,
          'inMovements': inMovements,
          'outMovements': outMovements,
          'currentItems': currentItems.length,
          'netMovement': inMovements - outMovements,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error calculating statistics: $e');
      }
    }
  }

  List<Map<String, dynamic>> get _filteredTransfers {
    return _transferHistory.where((transfer) {
      // Filter by date range
      if (_selectedDateRange != null) {
        final transferDate = transfer['transferDate'] as DateTime;
        if (transferDate.isBefore(_selectedDateRange!.start) ||
            transferDate.isAfter(_selectedDateRange!.end.add(const Duration(days: 1)))) {
          return false;
        }
      }

      // Filter by warehouse
      if (_selectedWarehouseId != null) {
        if (transfer['fromWarehouseId'] != _selectedWarehouseId &&
            transfer['toWarehouseId'] != _selectedWarehouseId) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  List<Map<String, dynamic>> get _filteredMovements {
    return _inventoryMovements.where((movement) {
      // Filter by date range
      if (_selectedDateRange != null) {
        final movementDate = movement['movementDate'] as DateTime;
        if (movementDate.isBefore(_selectedDateRange!.start) ||
            movementDate.isAfter(_selectedDateRange!.end.add(const Duration(days: 1)))) {
          return false;
        }
      }

      // Filter by warehouse
      if (_selectedWarehouseId != null) {
        if (movement['warehouseId'] != _selectedWarehouseId) {
          return false;
        }
      }

      // Filter by movement type
      if (_selectedMovementType != 'all') {
        if (movement['movementType'] != _selectedMovementType) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير حركة المخازن'),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _isLoading ? null : _exportToPDF,
            tooltip: 'تصدير PDF',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الإحصائيات'),
            Tab(text: 'أذونات التحويل'),
            Tab(text: 'حركة المخزون'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildStatisticsTab(),
                _buildTransfersTab(),
                _buildMovementsTab(),
              ],
            ),
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Statistics
          _buildOverallStatistics(),
          const SizedBox(height: AppConstants.defaultPadding),

          // Warehouse Statistics
          _buildWarehouseStatistics(),
        ],
      ),
    );
  }

  Widget _buildOverallStatistics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات عامة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المخازن',
                    '${_statistics['totalWarehouses'] ?? 0}',
                    Icons.warehouse,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي التحويلات',
                    '${_statistics['totalTransfers'] ?? 0}',
                    Icons.swap_horiz,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الحركات',
                    '${_statistics['totalMovements'] ?? 0}',
                    Icons.move_up,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'المخازن النشطة',
                    '${_warehouses.length}',
                    Icons.business,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(76)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseStatistics() {
    final warehouseStats = _statistics['warehouseStats'] as Map<String, Map<String, dynamic>>? ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات المخازن',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (warehouseStats.isEmpty)
              const Center(
                child: Text('لا توجد بيانات إحصائية'),
              )
            else
              ...warehouseStats.entries.map((entry) => _buildWarehouseStatItem(entry.value)),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseStatItem(Map<String, dynamic> stats) {
    final name = stats['name'] as String;
    final totalMovements = stats['totalMovements'] as int;
    final inMovements = stats['inMovements'] as int;
    final outMovements = stats['outMovements'] as int;
    final currentItems = stats['currentItems'] as int;
    final netMovement = stats['netMovement'] as int;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildMiniStat('إجمالي الحركات', '$totalMovements', Colors.blue),
                ),
                Expanded(
                  child: _buildMiniStat('الداخل', '$inMovements', Colors.green),
                ),
                Expanded(
                  child: _buildMiniStat('الخارج', '$outMovements', Colors.red),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildMiniStat('الأصناف الحالية', '$currentItems', Colors.purple),
                ),
                Expanded(
                  child: _buildMiniStat(
                    'صافي الحركة',
                    '$netMovement',
                    netMovement >= 0 ? Colors.green : Colors.red
                  ),
                ),
                const Expanded(child: SizedBox()), // Empty space
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMiniStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTransfersTab() {
    return Column(
      children: [
        // Filters
        _buildFilters(),

        // Transfers List
        Expanded(
          child: _buildTransfersList(),
        ),
      ],
    );
  }

  Widget _buildMovementsTab() {
    return Column(
      children: [
        // Filters
        _buildFilters(showMovementType: true),

        // Movements List
        Expanded(
          child: _buildMovementsList(),
        ),
      ],
    );
  }

  Widget _buildFilters({bool showMovementType = false}) {
    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلترة البيانات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Only show warehouse filter for managers, not agents
                if (!Provider.of<AuthProvider>(context, listen: false).isAgent) ...[
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedWarehouseId,
                      decoration: const InputDecoration(
                        labelText: 'المخزن',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem(value: null, child: Text('جميع المخازن')),
                        ..._warehouses.map((warehouse) => DropdownMenuItem(
                          value: warehouse.id,
                          child: Text(warehouse.name),
                        )),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedWarehouseId = value;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                if (showMovementType)
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedMovementType,
                      decoration: const InputDecoration(
                        labelText: 'نوع الحركة',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
                        DropdownMenuItem(value: 'in', child: Text('داخل')),
                        DropdownMenuItem(value: 'out', child: Text('خارج')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedMovementType = value ?? 'all';
                        });
                      },
                    ),
                  ),
                if (!showMovementType) const Expanded(child: SizedBox()),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _selectDateRange,
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _selectedDateRange == null
                          ? 'اختيار فترة'
                          : '${AppUtils.formatDate(_selectedDateRange!.start)} - ${AppUtils.formatDate(_selectedDateRange!.end)}',
                    ),
                  ),
                ),
              ],
            ),
            if (_selectedDateRange != null || _selectedWarehouseId != null ||
                (showMovementType && _selectedMovementType != 'all')) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    showMovementType
                      ? 'عدد الحركات: ${_filteredMovements.length}'
                      : 'عدد التحويلات: ${_filteredTransfers.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح الفلاتر'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedDateRange = null;
      _selectedWarehouseId = null;
      _selectedMovementType = 'all';
    });
  }

  Widget _buildTransfersList() {
    final transfers = _filteredTransfers;

    if (transfers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.swap_horiz, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد تحويلات تطابق الفلاتر المحددة',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: transfers.length,
      itemBuilder: (context, index) {
        final transfer = transfers[index];
        return _buildTransferCard(transfer);
      },
    );
  }

  Widget _buildTransferCard(Map<String, dynamic> transfer) {
    final transferDate = transfer['transferDate'] as DateTime;
    final status = transfer['status'] as String;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${transfer['itemBrand']} ${transfer['itemModel']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(status).withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(status),
                    style: TextStyle(
                      color: _getStatusColor(status),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('رقم الشاسيه: ${transfer['itemChassisNumber']}'),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.arrow_forward, color: Colors.grey[600], size: 16),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'من: ${transfer['fromWarehouseName']} → إلى: ${transfer['toWarehouseName']}',
                    style: TextStyle(color: Colors.grey[600]),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.grey[600], size: 16),
                const SizedBox(width: 4),
                Text(
                  AppUtils.formatDate(transferDate),
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const Spacer(),
                if (transfer['notes'] != null && transfer['notes'].isNotEmpty)
                  Tooltip(
                    message: transfer['notes'],
                    child: Icon(Icons.note, color: Colors.grey[600], size: 16),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMovementsList() {
    final movements = _filteredMovements;

    if (movements.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.move_up, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد حركات تطابق الفلاتر المحددة',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: movements.length,
      itemBuilder: (context, index) {
        final movement = movements[index];
        return _buildMovementCard(movement);
      },
    );
  }

  Widget _buildMovementCard(Map<String, dynamic> movement) {
    final movementDate = movement['movementDate'] as DateTime;
    final movementType = movement['movementType'] as String;
    final isIncoming = movementType == 'in';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: isIncoming ? Colors.green.withAlpha(51) : Colors.red.withAlpha(51),
                  child: Icon(
                    isIncoming ? Icons.arrow_downward : Icons.arrow_upward,
                    size: 16,
                    color: isIncoming ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '${movement['itemBrand']} ${movement['itemModel']}',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isIncoming ? Colors.green.withAlpha(51) : Colors.red.withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isIncoming ? 'داخل' : 'خارج',
                    style: TextStyle(
                      color: isIncoming ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('رقم الشاسيه: ${movement['itemChassisNumber']}'),
            Text('المخزن: ${movement['warehouseName']}'),
            Text('الوصف: ${movement['description']}'),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.grey[600], size: 16),
                const SizedBox(width: 4),
                Text(
                  AppUtils.formatDate(movementDate),
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  Future<void> _exportToPDF() async {
    try {
      // Show loading
      if (mounted) {
        AppUtils.showSnackBar(context, 'جاري إنشاء تقرير PDF...');
      }

      // Generate PDF using enhanced service
      final pdfBytes = await EnhancedPdfService.instance.generateWarehouseMovementPDF(
        warehouses: _warehouses,
        transfers: _transferHistory,
        movements: _inventoryMovements,
        statistics: _statistics,
      );

      // Share PDF
      await EnhancedPdfService.instance.saveAndSharePDF(
        pdfBytes,
        'تقرير_حركة_المخازن_${AppUtils.formatDate(DateTime.now())}.pdf',
      );

      if (mounted) {
        AppUtils.showSnackBar(context, '✅ تم تصدير التقرير بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting PDF: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تصدير PDF: $e', isError: true);
      }
    }
  }
}
