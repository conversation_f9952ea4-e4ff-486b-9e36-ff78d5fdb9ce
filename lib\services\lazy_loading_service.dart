import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../models/invoice_model.dart';
import '../models/user_model.dart';
import '../models/inventory_item_model.dart';
import '../models/warehouse_model.dart';
import 'data_service.dart';
import 'advanced_cache_service.dart';
import 'performance_service.dart';

class LazyLoadingService {
  static final LazyLoadingService _instance = LazyLoadingService._internal();
  factory LazyLoadingService() => _instance;
  LazyLoadingService._internal();

  static LazyLoadingService get instance => _instance;

  final DataService _dataService = DataService.instance;
  final AdvancedCacheService _cacheService = AdvancedCacheService.instance;
  final PerformanceService _performanceService = PerformanceService.instance;

  // Loading states
  final Map<String, bool> _loadingStates = {};
  final Map<String, Completer<void>> _loadingCompleters = {};

  // Pagination
  static const int _defaultPageSize = 20;
  final Map<String, int> _currentPages = {};
  final Map<String, bool> _hasMoreData = {};

  // Preloading queues
  final Set<String> _preloadQueue = {};
  final Set<String> _criticalResources = {};

  /// Initialize lazy loading service
  void initialize() {
    _setupCriticalResources();
    _startPreloadingCriticalResources();
    
    if (kDebugMode) {
      print('⚡ Lazy loading service initialized');
    }
  }

  /// Setup critical resources that should be preloaded
  void _setupCriticalResources() {
    _criticalResources.addAll([
      'users',
      'warehouses',
      'recent_invoices',
    ]);
  }

  /// Start preloading critical resources
  void _startPreloadingCriticalResources() {
    Timer(const Duration(milliseconds: 500), () {
      for (final resource in _criticalResources) {
        _preloadQueue.add(resource);
      }
      _processPreloadQueue();
    });
  }

  /// Process preload queue
  Future<void> _processPreloadQueue() async {
    while (_preloadQueue.isNotEmpty) {
      final resource = _preloadQueue.first;
      _preloadQueue.remove(resource);
      
      try {
        await _preloadResource(resource);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error preloading $resource: $e');
        }
      }
      
      // Small delay to avoid blocking UI
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// Preload specific resource
  Future<void> _preloadResource(String resource) async {
    switch (resource) {
      case 'users':
        await loadUsers(preload: true);
        break;
      case 'warehouses':
        await loadWarehouses(preload: true);
        break;
      case 'recent_invoices':
        await loadInvoices(page: 0, pageSize: 10, preload: true);
        break;
    }
  }

  /// Load users with lazy loading
  Future<List<UserModel>> loadUsers({
    int page = 0,
    int pageSize = _defaultPageSize,
    bool preload = false,
  }) async {
    final cacheKey = 'users_page_$page';
    
    if (!preload) {
      _performanceService.startScreenLoad('users_$page');
    }

    try {
      // Check cache first
      final cachedData = await _cacheService.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final users = cachedData.map((data) => UserModel.fromMap(data)).toList();
        if (!preload) {
          _performanceService.endScreenLoad('users_$page');
        }
        return users;
      }

      // Prevent duplicate loading
      if (_loadingStates[cacheKey] == true) {
        await _loadingCompleters[cacheKey]?.future;
        return await loadUsers(page: page, pageSize: pageSize, preload: preload);
      }

      _loadingStates[cacheKey] = true;
      final completer = Completer<void>();
      _loadingCompleters[cacheKey] = completer;

      // Load from data service
      final allUsers = await _dataService.getUsers();
      
      // Paginate
      final startIndex = page * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, allUsers.length);
      final pageUsers = allUsers.sublist(startIndex, endIndex);
      
      // Update pagination state
      _currentPages['users'] = page;
      _hasMoreData['users'] = endIndex < allUsers.length;

      // Cache the data
      final userData = pageUsers.map((user) => user.toMap()).toList();
      await _cacheService.cacheUserData('users_page_$page', {'users': userData});

      completer.complete();
      _loadingStates[cacheKey] = false;
      _loadingCompleters.remove(cacheKey);

      if (!preload) {
        _performanceService.endScreenLoad('users_$page');
      }

      if (kDebugMode) {
        print('👥 Loaded ${pageUsers.length} users (page $page)');
      }

      return pageUsers;
    } catch (e) {
      _loadingStates[cacheKey] = false;
      _loadingCompleters[cacheKey]?.completeError(e);
      _loadingCompleters.remove(cacheKey);
      
      if (!preload) {
        _performanceService.endScreenLoad('users_$page');
      }
      
      rethrow;
    }
  }

  /// Load invoices with lazy loading
  Future<List<InvoiceModel>> loadInvoices({
    int page = 0,
    int pageSize = _defaultPageSize,
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
    bool preload = false,
  }) async {
    final cacheKey = 'invoices_${page}_${agentId ?? 'all'}_${startDate?.toIso8601String() ?? ''}_${endDate?.toIso8601String() ?? ''}';
    
    if (!preload) {
      _performanceService.startScreenLoad('invoices_$page');
    }

    try {
      // Check cache first
      final cachedData = await _cacheService.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final invoices = cachedData.map((data) => InvoiceModel.fromMap(data)).toList();
        if (!preload) {
          _performanceService.endScreenLoad('invoices_$page');
        }
        return invoices;
      }

      // Prevent duplicate loading
      if (_loadingStates[cacheKey] == true) {
        await _loadingCompleters[cacheKey]?.future;
        return await loadInvoices(
          page: page,
          pageSize: pageSize,
          agentId: agentId,
          startDate: startDate,
          endDate: endDate,
          preload: preload,
        );
      }

      _loadingStates[cacheKey] = true;
      final completer = Completer<void>();
      _loadingCompleters[cacheKey] = completer;

      // Load from data service
      var allInvoices = await _dataService.getInvoices();
      
      // Apply filters
      if (agentId != null) {
        allInvoices = allInvoices.where((invoice) => invoice.agentId == agentId).toList();
      }
      
      if (startDate != null && endDate != null) {
        allInvoices = allInvoices.where((invoice) {
          return invoice.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
                 invoice.createdAt.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();
      }

      // Sort by date (newest first)
      allInvoices.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      // Paginate
      final startIndex = page * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, allInvoices.length);
      final pageInvoices = allInvoices.sublist(startIndex, endIndex);
      
      // Update pagination state
      _currentPages['invoices'] = page;
      _hasMoreData['invoices'] = endIndex < allInvoices.length;

      // Cache the data
      final invoiceData = pageInvoices.map((invoice) => invoice.toMap()).toList();
      await _cacheService.cacheInvoiceData(cacheKey, {'invoices': invoiceData});

      completer.complete();
      _loadingStates[cacheKey] = false;
      _loadingCompleters.remove(cacheKey);

      if (!preload) {
        _performanceService.endScreenLoad('invoices_$page');
      }

      if (kDebugMode) {
        print('📄 Loaded ${pageInvoices.length} invoices (page $page)');
      }

      return pageInvoices;
    } catch (e) {
      _loadingStates[cacheKey] = false;
      _loadingCompleters[cacheKey]?.completeError(e);
      _loadingCompleters.remove(cacheKey);
      
      if (!preload) {
        _performanceService.endScreenLoad('invoices_$page');
      }
      
      rethrow;
    }
  }

  /// Load inventory items with lazy loading
  Future<List<InventoryItemModel>> loadInventoryItems({
    int page = 0,
    int pageSize = _defaultPageSize,
    String? warehouseId,
    String? category,
    bool preload = false,
  }) async {
    final cacheKey = 'inventory_${page}_${warehouseId ?? 'all'}_${category ?? 'all'}';
    
    if (!preload) {
      _performanceService.startScreenLoad('inventory_$page');
    }

    try {
      // Check cache first
      final cachedData = await _cacheService.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final items = cachedData.map((data) => InventoryItemModel.fromMap(data)).toList();
        if (!preload) {
          _performanceService.endScreenLoad('inventory_$page');
        }
        return items;
      }

      // Prevent duplicate loading
      if (_loadingStates[cacheKey] == true) {
        await _loadingCompleters[cacheKey]?.future;
        return await loadInventoryItems(
          page: page,
          pageSize: pageSize,
          warehouseId: warehouseId,
          category: category,
          preload: preload,
        );
      }

      _loadingStates[cacheKey] = true;
      final completer = Completer<void>();
      _loadingCompleters[cacheKey] = completer;

      // Load from data service
      var allItems = await _dataService.getInventoryItems();
      
      // Apply filters
      if (warehouseId != null) {
        allItems = allItems.where((item) => item.warehouseId == warehouseId).toList();
      }
      
      if (category != null && category != 'all') {
        allItems = allItems.where((item) => item.category == category).toList();
      }

      // Sort by name
      allItems.sort((a, b) => a.name.compareTo(b.name));
      
      // Paginate
      final startIndex = page * pageSize;
      final endIndex = (startIndex + pageSize).clamp(0, allItems.length);
      final pageItems = allItems.sublist(startIndex, endIndex);
      
      // Update pagination state
      _currentPages['inventory'] = page;
      _hasMoreData['inventory'] = endIndex < allItems.length;

      // Cache the data
      final itemData = pageItems.map((item) => item.toMap()).toList();
      await _cacheService.cacheInventoryData(itemData);

      completer.complete();
      _loadingStates[cacheKey] = false;
      _loadingCompleters.remove(cacheKey);

      if (!preload) {
        _performanceService.endScreenLoad('inventory_$page');
      }

      if (kDebugMode) {
        print('📦 Loaded ${pageItems.length} inventory items (page $page)');
      }

      return pageItems;
    } catch (e) {
      _loadingStates[cacheKey] = false;
      _loadingCompleters[cacheKey]?.completeError(e);
      _loadingCompleters.remove(cacheKey);
      
      if (!preload) {
        _performanceService.endScreenLoad('inventory_$page');
      }
      
      rethrow;
    }
  }

  /// Load warehouses with lazy loading
  Future<List<WarehouseModel>> loadWarehouses({bool preload = false}) async {
    const cacheKey = 'warehouses_all';
    
    if (!preload) {
      _performanceService.startScreenLoad('warehouses');
    }

    try {
      // Check cache first
      final cachedData = await _cacheService.getCachedData<List<dynamic>>(cacheKey);
      if (cachedData != null) {
        final warehouses = cachedData.map((data) => WarehouseModel.fromMap(data)).toList();
        if (!preload) {
          _performanceService.endScreenLoad('warehouses');
        }
        return warehouses;
      }

      // Prevent duplicate loading
      if (_loadingStates[cacheKey] == true) {
        await _loadingCompleters[cacheKey]?.future;
        return await loadWarehouses(preload: preload);
      }

      _loadingStates[cacheKey] = true;
      final completer = Completer<void>();
      _loadingCompleters[cacheKey] = completer;

      // Load from data service
      final warehouses = await _dataService.getWarehouses();

      // Cache the data
      final warehouseData = warehouses.map((warehouse) => warehouse.toMap()).toList();
      await _cacheService.cacheData(
        cacheKey,
        warehouseData,
        strategy: CacheStrategy.memoryAndDisk,
        priority: CachePriority.high,
      );

      completer.complete();
      _loadingStates[cacheKey] = false;
      _loadingCompleters.remove(cacheKey);

      if (!preload) {
        _performanceService.endScreenLoad('warehouses');
      }

      if (kDebugMode) {
        print('🏪 Loaded ${warehouses.length} warehouses');
      }

      return warehouses;
    } catch (e) {
      _loadingStates[cacheKey] = false;
      _loadingCompleters[cacheKey]?.completeError(e);
      _loadingCompleters.remove(cacheKey);
      
      if (!preload) {
        _performanceService.endScreenLoad('warehouses');
      }
      
      rethrow;
    }
  }

  /// Check if more data is available for pagination
  bool hasMoreData(String dataType) {
    return _hasMoreData[dataType] ?? false;
  }

  /// Get current page for data type
  int getCurrentPage(String dataType) {
    return _currentPages[dataType] ?? 0;
  }

  /// Reset pagination for data type
  void resetPagination(String dataType) {
    _currentPages[dataType] = 0;
    _hasMoreData[dataType] = true;
  }

  /// Check if data is currently loading
  bool isLoading(String cacheKey) {
    return _loadingStates[cacheKey] ?? false;
  }

  /// Preload next page
  void preloadNextPage(String dataType) {
    final currentPage = getCurrentPage(dataType);
    if (hasMoreData(dataType)) {
      _preloadQueue.add('${dataType}_page_${currentPage + 1}');
      _processPreloadQueue();
    }
  }

  /// Clear all loading states
  void clearLoadingStates() {
    _loadingStates.clear();
    _loadingCompleters.clear();
    _currentPages.clear();
    _hasMoreData.clear();
    _preloadQueue.clear();
    
    if (kDebugMode) {
      print('🧹 Lazy loading states cleared');
    }
  }

  /// Dispose service
  void dispose() {
    clearLoadingStates();
    
    if (kDebugMode) {
      print('🧹 Lazy loading service disposed');
    }
  }
}
