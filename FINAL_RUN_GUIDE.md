# الدليل النهائي لتشغيل تطبيق آل فرحان 🚀

## 📱 **خطوات التشغيل المضمونة**

---

### **🔧 الخطوة 1: إعداد الهاتف (مهم جداً!)**

#### **تفعيل خيارات المطور:**
1. اذه<PERSON> إلى `الإعدادات` → `حول الهاتف`
2. اضغط على `رقم البناء` **7 مرات متتالية**
3. ستظهر رسالة "أصبحت مطوراً"

#### **تفعيل USB Debugging:**
1. اذه<PERSON> إلى `الإعدادات` → `خيارات المطور`
2. فعّل `USB debugging` ✅
3. فعّل `Install via USB` ✅
4. فعّل `USB Debugging (Security settings)` إذا كان متاحاً ✅

#### **توصيل الهاتف:**
1. استخدم **كابل USB أصلي** (مهم!)
2. وصل الهاتف بالكمبيوتر
3. اختر `نقل الملفات (MTP)` من إشعارات الهاتف
4. **اقبل تصريح USB Debugging** عند ظهوره
5. اختر `Always allow from this computer` ✅

---

### **💻 الخطوة 2: فتح Command Prompt**

1. **اضغط** `Windows + R`
2. **اكتب** `cmd` واضغط `Enter`
3. **انسخ والصق هذا الأمر:**
   ```cmd
   cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"
   ```

---

### **🔍 الخطوة 3: التحقق من الاتصال**

**انسخ والصق هذا الأمر:**
```cmd
flutter devices
```

#### **النتيجة المطلوبة:**
```
Found 1 connected device:
  SM-XXXXX (mobile) • XXXXXXXXX • android-arm64 • Android XX (API XX)
```

#### **إذا لم تر هاتفك:**
```cmd
adb devices
```

**يجب أن ترى:**
```
List of devices attached
XXXXXXXXX    device
```

#### **إذا ظهر "unauthorized":**
- تحقق من قبول تصريح USB Debugging على الهاتف
- افصل وأعد توصيل الكابل

---

### **🧹 الخطوة 4: تنظيف وإعداد المشروع**

**انسخ والصق هذه الأوامر واحداً تلو الآخر:**

```cmd
flutter clean
```
*انتظر حتى ينتهي...*

```cmd
flutter pub get
```
*انتظر حتى ينتهي...*

```cmd
flutter analyze
```
*تحقق من عدم وجود أخطاء...*

---

### **🚀 الخطوة 5: تشغيل التطبيق**

**انسخ والصق هذا الأمر:**
```cmd
flutter run --debug
```

#### **ما ستراه:**
```
Launching lib\main.dart on SM-XXXXX in debug mode...
Running Gradle task 'assembleDebug'...
✓ Built build\app\outputs\flutter-apk\app-debug.apk.
Installing build\app\outputs\flutter-apk\app-debug.apk...
Syncing files to device SM-XXXXX...
Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h Repeat this help message.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

💪 Running with sound null safety 💪

An Observatory debugger and profiler on SM-XXXXX is available at: http://127.0.0.1:XXXXX/
The Flutter DevTools debugger and profiler on SM-XXXXX is available at: http://127.0.0.1:XXXXX/
```

---

## 🎯 **معلومات تسجيل الدخول**

**عند فتح التطبيق:**
- **اسم المستخدم:** `ahmed`
- **كلمة المرور:** `admin123`

---

## 🚨 **حل المشاكل السريع**

### **مشكلة: "No devices found"**
```cmd
adb kill-server
adb start-server
flutter devices
```

### **مشكلة: "Gradle build failed"**
```cmd
cd android
gradlew clean
cd ..
flutter clean
flutter pub get
flutter run
```

### **مشكلة: "Permission denied"**
- تحقق من قبول تصريح USB Debugging
- جرب كابل USB مختلف
- أعد تشغيل الهاتف

---

## 📦 **بديل: بناء APK وتثبيته**

**إذا لم يعمل التشغيل المباشر:**

### **1. بناء APK:**
```cmd
flutter build apk --debug
```

### **2. تثبيت APK:**
```cmd
adb install build\app\outputs\flutter-apk\app-debug.apk
```

### **3. أو نسخ APK للهاتف:**
- الملف موجود في: `build\app\outputs\flutter-apk\app-debug.apk`
- انسخه للهاتف وثبته يدوياً

---

## ✅ **قائمة فحص نهائية**

**تأكد من:**
- [ ] الهاتف متصل بكابل USB جيد
- [ ] USB Debugging مفعل ومقبول
- [ ] الهاتف يظهر في `flutter devices`
- [ ] اتصال بالإنترنت متاح
- [ ] مساحة كافية على الهاتف (1 جيجا)
- [ ] Flutter مثبت ويعمل بشكل صحيح

---

## 🎉 **ما ستراه عند النجاح**

1. **شاشة التحميل** بلوجو المؤسسة وصورة خلفية مخصصة
2. **شاشة تسجيل الدخول** مع لوجو المؤسسة
3. **الشاشة الرئيسية** مع:
   - لوجو المؤسسة في الأعلى
   - إحصائيات المخزون
   - قائمة جانبية مع لوجو المؤسسة
   - جميع المميزات تعمل

---

## 📞 **الدعم الفني**

**إذا واجهت أي مشاكل:**
- **المطور:** معتصم سالم
- **واتساب:** 01062606098

**أرسل لي:**
- لقطة شاشة من رسالة الخطأ
- نتيجة أمر `flutter doctor -v`
- نتيجة أمر `flutter devices`

---

## 🔄 **الأوامر السريعة (انسخ والصق)**

```cmd
cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"
flutter devices
flutter clean
flutter pub get
flutter run --debug
```

---

## 🎯 **الهدف النهائي**

**رؤية التطبيق يعمل على هاتفك مع:**
- ✅ لوجو المؤسسة في جميع الشاشات
- ✅ شاشة تحميل مخصصة
- ✅ جميع المميزات تعمل بسلاسة
- ✅ تسجيل دخول ناجح
- ✅ إدارة المخزون والمبيعات

**التطبيق جاهز للاستخدام الفعلي!** 🚀

---

## 📋 **ملاحظات مهمة**

1. **المرة الأولى** قد تستغرق 5-10 دقائق للبناء
2. **Hot Reload** يعمل بعد التشغيل الأول (اضغط `r`)
3. **البيانات محفوظة محلياً** ومتزامنة مع Firebase
4. **النسخ الاحتياطي** يعمل تلقائياً

**استمتع بالتطبيق!** 🎉
