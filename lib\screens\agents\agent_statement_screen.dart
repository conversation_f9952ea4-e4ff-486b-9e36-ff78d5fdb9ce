import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_account_model.dart';
import '../../models/user_model.dart';
import '../../services/data_service.dart';

class AgentStatementScreen extends StatefulWidget {
  final UserModel agent;

  const AgentStatementScreen({
    super.key,
    required this.agent,
  });

  @override
  State<AgentStatementScreen> createState() => _AgentStatementScreenState();
}

class _AgentStatementScreenState extends State<AgentStatementScreen> {
  bool _isLoading = true;
  AgentAccountModel? _agentAccount;
  List<AgentTransaction> _filteredTransactions = [];
  String _selectedFilter = 'all';
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _loadAgentAccount();
  }

  Future<void> _loadAgentAccount() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dataService = Provider.of<DataService>(context, listen: false);
      final account = await dataService.getAgentAccount(widget.agent.id);

      if (mounted) {
        setState(() {
          _agentAccount = account;
          _filteredTransactions = account?.transactions ?? [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading agent account: $e');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        AppUtils.showSnackBar(context, 'خطأ في تحميل بيانات الحساب: $e', isError: true);
      }
    }
  }

  void _applyFilters() {
    if (_agentAccount == null) return;

    List<AgentTransaction> transactions = List.from(_agentAccount!.transactions);

    // Filter by type
    if (_selectedFilter != 'all') {
      transactions = transactions.where((t) => t.type == _selectedFilter).toList();
    }

    // Filter by date range
    if (_selectedDateRange != null) {
      transactions = transactions.where((t) {
        return t.timestamp.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               t.timestamp.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Sort by date (newest first)
    transactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    setState(() {
      _filteredTransactions = transactions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كشف حساب ${widget.agent.fullName}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _agentAccount != null ? _exportToPDF : null,
            tooltip: 'تصدير PDF',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAgentAccount,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _agentAccount == null
              ? _buildNoDataWidget()
              : Column(
                  children: [
                    _buildAccountSummary(),
                    _buildFilters(),
                    Expanded(child: _buildTransactionsList()),
                  ],
                ),
    );
  }

  Widget _buildNoDataWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.account_balance_wallet, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'لا توجد بيانات حساب لهذا الوكيل',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSummary() {
    if (_agentAccount == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الحساب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المديونية',
                    _agentAccount!.totalDebt,
                    Colors.red,
                    Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المدفوع',
                    _agentAccount!.totalPaid,
                    Colors.green,
                    Icons.payment,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildSummaryCard(
              'الرصيد الحالي',
              _agentAccount!.currentBalance,
              _agentAccount!.currentBalance >= 0 ? Colors.green : Colors.red,
              _agentAccount!.currentBalance >= 0 ? Icons.account_balance : Icons.warning,
              isFullWidth: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    double amount,
    Color color,
    IconData icon, {
    bool isFullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            AppUtils.formatCurrency(amount.abs()),
            style: TextStyle(
              fontSize: isFullWidth ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلترة المعاملات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedFilter,
                    decoration: const InputDecoration(
                      labelText: 'نوع المعاملة',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('جميع المعاملات')),
                      DropdownMenuItem(value: 'sale', child: Text('مبيعات')),
                      DropdownMenuItem(value: 'payment', child: Text('دفعات')),
                      DropdownMenuItem(value: 'transfer', child: Text('تحويلات')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedFilter = value ?? 'all';
                      });
                      _applyFilters();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _selectDateRange,
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _selectedDateRange == null
                          ? 'اختيار فترة'
                          : '${AppUtils.formatDate(_selectedDateRange!.start)} - ${AppUtils.formatDate(_selectedDateRange!.end)}',
                    ),
                  ),
                ),
              ],
            ),
            if (_selectedDateRange != null || _selectedFilter != 'all') ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    'عدد المعاملات: ${_filteredTransactions.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح الفلاتر'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList() {
    if (_filteredTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد معاملات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _selectedFilter != 'all' || _selectedDateRange != null
                  ? 'جرب تغيير الفلاتر'
                  : 'لم يتم تسجيل أي معاملات بعد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _filteredTransactions[index];
        return _buildTransactionCard(transaction, index);
      },
    );
  }

  Widget _buildTransactionCard(AgentTransaction transaction, int index) {
    final isDebit = transaction.type == 'sale' || transaction.type == 'transfer';
    final color = isDebit ? Colors.red : Colors.green;
    final icon = _getTransactionIcon(transaction.type);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          transaction.description,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppUtils.formatDateTime(transaction.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            if (transaction.metadata != null && transaction.metadata!.isNotEmpty)
              Text(
                _getTransactionDetails(transaction),
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[500],
                ),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${isDebit ? '-' : '+'}${AppUtils.formatCurrency(transaction.amount)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              _getTransactionTypeText(transaction.type),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        onTap: () => _showTransactionDetails(transaction),
      ),
    );
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'sale':
        return Icons.shopping_cart;
      case 'payment':
        return Icons.payment;
      case 'transfer':
        return Icons.swap_horiz;
      default:
        return Icons.receipt;
    }
  }

  String _getTransactionTypeText(String type) {
    switch (type) {
      case 'sale':
        return 'مبيعة';
      case 'payment':
        return 'دفعة';
      case 'transfer':
        return 'تحويل';
      default:
        return 'معاملة';
    }
  }

  String _getTransactionDetails(AgentTransaction transaction) {
    final metadata = transaction.metadata ?? {};
    List<String> details = [];

    if (metadata['invoiceNumber'] != null) {
      details.add('فاتورة: ${metadata['invoiceNumber']}');
    }
    if (metadata['paymentMethod'] != null) {
      details.add('طريقة الدفع: ${metadata['paymentMethod']}');
    }
    if (metadata['notes'] != null && metadata['notes'].toString().isNotEmpty) {
      details.add('ملاحظات: ${metadata['notes']}');
    }

    return details.join(' • ');
  }

  void _showTransactionDetails(AgentTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${_getTransactionTypeText(transaction.type)}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('المبلغ', AppUtils.formatCurrency(transaction.amount)),
              _buildDetailRow('التاريخ', AppUtils.formatDateTime(transaction.timestamp)),
              _buildDetailRow('الوصف', transaction.description),
              _buildDetailRow('النوع', _getTransactionTypeText(transaction.type)),
              if (transaction.metadata != null) ...[
                const Divider(),
                const Text(
                  'تفاصيل إضافية:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...transaction.metadata!.entries.map((entry) =>
                    _buildDetailRow(entry.key, entry.value.toString())),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _applyFilters();
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedFilter = 'all';
      _selectedDateRange = null;
    });
    _applyFilters();
  }

  Future<void> _exportToPDF() async {
    try {
      // TODO: Implement PDF export
      AppUtils.showSnackBar(context, 'سيتم إضافة تصدير PDF قريباً');
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في تصدير PDF: $e', isError: true);
    }
  }
}
