import 'package:cloud_firestore/cloud_firestore.dart';

class ItemModel {
  final String id; // This will be the motor fingerprint text (OCR result)
  final String type; // نوع المركبة (موتوسيكل، تروسيكل، سكوتر، توكتوك)
  final String model; // الموديل
  final String color; // اللون
  final String brand; // الماركة
  final String countryOfOrigin; // بلد المنشأ
  final int yearOfManufacture; // سنة الصنع
  final double purchasePrice; // سعر الشراء الأساسي
  final double suggestedSellingPrice; // سعر البيع المقترح
  final String motorFingerprintImageUrl; // رابط صورة بصمة الموتور
  final String motorFingerprintText; // النص المستخرج من بصمة الموتور (OCR)
  final String chassisImageUrl; // رابط صورة رقم الشاسيه
  final String chassisNumber; // النص المستخرج من رقم الشاسيه (OCR)
  final String currentWarehouseId; // المخزن الحالي للصنف
  final String status; // available, sold, transferred, returned
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy; // User ID who added this item
  final Map<String, dynamic>? additionalData;

  ItemModel({
    required this.id,
    required this.type,
    required this.model,
    required this.color,
    required this.brand,
    required this.countryOfOrigin,
    required this.yearOfManufacture,
    required this.purchasePrice,
    required this.suggestedSellingPrice,
    required this.motorFingerprintImageUrl,
    required this.motorFingerprintText,
    required this.chassisImageUrl,
    required this.chassisNumber,
    required this.currentWarehouseId,
    this.status = 'متاح',
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.additionalData,
  });

  // Convert from Firestore document
  factory ItemModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ItemModel(
      id: doc.id,
      type: data['type'] ?? '',
      model: data['model'] ?? '',
      color: data['color'] ?? '',
      brand: data['brand'] ?? '',
      countryOfOrigin: data['countryOfOrigin'] ?? '',
      yearOfManufacture: data['yearOfManufacture'] ?? 0,
      purchasePrice: (data['purchasePrice'] ?? 0).toDouble(),
      suggestedSellingPrice: (data['suggestedSellingPrice'] ?? 0).toDouble(),
      motorFingerprintImageUrl: data['motorFingerprintImageUrl'] ?? '',
      motorFingerprintText: data['motorFingerprintText'] ?? '',
      chassisImageUrl: data['chassisImageUrl'] ?? '',
      chassisNumber: data['chassisNumber'] ?? '',
      currentWarehouseId: data['currentWarehouseId'] ?? '',
      status: data['status'] ?? 'متاح',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      additionalData: data['additionalData'],
    );
  }

  // Convert from Map (for local database)
  factory ItemModel.fromMap(Map<String, dynamic> map) {
    return ItemModel(
      id: map['id'] ?? '',
      type: map['type'] ?? '',
      model: map['model'] ?? '',
      color: map['color'] ?? '',
      brand: map['brand'] ?? '',
      countryOfOrigin: map['countryOfOrigin'] ?? '',
      yearOfManufacture: map['yearOfManufacture'] ?? 0,
      purchasePrice: (map['purchasePrice'] ?? 0).toDouble(),
      suggestedSellingPrice: (map['suggestedSellingPrice'] ?? 0).toDouble(),
      motorFingerprintImageUrl: map['motorFingerprintImageUrl'] ?? '',
      motorFingerprintText: map['motorFingerprintText'] ?? '',
      chassisImageUrl: map['chassisImageUrl'] ?? '',
      chassisNumber: map['chassisNumber'] ?? '',
      currentWarehouseId: map['currentWarehouseId'] ?? '',
      status: map['status'] ?? 'متاح',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      createdBy: map['createdBy'] ?? '',
      additionalData: map['additionalData'] != null 
          ? Map<String, dynamic>.from(map['additionalData']) 
          : null,
    );
  }

  // Convert to Map (for Firestore and local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'model': model,
      'color': color,
      'brand': brand,
      'countryOfOrigin': countryOfOrigin,
      'yearOfManufacture': yearOfManufacture,
      'purchasePrice': purchasePrice,
      'suggestedSellingPrice': suggestedSellingPrice,
      'motorFingerprintImageUrl': motorFingerprintImageUrl,
      'motorFingerprintText': motorFingerprintText,
      'chassisImageUrl': chassisImageUrl,
      'chassisNumber': chassisNumber,
      'currentWarehouseId': currentWarehouseId,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'type': type,
      'model': model,
      'color': color,
      'brand': brand,
      'countryOfOrigin': countryOfOrigin,
      'yearOfManufacture': yearOfManufacture,
      'purchasePrice': purchasePrice,
      'suggestedSellingPrice': suggestedSellingPrice,
      'motorFingerprintImageUrl': motorFingerprintImageUrl,
      'motorFingerprintText': motorFingerprintText,
      'chassisImageUrl': chassisImageUrl,
      'chassisNumber': chassisNumber,
      'currentWarehouseId': currentWarehouseId,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  ItemModel copyWith({
    String? id,
    String? type,
    String? model,
    String? color,
    String? brand,
    String? countryOfOrigin,
    int? yearOfManufacture,
    double? purchasePrice,
    double? suggestedSellingPrice,
    String? motorFingerprintImageUrl,
    String? motorFingerprintText,
    String? chassisImageUrl,
    String? chassisNumber,
    String? currentWarehouseId,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? additionalData,
  }) {
    return ItemModel(
      id: id ?? this.id,
      type: type ?? this.type,
      model: model ?? this.model,
      color: color ?? this.color,
      brand: brand ?? this.brand,
      countryOfOrigin: countryOfOrigin ?? this.countryOfOrigin,
      yearOfManufacture: yearOfManufacture ?? this.yearOfManufacture,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      suggestedSellingPrice: suggestedSellingPrice ?? this.suggestedSellingPrice,
      motorFingerprintImageUrl: motorFingerprintImageUrl ?? this.motorFingerprintImageUrl,
      motorFingerprintText: motorFingerprintText ?? this.motorFingerprintText,
      chassisImageUrl: chassisImageUrl ?? this.chassisImageUrl,
      chassisNumber: chassisNumber ?? this.chassisNumber,
      currentWarehouseId: currentWarehouseId ?? this.currentWarehouseId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isAvailable => status == 'متاح' || status == 'available';
  bool get isSold => status == 'مباع' || status == 'sold';
  bool get isTransferred => status == 'محول' || status == 'transferred';
  bool get isReturned => status == 'مرتجع' || status == 'returned';

  String get displayName => '$brand $model ($color)';
  
  double get profitAmount => suggestedSellingPrice - purchasePrice;
  double get profitPercentage => purchasePrice > 0 ? (profitAmount / purchasePrice) * 100 : 0;

  @override
  String toString() {
    return 'ItemModel(id: $id, type: $type, brand: $brand, model: $model, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
