# تعليمات تسجيل الدخول - تطبيق آل فرحان

## 👤 **المستخدم الأساسي (المدير الأعلى)**

### 🔑 **بيانات تسجيل الدخول:**
```
اسم المستخدم: ahmed
كلمة المرور: admin123
الدور: مدير أعلى (Super Admin)
```

---

## 🚀 **كيفية تسجيل الدخول:**

### 1. **افتح التطبيق على جهازك**
- ستظهر شاشة تسجيل الدخول

### 2. **أدخل البيانات:**
- **اسم المستخدم:** `ahmed`
- **كلمة المرور:** `admin123`

### 3. **اضغط "تسجيل الدخول"**

---

## ✅ **ما تم إعداده:**

### 🔧 **إنشاء المستخدم تلقائياً:**
- يتم إنشاء المستخدم الأساسي تلقائياً عند تشغيل التطبيق لأول مرة
- البيانات محفوظة في قاعدة البيانات المحلية
- لا يحتاج اتصال بالإنترنت للتسجيل

### 📊 **صلاحيات المدير الأعلى:**
- ✅ إدارة جميع المستخدمين
- ✅ إدارة المخزون الكامل
- ✅ إنشاء فواتير البيع
- ✅ تتبع الجوابات
- ✅ إدارة حسابات الوكلاء
- ✅ عرض جميع التقارير
- ✅ إدارة إعدادات النظام

---

## 🎯 **بعد تسجيل الدخول:**

### **ستصل إلى الشاشة الرئيسية وتجد:**
1. **إدارة المخزون** - إضافة وعرض الموتورات
2. **إنشاء فاتورة** - بيع الموتورات
3. **تتبع الجوابات** - متابعة حالة الأوراق
4. **إدارة المستخدمين** - إضافة وكلاء ومستخدمين
5. **التقارير** - تقارير شاملة عن الأعمال
6. **إدارة الحسابات** - حسابات الوكلاء والمدفوعات

---

## 🔧 **إضافة مستخدمين جدد:**

### **بعد تسجيل الدخول كمدير أعلى:**
1. اذهب إلى **"إدارة المستخدمين"**
2. اضغط **"إضافة مستخدم جديد"**
3. أدخل البيانات المطلوبة
4. اختر الدور (مدير إداري، وكيل، معرض)
5. احفظ المستخدم

### **أنواع المستخدمين:**
- **مدير أعلى** - صلاحيات كاملة
- **مدير إداري** - إدارة المخزون والتقارير
- **وكيل** - إدارة مخزنه وحسابه
- **معرض** - إدارة معرضه فقط

---

## 🛠️ **استكشاف الأخطاء:**

### مشكلة: "اسم المستخدم غير موجود"
```
الحل: تأكد من كتابة "ahmed" بالضبط (بدون مسافات)
```

### مشكلة: "كلمة المرور غير صحيحة"
```
الحل: تأكد من كتابة "admin123" بالضبط (بدون مسافات)
```

### مشكلة: "type 'String' is not a subtype of type 'Map'"
```
الحل: تم إصلاح هذه المشكلة في التحديث الأخير
```

### مشكلة: "لا يمكن تسجيل الدخول"
```
الحل:
1. أعد تشغيل التطبيق
2. تأكد من إنشاء المستخدم الأساسي
3. تحقق من رسائل الخطأ في Console
4. تأكد من وجود جدول demo_passwords
```

---

## 📱 **اختبار الوظائف:**

### **بعد تسجيل الدخول بنجاح:**
1. **جرب إضافة موتور جديد**
2. **جرب إنشاء فاتورة بيع**
3. **جرب عرض التقارير**
4. **جرب إضافة مستخدم جديد**

---

## 🎉 **تهانينا!**

**إذا تمكنت من تسجيل الدخول بنجاح، فالتطبيق يعمل بشكل مثالي! 🚀**

### **الخطوات التالية:**
1. **استكشف جميع الوظائف**
2. **أضف بيانات تجريبية**
3. **اختبر العمل بدون إنترنت**
4. **جرب OCR لقراءة بصمة الموتور**

---

## 📞 **للدعم:**

إذا واجهت أي مشاكل:
1. تحقق من رسائل Console في Android Studio
2. تأكد من عمل قاعدة البيانات المحلية
3. جرب إعادة تشغيل التطبيق

**التطبيق جاهز للاستخدام الكامل! 🎊**
