# 🚀 دليل الإصلاح السريع - تطبيق آل فرحان

## ⚡ **إصلاح سريع لمشاكل التشغيل:**

### 🔧 **إذا كان التطبيق لا يعمل:**

#### **1. تنظيف شامل:**
```bash
# في PowerShell أو Command Prompt
cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"

# تنظيف Flutter
flutter clean

# تنظيف Gradle
cd android
gradlew clean
gradlew --stop
cd ..

# إعادة تحميل Dependencies
flutter pub get

# تشغيل التطبيق
flutter run
```

#### **2. إذا فشل Kotlin Daemon:**
```bash
# إيقاف جميع عمليات Gradle
taskkill /f /im java.exe
taskkill /f /im gradle.exe

# إعادة تشغيل
flutter run
```

#### **3. إذا فشل تحميل الخطوط:**
- تأكد من اتصال الإنترنت
- التطبيق سيستخدم خطوط النظام تلقائياً

---

## 🔑 **بيانات تسجيل الدخول:**

### **المدير الأعلى:**
```
اسم المستخدم: ahmed
كلمة المرور: admin123
```

**أو:**
```
اسم المستخدم: admin  
كلمة المرور: admin123
```

---

## 🐛 **حل المشاكل الشائعة:**

### **مشكلة: "Unable to find a font"**
**الحل**: تجاهل هذه الرسالة - التطبيق سيعمل بخطوط النظام

### **مشكلة: "RenderFlex overflowed"**
**الحل**: تم إصلاحها - أعد تشغيل التطبيق

### **مشكلة: "Kotlin compile daemon"**
**الحل**: 
```bash
cd android
gradlew --stop
cd ..
flutter clean
flutter run
```

### **مشكلة: "Firebase Auth Error"**
**الحل**: استخدم بيانات الدخول الصحيحة أعلاه

---

## 📱 **اختبار التطبيق:**

### **1. تسجيل الدخول:**
- افتح التطبيق
- أدخل: ahmed / admin123
- يجب أن تدخل للشاشة الرئيسية

### **2. اختبار الوظائف:**
- جرب فتح قائمة المستخدمين
- جرب إضافة صنف جديد
- جرب تصدير تقرير PDF

### **3. اختبار الخطوط:**
- تحقق من عرض النصوص العربية
- جرب تصدير PDF

---

## 🆘 **إذا استمرت المشاكل:**

### **إعادة تعيين كاملة:**
```bash
# حذف build folders
rmdir /s build
rmdir /s android\build
rmdir /s android\app\build

# إعادة البناء
flutter clean
flutter pub get
flutter run
```

### **فحص النظام:**
```bash
flutter doctor
flutter devices
```

---

## 📞 **الدعم الفني:**
**المطور**: Motasem Salem  
**WhatsApp**: 01062606098

---

## ✅ **تأكيد نجاح الإصلاحات:**

إذا رأيت هذه الرسائل في Terminal، فالتطبيق يعمل بشكل صحيح:

```
✅ Successfully loaded Noto Sans Arabic fonts
✅ Users synced
✅ Warehouses synced  
✅ Items synced
✅ Auto sync completed successfully
Super admin user already exists
All services initialized successfully
```

**بيانات الدخول الافتراضية:**
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123
- اسم المستخدم: ahmed أو admin
- كلمة المرور: admin123
