import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'firebase_service.dart';
import 'local_database_service.dart';
import '../models/user_model.dart';
import '../models/item_model.dart';
import '../models/warehouse_model.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';
import '../models/document_tracking_model.dart';

class SyncService {
  static SyncService? _instance;
  static SyncService get instance => _instance ??= SyncService._();
  
  SyncService._();

  final FirebaseService _firebaseService = FirebaseService.instance;
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  final Connectivity _connectivity = Connectivity();
  
  bool _isSyncing = false;
  Timer? _syncTimer;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Initialize sync service
  Future<void> initialize() async {
    try {
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
      
      // Start periodic sync
      _startPeriodicSync();
      
      if (kDebugMode) {
        print('Sync service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing sync service: $e');
      }
    }
  }

  // Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    final isConnected = results.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet);
    
    if (isConnected && !_isSyncing) {
      if (kDebugMode) {
        print('Connection restored, starting sync...');
      }
      syncAll();
    }
  }

  // Start periodic sync (every 5 minutes when online)
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (!_isSyncing) {
        syncAll();
      }
    });
  }

  // Check if device is online
  Future<bool> isOnline() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final isConnected = connectivityResults.any((result) => 
          result == ConnectivityResult.mobile || 
          result == ConnectivityResult.wifi ||
          result == ConnectivityResult.ethernet);
      
      if (isConnected) {
        // Double check with Firebase
        return await _firebaseService.isOnline();
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Sync all data
  Future<void> syncAll() async {
    if (_isSyncing) return;
    
    try {
      _isSyncing = true;
      
      if (!await isOnline()) {
        if (kDebugMode) {
          print('Device is offline, skipping sync');
        }
        return;
      }

      if (kDebugMode) {
        print('Starting full sync...');
      }

      // Sync in order of dependencies
      await syncUsers();
      await syncWarehouses();
      await syncItems();
      await syncInvoices();
      await syncPayments();
      await syncDocumentTracking();
      
      // Process sync queue for offline operations
      await processSyncQueue();

      if (kDebugMode) {
        print('Full sync completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during sync: $e');
      }
    } finally {
      _isSyncing = false;
    }
  }

  // Sync users
  Future<void> syncUsers() async {
    try {
      // Download from Firebase
      final snapshot = await _firebaseService.firestore.collection('users').get();
      
      for (final doc in snapshot.docs) {
        final user = UserModel.fromFirestore(doc);
        final userData = user.toMap();
        userData['syncStatus'] = 1; // Mark as synced
        
        await _localDb.insert('users', userData);
      }

      // Upload local changes
      final localUsers = await _localDb.query('users', where: 'syncStatus = ?', whereArgs: [0]);
      
      for (final userData in localUsers) {
        final user = UserModel.fromMap(userData);
        await _firebaseService.firestore.collection('users').doc(user.id).set(user.toFirestore());
        await _localDb.markAsSynced('users', user.id);
      }

      if (kDebugMode) {
        print('Users synced successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing users: $e');
      }
    }
  }

  // Sync warehouses
  Future<void> syncWarehouses() async {
    try {
      // Download from Firebase
      final snapshot = await _firebaseService.firestore.collection('warehouses').get();
      
      for (final doc in snapshot.docs) {
        final warehouse = WarehouseModel.fromFirestore(doc);
        final warehouseData = warehouse.toMap();
        warehouseData['syncStatus'] = 1; // Mark as synced
        
        await _localDb.insert('warehouses', warehouseData);
      }

      // Upload local changes
      final localWarehouses = await _localDb.query('warehouses', where: 'syncStatus = ?', whereArgs: [0]);
      
      for (final warehouseData in localWarehouses) {
        final warehouse = WarehouseModel.fromMap(warehouseData);
        await _firebaseService.firestore.collection('warehouses').doc(warehouse.id).set(warehouse.toFirestore());
        await _localDb.markAsSynced('warehouses', warehouse.id);
      }

      if (kDebugMode) {
        print('Warehouses synced successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing warehouses: $e');
      }
    }
  }

  // Sync items
  Future<void> syncItems() async {
    try {
      // Download from Firebase
      final snapshot = await _firebaseService.firestore.collection('items').get();
      
      for (final doc in snapshot.docs) {
        final item = ItemModel.fromFirestore(doc);
        final itemData = item.toMap();
        itemData['syncStatus'] = 1; // Mark as synced
        
        await _localDb.insert('items', itemData);
      }

      // Upload local changes
      final localItems = await _localDb.query('items', where: 'syncStatus = ?', whereArgs: [0]);
      
      for (final itemData in localItems) {
        final item = ItemModel.fromMap(itemData);
        await _firebaseService.firestore.collection('items').doc(item.id).set(item.toFirestore());
        await _localDb.markAsSynced('items', item.id);
      }

      if (kDebugMode) {
        print('Items synced successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing items: $e');
      }
    }
  }

  // Sync invoices
  Future<void> syncInvoices() async {
    try {
      // Download from Firebase
      final snapshot = await _firebaseService.firestore.collection('invoices').get();
      
      for (final doc in snapshot.docs) {
        final invoice = InvoiceModel.fromFirestore(doc);
        final invoiceData = invoice.toMap();
        invoiceData['syncStatus'] = 1; // Mark as synced
        
        await _localDb.insert('invoices', invoiceData);
      }

      // Upload local changes
      final localInvoices = await _localDb.query('invoices', where: 'syncStatus = ?', whereArgs: [0]);
      
      for (final invoiceData in localInvoices) {
        final invoice = InvoiceModel.fromMap(invoiceData);
        await _firebaseService.firestore.collection('invoices').doc(invoice.id).set(invoice.toFirestore());
        await _localDb.markAsSynced('invoices', invoice.id);
      }

      if (kDebugMode) {
        print('Invoices synced successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing invoices: $e');
      }
    }
  }

  // Sync payments
  Future<void> syncPayments() async {
    try {
      // Download from Firebase
      final snapshot = await _firebaseService.firestore.collection('payments').get();
      
      for (final doc in snapshot.docs) {
        final payment = PaymentModel.fromFirestore(doc);
        final paymentData = payment.toMap();
        paymentData['syncStatus'] = 1; // Mark as synced
        
        await _localDb.insert('payments', paymentData);
      }

      // Upload local changes
      final localPayments = await _localDb.query('payments', where: 'syncStatus = ?', whereArgs: [0]);
      
      for (final paymentData in localPayments) {
        final payment = PaymentModel.fromMap(paymentData);
        await _firebaseService.firestore.collection('payments').doc(payment.id).set(payment.toFirestore());
        await _localDb.markAsSynced('payments', payment.id);
      }

      if (kDebugMode) {
        print('Payments synced successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing payments: $e');
      }
    }
  }

  // Sync document tracking
  Future<void> syncDocumentTracking() async {
    try {
      // Download from Firebase
      final snapshot = await _firebaseService.firestore.collection('document_tracking').get();

      for (final doc in snapshot.docs) {
        final tracking = DocumentTrackingModel.fromFirestore(doc);
        final trackingData = tracking.toMap();
        trackingData['syncStatus'] = 1; // Mark as synced

        // Check if local version has composite image that Firebase doesn't have
        final existingResults = await _localDb.query('document_tracking', where: 'id = ?', whereArgs: [tracking.id]);
        if (existingResults.isNotEmpty) {
          final existingTracking = DocumentTrackingModel.fromMap(existingResults.first);

          // If local has composite image but Firebase doesn't, keep the local one
          if (existingTracking.compositeImagePath != null &&
              existingTracking.compositeImagePath!.isNotEmpty &&
              (tracking.compositeImagePath == null || tracking.compositeImagePath!.isEmpty)) {
            trackingData['compositeImagePath'] = existingTracking.compositeImagePath;

            if (kDebugMode) {
              print('🖼️ Preserving local composite image for tracking: ${tracking.id}');
              print('   Local path: ${existingTracking.compositeImagePath}');
            }
          }
        }

        await _localDb.insert('document_tracking', trackingData);
      }

      // Upload local changes
      final localTracking = await _localDb.query('document_tracking', where: 'syncStatus = ?', whereArgs: [0]);
      
      for (final trackingData in localTracking) {
        final tracking = DocumentTrackingModel.fromMap(trackingData);
        await _firebaseService.firestore.collection('document_tracking').doc(tracking.id).set(tracking.toFirestore());
        await _localDb.markAsSynced('document_tracking', tracking.id);
      }

      if (kDebugMode) {
        print('Document tracking synced successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing document tracking: $e');
      }
    }
  }

  // Process sync queue for offline operations
  Future<void> processSyncQueue() async {
    try {
      final pendingItems = await _localDb.getPendingSyncItems();
      
      for (final item in pendingItems) {
        try {
          final data = jsonDecode(item['data']);
          final collection = _firebaseService.firestore.collection(item['tableName']);
          
          switch (item['operation']) {
            case 'INSERT':
            case 'UPDATE':
              await collection.doc(item['recordId']).set(data);
              break;
            case 'DELETE':
              await collection.doc(item['recordId']).delete();
              break;
          }
          
          await _localDb.removeSyncItem(item['id']);
          
          if (kDebugMode) {
            print('Processed sync queue item: ${item['operation']} ${item['tableName']}/${item['recordId']}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error processing sync queue item ${item['id']}: $e');
          }
          // Could implement retry logic here
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error processing sync queue: $e');
      }
    }
  }

  // Force immediate sync
  Future<void> forceSyncNow() async {
    if (kDebugMode) {
      print('Force sync requested');
    }
    await syncAll();
  }

  // Dispose resources
  void dispose() {
    _syncTimer?.cancel();
    _connectivitySubscription?.cancel();
    _isSyncing = false;
  }
}
