@echo off
echo ========================================
echo    فحص وتشغيل تطبيق آل فرحان
echo ========================================
echo.

echo [1/6] فحص Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo خطأ: Flutter غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Flutter أولاً
    pause
    exit /b 1
)

echo.
echo [2/6] فحص الأجهزة المتصلة...
adb devices
echo.
flutter devices

echo.
echo [3/6] تنظيف المشروع...
flutter clean

echo.
echo [4/6] تحميل التبعيات...
flutter pub get

echo.
echo [5/6] فحص الأخطاء...
flutter analyze --no-fatal-infos

echo.
echo [6/6] تشغيل التطبيق...
echo ملاحظة: تأكد من أن هاتفك متصل ومفعل عليه USB Debugging
echo.

flutter run --debug --verbose

echo.
echo انتهى!
pause
