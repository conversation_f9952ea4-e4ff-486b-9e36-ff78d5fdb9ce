# 🧪 سيناريوهات الاختبار الشاملة - تطبيق آل فرحان

## 📋 **نظرة عامة**
هذا المستند يحتوي على سيناريوهات اختبار شاملة لجميع وظائف تطبيق آل فرحان للنقل الخفيف.

---

## 🔐 **1. اختبار إدارة المستخدمين والصلاحيات**

### **1.1 اختبار تسجيل الدخول**
- **السيناريو**: تسجيل دخول المدير العام
- **الخطوات**:
  1. فتح التطبيق
  2. إدخال اسم المستخدم: `ahmed`
  3. إدخال كلمة المرور: `admin123`
  4. الضغط على تسجيل الدخول
- **النتيجة المتوقعة**: دخول ناجح إلى الشاشة الرئيسية مع عرض جميع الوظائف

### **1.2 اختبار إنشاء حساب وكيل**
- **السيناريو**: إنشاء حساب وكيل جديد بواسطة المدير العام
- **الخطوات**:
  1. الانتقال إلى إدارة المستخدمين
  2. الضغط على "إضافة مستخدم جديد"
  3. ملء البيانات (الاسم، اسم المستخدم، كلمة المرور، الدور: وكيل)
  4. تحديد مخزن مخصص للوكيل
  5. حفظ البيانات
- **النتيجة المتوقعة**: إنشاء حساب الوكيل بنجاح مع ربطه بالمخزن المحدد

### **1.3 اختبار صلاحيات الوكيل**
- **السيناريو**: التحقق من أن الوكيل يرى مخزنه فقط
- **الخطوات**:
  1. تسجيل دخول الوكيل
  2. الانتقال إلى شاشة المخزون
  3. التحقق من عدم وجود فلتر للمخازن
  4. التحقق من عرض أصناف مخزن الوكيل فقط
- **النتيجة المتوقعة**: عرض أصناف مخزن الوكيل فقط بدون خيارات فلترة المخازن

---

## 📦 **2. اختبار إدارة المخزون**

### **2.1 اختبار إضافة صنف جديد**
- **السيناريو**: إضافة موتوسيكل جديد للمخزن الرئيسي
- **الخطوات**:
  1. الانتقال إلى إدارة المخزون
  2. الضغط على "إضافة صنف جديد"
  3. ملء البيانات الأساسية (النوع، الماركة، الموديل، اللون)
  4. تصوير بصمة الموتور واستخراج النص
  5. تصوير رقم الشاسيه واستخراج النص
  6. إدخال الأسعار (الشراء والبيع المقترح)
  7. تحديد المخزن الرئيسي
  8. حفظ الصنف
- **النتيجة المتوقعة**: إضافة الصنف بحالة "متاح" في المخزن المحدد

### **2.2 اختبار نقل الأصناف بين المخازن**
- **السيناريو**: نقل موتوسيكل من المخزن الرئيسي إلى مخزن وكيل
- **الخطوات**:
  1. الانتقال إلى نقل البضائع
  2. اختيار المخزن المصدر (الرئيسي)
  3. اختيار المخزن الهدف (مخزن الوكيل)
  4. اختيار الصنف المراد نقله
  5. تأكيد النقل
- **النتيجة المتوقعة**: 
  - نقل الصنف إلى مخزن الوكيل
  - إنشاء فاتورة بضائع للوكيل
  - تحديث رصيد حساب الوكيل

---

## 💰 **3. اختبار المبيعات وحسابات الوكلاء**

### **3.1 اختبار بيع من مخزن الوكيل**
- **السيناريو**: بيع موتوسيكل من مخزن الوكيل لعميل نهائي
- **الخطوات**:
  1. تسجيل دخول الوكيل
  2. الانتقال إلى إنشاء فاتورة
  3. اختيار الصنف من مخزن الوكيل
  4. تصوير هوية العميل واستخراج البيانات
  5. إدخال سعر البيع
  6. تأكيد البيع
- **النتيجة المتوقعة**:
  - تغيير حالة الصنف إلى "مباع"
  - إنشاء فاتورة عميل
  - تقسيم الربح 50/50 بين الشركة والوكيل
  - إضافة نصيب الشركة لرصيد الوكيل

### **3.2 اختبار تسجيل دفعة وكيل**
- **السيناريو**: تسجيل دفعة من الوكيل للشركة بواسطة المدير
- **الخطوات**:
  1. تسجيل دخول المدير
  2. الانتقال إلى إدارة الوكلاء
  3. اختيار الوكيل
  4. الضغط على "تسجيل دفعة"
  5. إدخال مبلغ الدفعة
  6. إضافة ملاحظات
  7. تأكيد التسجيل
- **النتيجة المتوقعة**: تحديث رصيد حساب الوكيل وتسجيل الدفعة

---

## 📄 **4. اختبار تتبع الوثائق**

### **4.1 اختبار إنشاء تتبع وثيقة**
- **السيناريو**: إنشاء تتبع وثيقة عند بيع موتوسيكل
- **الخطوات**:
  1. إتمام عملية بيع موتوسيكل
  2. التحقق من إنشاء تتبع الوثيقة تلقائياً
  3. التحقق من الحالة الأولى: "تم إرسال البيانات للمدير"
  4. التحقق من إنشاء الصورة المجمعة
- **النتيجة المتوقعة**: إنشاء تتبع وثيقة بالحالة الأولى مع الصورة المجمعة

### **4.2 اختبار تحديث حالة الوثيقة**
- **السيناريو**: تحديث حالة الوثيقة بواسطة المدير
- **الخطوات**:
  1. تسجيل دخول المدير
  2. الانتقال إلى تتبع الوثائق
  3. اختيار وثيقة في الحالة الأولى
  4. الضغط على "تحديث الحالة"
  5. تأكيد التحديث للحالة التالية
- **النتيجة المتوقعة**: 
  - تحديث الحالة إلى "تم إرسال الجواب للشركة المصنعة"
  - إرسال إشعار للوكيل

### **4.3 اختبار الحالات الأربع للوثيقة**
- **السيناريو**: التنقل عبر جميع حالات الوثيقة
- **الحالات**:
  1. "تم إرسال البيانات للمدير" (برتقالي)
  2. "تم إرسال الجواب للشركة المصنعة" (أزرق)
  3. "تم استلام الجواب من الشركة المصنعة" (أخضر)
  4. "تم التسليم الي الوكيل" (أخضر داكن)
- **النتيجة المتوقعة**: تحديث متسلسل للحالات مع الألوان المناسبة

---

## 🔄 **5. اختبار المزامنة والعمل دون اتصال**

### **5.1 اختبار العمل دون اتصال**
- **السيناريو**: استخدام التطبيق بدون اتصال إنترنت
- **الخطوات**:
  1. قطع الاتصال بالإنترنت
  2. إضافة صنف جديد
  3. إجراء عملية بيع
  4. تسجيل دفعة وكيل
  5. إعادة تشغيل الاتصال
  6. التحقق من المزامنة التلقائية
- **النتيجة المتوقعة**: حفظ جميع العمليات محلياً ومزامنتها عند عودة الاتصال

---

## 📊 **6. اختبار التقارير**

### **6.1 اختبار تقارير المخزون**
- **السيناريو**: عرض تقرير المخزون للمدير
- **الخطوات**:
  1. الانتقال إلى التقارير
  2. عرض تقرير المخزون
  3. التحقق من الإحصائيات
  4. تصدير التقرير كـ PDF
- **النتيجة المتوقعة**: عرض دقيق لحالة المخزون مع إمكانية التصدير

### **6.2 اختبار إخفاء تقارير الأرباح عن الوكلاء**
- **السيناريو**: التحقق من عدم رؤية الوكلاء لتقارير الأرباح
- **الخطوات**:
  1. تسجيل دخول الوكيل
  2. الانتقال إلى التقارير
  3. التحقق من عدم وجود قسم الأرباح
- **النتيجة المتوقعة**: عدم عرض تقارير الأرباح للوكلاء

---

## 🔍 **7. اختبار البحث والفلترة**

### **7.1 اختبار البحث في المبيعات**
- **السيناريو**: البحث عن فاتورة بأسماء العملاء
- **الخطوات**:
  1. الانتقال إلى المبيعات
  2. إدخال اسم عميل في البحث
  3. التحقق من النتائج
  4. البحث برقم الهاتف
  5. البحث برقم الفاتورة
- **النتيجة المتوقعة**: عرض نتائج دقيقة للبحث في جميع الحقول

---

## ⚠️ **8. اختبارات الأخطاء والحالات الاستثنائية**

### **8.1 اختبار منع الوكيل من تسجيل دفعة لنفسه**
- **السيناريو**: محاولة الوكيل تسجيل دفعة لنفسه
- **الخطوات**:
  1. تسجيل دخول الوكيل
  2. محاولة الوصول لتسجيل الدفعات
- **النتيجة المتوقعة**: عدم ظهور خيار تسجيل الدفعات للوكيل

### **8.2 اختبار منع بيع صنف مباع**
- **السيناريو**: محاولة بيع صنف تم بيعه مسبقاً
- **الخطوات**:
  1. بيع صنف معين
  2. محاولة بيعه مرة أخرى
- **النتيجة المتوقعة**: عدم ظهور الصنف المباع في قائمة الأصناف المتاحة

---

## ✅ **معايير النجاح العامة**

1. **الأداء**: بدء التطبيق في أقل من 3 ثوان
2. **الذاكرة**: استهلاك أقل من 150 ميجابايت
3. **الحجم**: حجم التطبيق أقل من 50 ميجابايت
4. **الاستقرار**: عدم تعطل التطبيق أثناء الاختبارات
5. **دقة البيانات**: تطابق البيانات بين الشاشات المختلفة
6. **المزامنة**: مزامنة فورية عند توفر الاتصال

---

## 🎯 **9. اختبارات التكامل الشاملة**

### **9.1 سيناريو العمل الكامل - من الإضافة للبيع**
- **السيناريو**: دورة حياة كاملة لصنف من الإضافة حتى البيع
- **الخطوات**:
  1. إضافة موتوسيكل جديد للمخزن الرئيسي
  2. نقله إلى مخزن وكيل
  3. بيعه من الوكيل لعميل
  4. تتبع حالة الوثيقة
  5. تسجيل دفعة من الوكيل
  6. مراجعة كشف حساب الوكيل
- **النتيجة المتوقعة**: تنفيذ سلس لجميع المراحل مع تحديث دقيق للبيانات

### **9.2 اختبار الحسابات والأرباح**
- **السيناريو**: التحقق من دقة حسابات الأرباح وأرصدة الوكلاء
- **الخطوات**:
  1. بيع عدة أصناف من مخازن مختلفة
  2. مراجعة تقسيم الأرباح (50/50 للوكلاء، 100% للشركة من المعرض)
  3. التحقق من أرصدة حسابات الوكلاء
  4. مراجعة تقارير الأرباح
- **النتيجة المتوقعة**: حسابات دقيقة ومتطابقة عبر جميع التقارير

---

## 🔧 **10. اختبارات الصيانة والإدارة**

### **10.1 اختبار تنظيف البيانات**
- **السيناريو**: استخدام وظيفة تنظيف البيانات الكاملة
- **الخطوات**:
  1. الانتقال إلى الإعدادات
  2. الضغط على "تنظيف البيانات الكاملة"
  3. تأكيد العملية
  4. التحقق من حذف جميع البيانات التجريبية
- **النتيجة المتوقعة**: حذف البيانات التجريبية مع الاحتفاظ بالبيانات الحقيقية

### **10.2 اختبار تحديث البيانات من Firebase**
- **السيناريو**: تحديث البيانات المحلية من Firebase
- **الخطوات**:
  1. إجراء تغييرات في Firebase مباشرة
  2. استخدام زر التحديث في التطبيق
  3. التحقق من تحديث البيانات المحلية
- **النتيجة المتوقعة**: مزامنة فورية للبيانات المحدثة

---

## 📱 **11. اختبارات واجهة المستخدم**

### **11.1 اختبار الاستجابة على أحجام شاشات مختلفة**
- **السيناريو**: اختبار التطبيق على أجهزة مختلفة
- **الأجهزة المطلوبة**:
  - هاتف صغير (5 بوصة)
  - هاتف متوسط (6 بوصة)
  - هاتف كبير (6.5+ بوصة)
  - تابلت (10+ بوصة)
- **النتيجة المتوقعة**: عرض مناسب ومقروء على جميع الأحجام

### **11.2 اختبار دعم اللغة العربية**
- **السيناريو**: التحقق من دعم النصوص العربية
- **الخطوات**:
  1. إدخال نصوص عربية في جميع الحقول
  2. تصدير تقارير PDF
  3. مشاركة البيانات عبر WhatsApp
- **النتيجة المتوقعة**: عرض صحيح للنصوص العربية في جميع الوظائف

---

## 🚨 **12. اختبارات الأمان**

### **12.1 اختبار صلاحيات الوصول**
- **السيناريو**: التحقق من عدم تجاوز المستخدمين لصلاحياتهم
- **الاختبارات**:
  - محاولة الوكيل الوصول لبيانات وكيل آخر
  - محاولة الوكيل رؤية تقارير الأرباح
  - محاولة الوكيل تعديل بيانات المستخدمين
- **النتيجة المتوقعة**: منع الوصول غير المصرح به

### **12.2 اختبار حماية البيانات الحساسة**
- **السيناريو**: التحقق من حماية البيانات المالية
- **الخطوات**:
  1. محاولة الوصول للبيانات المالية بدون صلاحية
  2. التحقق من تشفير البيانات المحلية
  3. اختبار النسخ الاحتياطي الآمن
- **النتيجة المتوقعة**: حماية كاملة للبيانات الحساسة

---

## 📊 **13. اختبارات الأداء**

### **13.1 اختبار الأداء مع بيانات كبيرة**
- **السيناريو**: اختبار التطبيق مع كمية كبيرة من البيانات
- **البيانات المطلوبة**:
  - 1000+ صنف في المخزون
  - 500+ فاتورة مبيعات
  - 50+ وكيل
  - 100+ وثيقة تتبع
- **النتيجة المتوقعة**: أداء سريع ومستقر مع البيانات الكبيرة

### **13.2 اختبار استهلاك الذاكرة**
- **السيناريو**: مراقبة استهلاك الذاكرة أثناء الاستخدام المكثف
- **الخطوات**:
  1. فتح جميع الشاشات بالتتابع
  2. إجراء عمليات متعددة
  3. مراقبة استهلاك الذاكرة
- **النتيجة المتوقعة**: استهلاك أقل من 150 ميجابايت

---

## 📝 **ملاحظات الاختبار**

### **متطلبات البيئة**
- يجب تنفيذ جميع السيناريوهات على أجهزة مختلفة
- اختبار التطبيق مع بيانات حقيقية وليس بيانات تجريبية
- التحقق من عمل الإشعارات بشكل صحيح
- اختبار النسخ الاحتياطي والاستعادة
- التحقق من دعم اللغة العربية في جميع الوظائف

### **معايير القبول**
- نجاح 100% من الاختبارات الأساسية
- نجاح 95% من اختبارات الأداء
- عدم وجود أخطاء حرجة
- استقرار التطبيق لمدة 24 ساعة متواصلة

### **تقرير الاختبار**
يجب توثيق:
- نتائج كل سيناريو اختبار
- الأخطاء المكتشفة وحلولها
- توصيات التحسين
- تقييم الأداء العام
