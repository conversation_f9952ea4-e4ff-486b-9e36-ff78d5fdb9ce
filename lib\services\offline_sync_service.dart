import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/invoice_model.dart';
import '../models/user_model.dart';
import '../models/inventory_item_model.dart';
import '../models/payment_model.dart';
import '../models/warehouse_model.dart';
import 'data_service.dart';
import 'local_database_service.dart';
import 'enhanced_notification_service.dart';

class OfflineSyncService {
  static final OfflineSyncService _instance = OfflineSyncService._internal();
  factory OfflineSyncService() => _instance;
  OfflineSyncService._internal();

  static OfflineSyncService get instance => _instance;

  final DataService _dataService = DataService.instance;
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  final EnhancedNotificationService _notificationService = EnhancedNotificationService.instance;

  // Connection monitoring
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;
  bool _isOnline = false;
  bool _isSyncing = false;

  // Sync queues
  final List<SyncOperation> _pendingOperations = [];
  final Map<String, DateTime> _lastSyncTimes = {};

  // Conflict resolution
  final List<ConflictResolution> _conflictQueue = [];

  // Sync settings
  static const Duration _syncInterval = Duration(minutes: 5);
  static const Duration _maxOfflineTime = Duration(days: 7);

  /// Initialize offline sync service
  Future<void> initialize() async {
    await _loadPendingOperations();
    await _loadLastSyncTimes();
    _startConnectivityMonitoring();
    _startPeriodicSync();
    
    if (kDebugMode) {
      print('🔄 Offline sync service initialized');
    }
  }

  /// Start monitoring connectivity
  void _startConnectivityMonitoring() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      if (kDebugMode) {
        print('📶 Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
      }

      if (!wasOnline && _isOnline) {
        // Just came online - start sync
        _onConnectionRestored();
      } else if (wasOnline && !_isOnline) {
        // Just went offline
        _onConnectionLost();
      }
    });

    // Check initial connectivity
    Connectivity().checkConnectivity().then((result) {
      _isOnline = result != ConnectivityResult.none;
      if (kDebugMode) {
        print('📶 Initial connectivity: ${_isOnline ? 'Online' : 'Offline'}');
      }
    });
  }

  /// Start periodic sync when online
  void _startPeriodicSync() {
    Timer.periodic(_syncInterval, (timer) {
      if (_isOnline && !_isSyncing) {
        _performSync();
      }
    });
  }

  /// Handle connection restored
  void _onConnectionRestored() {
    _notificationService.createNotification(
      title: 'اتصال الإنترنت متاح',
      body: 'جاري مزامنة البيانات...',
      type: 'sync',
    );
    
    _performSync();
  }

  /// Handle connection lost
  void _onConnectionLost() {
    _notificationService.createNotification(
      title: 'انقطع الاتصال بالإنترنت',
      body: 'سيتم حفظ التغييرات محلياً ومزامنتها عند عودة الاتصال',
      type: 'sync',
    );
  }

  /// Perform full sync
  Future<void> _performSync() async {
    if (_isSyncing) return;

    _isSyncing = true;
    
    try {
      if (kDebugMode) {
        print('🔄 Starting sync...');
      }

      // Upload pending operations first
      await _uploadPendingOperations();

      // Download latest data
      await _downloadLatestData();

      // Resolve conflicts
      await _resolveConflicts();

      // Update last sync times
      await _updateLastSyncTimes();

      if (kDebugMode) {
        print('✅ Sync completed successfully');
      }

      _notificationService.createNotification(
        title: 'تمت المزامنة بنجاح',
        body: 'تم تحديث جميع البيانات',
        type: 'sync',
      );

    } catch (e) {
      if (kDebugMode) {
        print('❌ Sync failed: $e');
      }

      _notificationService.createNotification(
        title: 'فشل في المزامنة',
        body: 'سيتم إعادة المحاولة تلقائياً',
        type: 'sync',
      );
    } finally {
      _isSyncing = false;
    }
  }

  /// Upload pending operations to server
  Future<void> _uploadPendingOperations() async {
    final operations = List<SyncOperation>.from(_pendingOperations);
    
    for (final operation in operations) {
      try {
        await _executeOperation(operation);
        _pendingOperations.remove(operation);
        
        if (kDebugMode) {
          print('⬆️ Uploaded: ${operation.type} ${operation.id}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Failed to upload ${operation.type} ${operation.id}: $e');
        }
        
        // Check if operation is too old
        if (DateTime.now().difference(operation.timestamp) > _maxOfflineTime) {
          _pendingOperations.remove(operation);
          if (kDebugMode) {
            print('🗑️ Removed expired operation: ${operation.type} ${operation.id}');
          }
        }
      }
    }

    await _savePendingOperations();
  }

  /// Execute sync operation
  Future<void> _executeOperation(SyncOperation operation) async {
    switch (operation.type) {
      case 'create_invoice':
        final invoice = InvoiceModel.fromMap(operation.data);
        await _dataService.createInvoice(invoice);
        break;
      case 'update_invoice':
        final invoice = InvoiceModel.fromMap(operation.data);
        await _dataService.updateInvoice(invoice);
        break;
      case 'create_payment':
        final payment = PaymentModel.fromMap(operation.data);
        await _dataService.createPayment(payment);
        break;
      case 'update_inventory':
        final item = InventoryItemModel.fromMap(operation.data);
        await _dataService.updateInventoryItem(item);
        break;
      case 'create_user':
        final user = UserModel.fromMap(operation.data);
        await _dataService.createUser(user);
        break;
      case 'update_user':
        final user = UserModel.fromMap(operation.data);
        await _dataService.updateUser(user);
        break;
    }
  }

  /// Download latest data from server
  Future<void> _downloadLatestData() async {
    try {
      // Download users
      final users = await _dataService.getUsers();
      await _syncUsersToLocal(users);

      // Download warehouses
      final warehouses = await _dataService.getWarehouses();
      await _syncWarehousesToLocal(warehouses);

      // Download recent invoices (last 30 days)
      final recentDate = DateTime.now().subtract(const Duration(days: 30));
      final invoices = await _dataService.getInvoices();
      final recentInvoices = invoices.where((invoice) => 
          invoice.createdAt.isAfter(recentDate)).toList();
      await _syncInvoicesToLocal(recentInvoices);

      // Download inventory items
      final items = await _dataService.getInventoryItems();
      await _syncInventoryToLocal(items);

      // Download recent payments
      final payments = await _dataService.getPayments();
      final recentPayments = payments.where((payment) => 
          payment.createdAt.isAfter(recentDate)).toList();
      await _syncPaymentsToLocal(recentPayments);

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error downloading data: $e');
      }
      rethrow;
    }
  }

  /// Sync users to local database
  Future<void> _syncUsersToLocal(List<UserModel> users) async {
    for (final user in users) {
      try {
        final existingUser = await _localDb.getUserById(user.id);
        
        if (existingUser == null) {
          await _localDb.insertUser(user);
        } else {
          // Check for conflicts
          if (_hasUserConflict(existingUser, user)) {
            _conflictQueue.add(ConflictResolution(
              type: 'user',
              localData: existingUser.toMap(),
              serverData: user.toMap(),
              id: user.id,
            ));
          } else {
            await _localDb.updateUser(user);
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error syncing user ${user.id}: $e');
        }
      }
    }
  }

  /// Sync warehouses to local database
  Future<void> _syncWarehousesToLocal(List<WarehouseModel> warehouses) async {
    for (final warehouse in warehouses) {
      try {
        await _localDb.insertOrUpdateWarehouse(warehouse);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error syncing warehouse ${warehouse.id}: $e');
        }
      }
    }
  }

  /// Sync invoices to local database
  Future<void> _syncInvoicesToLocal(List<InvoiceModel> invoices) async {
    for (final invoice in invoices) {
      try {
        final existingInvoice = await _localDb.getInvoiceById(invoice.id);
        
        if (existingInvoice == null) {
          await _localDb.insertInvoice(invoice);
        } else {
          if (_hasInvoiceConflict(existingInvoice, invoice)) {
            _conflictQueue.add(ConflictResolution(
              type: 'invoice',
              localData: existingInvoice.toMap(),
              serverData: invoice.toMap(),
              id: invoice.id,
            ));
          } else {
            await _localDb.updateInvoice(invoice);
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error syncing invoice ${invoice.id}: $e');
        }
      }
    }
  }

  /// Sync inventory to local database
  Future<void> _syncInventoryToLocal(List<InventoryItemModel> items) async {
    for (final item in items) {
      try {
        await _localDb.insertOrUpdateInventoryItem(item);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error syncing inventory item ${item.id}: $e');
        }
      }
    }
  }

  /// Sync payments to local database
  Future<void> _syncPaymentsToLocal(List<PaymentModel> payments) async {
    for (final payment in payments) {
      try {
        await _localDb.insertOrUpdatePayment(payment);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error syncing payment ${payment.id}: $e');
        }
      }
    }
  }

  /// Check for user conflicts
  bool _hasUserConflict(UserModel local, UserModel server) {
    // Simple conflict detection based on update time
    return server.updatedAt != null && 
           local.updatedAt.isAfter(server.updatedAt);
  }

  /// Check for invoice conflicts
  bool _hasInvoiceConflict(InvoiceModel local, InvoiceModel server) {
    return server.updatedAt != null && 
           local.updatedAt.isAfter(server.updatedAt);
  }

  /// Resolve conflicts
  Future<void> _resolveConflicts() async {
    for (final conflict in _conflictQueue) {
      try {
        await _resolveConflict(conflict);
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error resolving conflict for ${conflict.type} ${conflict.id}: $e');
        }
      }
    }
    _conflictQueue.clear();
  }

  /// Resolve individual conflict
  Future<void> _resolveConflict(ConflictResolution conflict) async {
    // For now, server wins (can be made configurable)
    switch (conflict.type) {
      case 'user':
        final user = UserModel.fromMap(conflict.serverData);
        await _localDb.updateUser(user);
        break;
      case 'invoice':
        final invoice = InvoiceModel.fromMap(conflict.serverData);
        await _localDb.updateInvoice(invoice);
        break;
    }
    
    if (kDebugMode) {
      print('🔧 Resolved conflict for ${conflict.type} ${conflict.id}');
    }
  }

  /// Add operation to sync queue
  Future<void> addPendingOperation(SyncOperation operation) async {
    _pendingOperations.add(operation);
    await _savePendingOperations();
    
    if (kDebugMode) {
      print('📝 Added pending operation: ${operation.type} ${operation.id}');
    }

    // Try to sync immediately if online
    if (_isOnline && !_isSyncing) {
      _performSync();
    }
  }

  /// Save pending operations to storage
  Future<void> _savePendingOperations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final operationsJson = _pendingOperations.map((op) => op.toJson()).toList();
      await prefs.setString('pending_operations', jsonEncode(operationsJson));
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving pending operations: $e');
      }
    }
  }

  /// Load pending operations from storage
  Future<void> _loadPendingOperations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final operationsJson = prefs.getString('pending_operations');
      
      if (operationsJson != null) {
        final operationsList = jsonDecode(operationsJson) as List;
        _pendingOperations.clear();
        _pendingOperations.addAll(
          operationsList.map((json) => SyncOperation.fromJson(json)).toList()
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading pending operations: $e');
      }
    }
  }

  /// Update last sync times
  Future<void> _updateLastSyncTimes() async {
    final now = DateTime.now();
    _lastSyncTimes['users'] = now;
    _lastSyncTimes['invoices'] = now;
    _lastSyncTimes['inventory'] = now;
    _lastSyncTimes['payments'] = now;
    _lastSyncTimes['warehouses'] = now;
    
    await _saveLastSyncTimes();
  }

  /// Save last sync times
  Future<void> _saveLastSyncTimes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final syncTimesJson = _lastSyncTimes.map((key, value) => 
          MapEntry(key, value.toIso8601String()));
      await prefs.setString('last_sync_times', jsonEncode(syncTimesJson));
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving sync times: $e');
      }
    }
  }

  /// Load last sync times
  Future<void> _loadLastSyncTimes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final syncTimesJson = prefs.getString('last_sync_times');
      
      if (syncTimesJson != null) {
        final syncTimes = jsonDecode(syncTimesJson) as Map<String, dynamic>;
        _lastSyncTimes.clear();
        syncTimes.forEach((key, value) {
          _lastSyncTimes[key] = DateTime.parse(value);
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading sync times: $e');
      }
    }
  }

  /// Get sync status
  Map<String, dynamic> getSyncStatus() {
    return {
      'isOnline': _isOnline,
      'isSyncing': _isSyncing,
      'pendingOperations': _pendingOperations.length,
      'conflicts': _conflictQueue.length,
      'lastSyncTimes': _lastSyncTimes.map((key, value) => 
          MapEntry(key, value.toIso8601String())),
    };
  }

  /// Force sync
  Future<void> forceSync() async {
    if (_isOnline) {
      await _performSync();
    } else {
      throw Exception('No internet connection available');
    }
  }

  /// Dispose service
  void dispose() {
    _connectivitySubscription.cancel();
    _pendingOperations.clear();
    _conflictQueue.clear();
    _lastSyncTimes.clear();
    
    if (kDebugMode) {
      print('🧹 Offline sync service disposed');
    }
  }
}

/// Sync operation model
class SyncOperation {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  SyncOperation({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory SyncOperation.fromJson(Map<String, dynamic> json) {
    return SyncOperation(
      id: json['id'],
      type: json['type'],
      data: Map<String, dynamic>.from(json['data']),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

/// Conflict resolution model
class ConflictResolution {
  final String type;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> serverData;
  final String id;

  ConflictResolution({
    required this.type,
    required this.localData,
    required this.serverData,
    required this.id,
  });
}
