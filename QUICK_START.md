# دليل التشغيل السريع - تطبيق آل فرحان

## 🚀 خطوات التشغيل السريع

### 1. **التأكد من البيئة**
```bash
flutter doctor
```

### 2. **تحميل المكتبات**
```bash
flutter pub get
```

### 3. **تشغيل التطبيق**
```bash
flutter run
```

### 4. **بناء APK للإنتاج**
```bash
flutter build apk --release
```

---

## 🔧 إعدادات مطلوبة

### Firebase (إذا لم تكن محدثة):
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. أنشئ مشروع جديد أو استخدم الموجود
3. أضف تطبيق Android
4. حمل `google-services.json` وضعه في `android/app/`

### Cloudinary (إذا لم تكن محدثة):
1. اذهب إلى [Cloudinary](https://cloudinary.com)
2. أنشئ حساب أو استخدم الموجود
3. احصل على `cloud_name` و `upload_preset`
4. حدث الإعدادات في `lib/services/image_service.dart`

---

## 👥 حسابات تجريبية

يمكنك إنشاء حسابات تجريبية في Firebase Console:

### المدير الأعلى:
- **Username:** admin
- **Password:** 123456
- **Role:** super_admin

### المدير الإداري:
- **Username:** manager  
- **Password:** 123456
- **Role:** admin

### الوكيل:
- **Username:** agent1
- **Password:** 123456
- **Role:** agent

### المعرض:
- **Username:** showroom
- **Password:** 123456
- **Role:** showroom

---

## 📱 الوظائف المتاحة

### ✅ **جاهزة للاستخدام:**
- تسجيل الدخول والمصادقة
- إدارة المخزون (إضافة/عرض/تحديث)
- إنشاء فواتير البيع
- تتبع الجوابات
- إدارة حسابات الوكلاء
- التقارير الشاملة
- العمل بدون إنترنت
- المزامنة التلقائية

### 🔍 **المميزات المتقدمة:**
- OCR لقراءة بصمة الموتور
- OCR لقراءة بطاقة الهوية
- ضغط ورفع الصور
- إشعارات Firebase
- نظام صلاحيات متدرج

---

## 🛠️ حل المشاكل الشائعة

### مشكلة: "No Firebase App"
```bash
# تأكد من وجود google-services.json في المكان الصحيح
# android/app/google-services.json
```

### مشكلة: "Gradle build failed"
```bash
flutter clean
flutter pub get
```

### مشكلة: "Permission denied"
```bash
# تأكد من إعطاء صلاحيات الكاميرا والتخزين
```

### مشكلة: "OCR not working"
```bash
# تأكد من إعدادات Google ML Kit
# تأكد من جودة الصورة
```

---

## 📊 حالة المشروع

### ✅ **مكتمل 100%:**
- البنية الأساسية
- جميع الخدمات (7 خدمات)
- جميع النماذج (6 نماذج)
- جميع الشاشات (12 شاشة)
- Firebase integration
- قاعدة البيانات المحلية
- نظام المزامنة
- OCR والصور
- الأمان والصلاحيات

### ⚠️ **تحذيرات بسيطة:**
- 9 تحذيرات فقط (متغيرات غير مستخدمة)
- لا تؤثر على عمل التطبيق

---

## 🎯 الخطوات التالية

1. **اختبار التطبيق** على جهاز حقيقي
2. **إضافة بيانات تجريبية** للاختبار
3. **تخصيص التصميم** حسب الحاجة
4. **نشر التطبيق** على متجر Google Play

---

## 📞 الدعم

للمساعدة في:
- إعداد Firebase
- إعداد Cloudinary  
- حل مشاكل التشغيل
- إضافة مميزات جديدة

**التطبيق جاهز للاستخدام الفوري! 🚀**
