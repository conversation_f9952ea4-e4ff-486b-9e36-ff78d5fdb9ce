import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:convert';
import 'local_database_service.dart';
import '../models/report_model.dart';

class ReportService {
  static final ReportService _instance = ReportService._internal();
  factory ReportService() => _instance;
  ReportService._internal();

  static ReportService get instance => _instance;

  final LocalDatabaseService _localDb = LocalDatabaseService.instance;

  /// Generate sales report
  Future<SalesReport> generateSalesReport({
    required DateTime startDate,
    required DateTime endDate,
    String? agentId,
    String? warehouseId,
  }) async {
    try {
      debugPrint('Generating sales report from ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      // Build query conditions
      String whereClause = 'createdAt >= ? AND createdAt <= ?';
      List<dynamic> whereArgs = [startDate.toIso8601String(), endDate.toIso8601String()];

      if (agentId != null) {
        whereClause += ' AND agentId = ?';
        whereArgs.add(agentId);
      }

      if (warehouseId != null) {
        whereClause += ' AND warehouseId = ?';
        whereArgs.add(warehouseId);
      }

      // Get invoices
      final invoices = await _localDb.query(
        'invoices',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'createdAt DESC',
      );

      // Calculate totals
      double totalSales = 0;
      double totalProfit = 0;
      int totalInvoices = invoices.length;
      int totalItems = 0;

      final Map<String, double> salesByAgent = {};
      final Map<String, double> salesByWarehouse = {};
      final Map<String, int> itemsSold = {};

      for (final invoice in invoices) {
        final amount = (invoice['totalAmount'] as num).toDouble();
        final profit = (invoice['profit'] as num?)?.toDouble() ?? 0;
        final itemCount = (invoice['itemCount'] as num?)?.toInt() ?? 0;
        
        totalSales += amount;
        totalProfit += profit;
        totalItems += itemCount;

        // Group by agent
        final agentName = invoice['agentName'] as String? ?? 'غير محدد';
        salesByAgent[agentName] = (salesByAgent[agentName] ?? 0) + amount;

        // Group by warehouse
        final warehouseName = invoice['warehouseName'] as String? ?? 'غير محدد';
        salesByWarehouse[warehouseName] = (salesByWarehouse[warehouseName] ?? 0) + amount;

        // Count items sold
        // This would need to be expanded to get actual item details from invoice_items table
      }

      return SalesReport(
        startDate: startDate,
        endDate: endDate,
        totalSales: totalSales,
        totalProfit: totalProfit,
        totalInvoices: totalInvoices,
        totalItems: totalItems,
        salesByAgent: salesByAgent,
        salesByWarehouse: salesByWarehouse,
        itemsSold: itemsSold,
        invoices: invoices,
      );
    } catch (e) {
      debugPrint('Error generating sales report: $e');
      throw Exception('فشل في إنشاء تقرير المبيعات: $e');
    }
  }

  /// Generate inventory report
  Future<InventoryReport> generateInventoryReport({
    String? warehouseId,
    String? category,
  }) async {
    try {
      debugPrint('Generating inventory report');

      // Build query conditions
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += ' AND warehouseId = ?';
        whereArgs.add(warehouseId);
      }

      if (category != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(category);
      }

      // Get items
      final items = await _localDb.query(
        'items',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'brand, model',
      );

      // Calculate totals
      int totalItems = items.length;
      int availableItems = 0;
      int soldItems = 0;
      int reservedItems = 0;

      final Map<String, int> itemsByWarehouse = {};
      final Map<String, int> itemsByCategory = {};
      final Map<String, int> itemsByStatus = {};

      for (final item in items) {
        final status = item['status'] as String;
        
        switch (status) {
          case 'متاح':
            availableItems++;
            break;
          case 'مباع':
            soldItems++;
            break;
          case 'محجوز':
            reservedItems++;
            break;
        }

        // Group by warehouse
        final warehouseName = item['warehouseName'] as String? ?? 'غير محدد';
        itemsByWarehouse[warehouseName] = (itemsByWarehouse[warehouseName] ?? 0) + 1;

        // Group by category
        final itemType = item['type'] as String? ?? 'غير محدد';
        itemsByCategory[itemType] = (itemsByCategory[itemType] ?? 0) + 1;

        // Group by status
        itemsByStatus[status] = (itemsByStatus[status] ?? 0) + 1;
      }

      return InventoryReport(
        totalItems: totalItems,
        availableItems: availableItems,
        soldItems: soldItems,
        reservedItems: reservedItems,
        itemsByWarehouse: itemsByWarehouse,
        itemsByCategory: itemsByCategory,
        itemsByStatus: itemsByStatus,
        items: items,
      );
    } catch (e) {
      debugPrint('Error generating inventory report: $e');
      throw Exception('فشل في إنشاء تقرير المخزون: $e');
    }
  }

  /// Generate agent performance report
  Future<AgentReport> generateAgentReport({
    required DateTime startDate,
    required DateTime endDate,
    String? agentId,
  }) async {
    try {
      debugPrint('Generating agent report');

      // Build query conditions
      String whereClause = 'createdAt >= ? AND createdAt <= ?';
      List<dynamic> whereArgs = [startDate.toIso8601String(), endDate.toIso8601String()];

      if (agentId != null) {
        whereClause += ' AND agentId = ?';
        whereArgs.add(agentId);
      }

      // Get invoices
      final invoices = await _localDb.query(
        'invoices',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'createdAt DESC',
      );

      // Calculate agent performance
      final Map<String, AgentPerformance> agentPerformance = {};

      for (final invoice in invoices) {
        final agentName = invoice['agentName'] as String? ?? 'غير محدد';
        final amount = (invoice['totalAmount'] as num).toDouble();
        final profit = (invoice['profit'] as num?)?.toDouble() ?? 0;
        final itemCount = (invoice['itemCount'] as num?)?.toInt() ?? 0;

        if (!agentPerformance.containsKey(agentName)) {
          agentPerformance[agentName] = AgentPerformance(
            agentName: agentName,
            totalSales: 0,
            totalProfit: 0,
            totalInvoices: 0,
            totalItems: 0,
          );
        }

        final performance = agentPerformance[agentName]!;
        performance.totalSales += amount;
        performance.totalProfit += profit;
        performance.totalInvoices += 1;
        performance.totalItems += itemCount;
      }

      return AgentReport(
        startDate: startDate,
        endDate: endDate,
        agentPerformance: agentPerformance,
        invoices: invoices,
      );
    } catch (e) {
      debugPrint('Error generating agent report: $e');
      throw Exception('فشل في إنشاء تقرير الوكلاء: $e');
    }
  }

  /// Export report to PDF
  Future<File> exportToPDF({
    required String reportTitle,
    required Map<String, dynamic> reportData,
    required List<Map<String, dynamic>> tableData,
    required List<String> tableHeaders,
  }) async {
    try {
      final pdf = pw.Document();

      // Add Arabic font support
      pw.Font? arabicFont;
      try {
        arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
      } catch (e) {
        // Fallback to default font if Arabic font fails
        debugPrint('Failed to load Arabic font, using default: $e');
        try {
          arabicFont = await PdfGoogleFonts.robotoRegular();
        } catch (e2) {
          debugPrint('Failed to load fallback font: $e2');
          // Will use default system font
        }
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              // Header
              pw.Header(
                level: 0,
                child: pw.Text(
                  reportTitle,
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: arabicFont,
                  ),
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // Report summary
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'ملخص التقرير',
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    ...reportData.entries.map((entry) {
                      return pw.Padding(
                        padding: const pw.EdgeInsets.only(bottom: 4),
                        child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text(entry.key),
                            pw.Text(entry.value.toString()),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // Data table
              if (tableData.isNotEmpty) ...[
                pw.Text(
                  'تفاصيل البيانات',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 10),
                pw.Table(
                  border: pw.TableBorder.all(),
                  children: [
                    // Header row
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey300,
                      ),
                      children: tableHeaders.map((header) {
                        return pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            header,
                            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          ),
                        );
                      }).toList(),
                    ),
                    // Data rows
                    ...tableData.map((row) {
                      return pw.TableRow(
                        children: tableHeaders.map((header) {
                          return pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(row[header]?.toString() ?? ''),
                          );
                        }).toList(),
                      );
                    }),
                  ],
                ),
              ],
              
              pw.SizedBox(height: 20),
              
              // Footer
              pw.Align(
                alignment: pw.Alignment.centerRight,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text('تم إنشاء التقرير في: ${DateTime.now().toString().split('.')[0]}'),
                    pw.Text('تطبيق آل فرحان للنقل الخفيف'),
                  ],
                ),
              ),
            ];
          },
        ),
      );

      // Save PDF to file
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'report_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      
      final pdfBytes = await pdf.save();
      await file.writeAsBytes(pdfBytes);

      debugPrint('PDF report saved to: ${file.path}');
      return file;
    } catch (e) {
      debugPrint('Error exporting PDF: $e');
      throw Exception('فشل في تصدير التقرير: $e');
    }
  }

  /// Share report file
  Future<void> shareReport(File reportFile, String title) async {
    try {
      await Share.shareXFiles(
        [XFile(reportFile.path)],
        text: title,
        subject: title,
      );
    } catch (e) {
      debugPrint('Error sharing report: $e');
      throw Exception('فشل في مشاركة التقرير: $e');
    }
  }

  /// Get report history
  Future<List<Map<String, dynamic>>> getReportHistory() async {
    try {
      return await _localDb.query(
        'reports',
        orderBy: 'createdAt DESC',
        limit: 50,
      );
    } catch (e) {
      debugPrint('Error getting report history: $e');
      return [];
    }
  }

  /// Save report to history
  Future<void> saveReportToHistory({
    required String title,
    required String type,
    required String filePath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _localDb.insert('reports', {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'title': title,
        'type': type,
        'filePath': filePath,
        'metadata': metadata?.toString() ?? '',
        'createdAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Error saving report to history: $e');
    }
  }

  /// Export report to CSV format
  Future<File> exportToCSV({
    required String title,
    required List<String> headers,
    required List<List<String>> data,
    Map<String, dynamic>? summary,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'report_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File('${directory.path}/$fileName');

      final csvContent = StringBuffer();

      // Add title
      csvContent.writeln('"$title"');
      csvContent.writeln('');

      // Add summary if provided
      if (summary != null && summary.isNotEmpty) {
        csvContent.writeln('"الملخص"');
        for (final entry in summary.entries) {
          csvContent.writeln('"${entry.key}","${entry.value}"');
        }
        csvContent.writeln('');
      }

      // Add headers
      csvContent.writeln(headers.map((h) => '"$h"').join(','));

      // Add data rows
      for (final row in data) {
        csvContent.writeln(row.map((cell) => '"${_escapeCsvCell(cell)}"').join(','));
      }

      // Add footer
      csvContent.writeln('');
      csvContent.writeln('"تم إنشاء التقرير في: ${DateTime.now().toString().split('.')[0]}"');
      csvContent.writeln('"تطبيق آل فرحان للنقل الخفيف"');

      await file.writeAsString(csvContent.toString(), encoding: utf8);

      debugPrint('CSV report saved to: ${file.path}');
      return file;
    } catch (e) {
      debugPrint('Error exporting CSV: $e');
      throw Exception('فشل في تصدير التقرير: $e');
    }
  }

  /// Export sales report to CSV
  Future<File> exportSalesReportToCSV(SalesReport report) async {
    final headers = [
      'رقم الفاتورة',
      'اسم العميل',
      'اسم الوكيل',
      'المخزن',
      'المبلغ',
      'الربح',
      'التاريخ',
    ];

    final data = <List<String>>[];
    for (final invoice in report.invoices) {
      data.add([
        invoice['invoiceNumber']?.toString() ?? '',
        invoice['customerName']?.toString() ?? '',
        invoice['agentName']?.toString() ?? '',
        invoice['warehouseName']?.toString() ?? '',
        invoice['totalAmount']?.toString() ?? '0',
        invoice['profit']?.toString() ?? '0',
        invoice['createdAt']?.toString() ?? '',
      ]);
    }

    final summary = {
      'إجمالي المبيعات': '${report.totalSales.toStringAsFixed(2)} ج.م',
      'إجمالي الأرباح': '${report.totalProfit.toStringAsFixed(2)} ج.م',
      'عدد الفواتير': report.totalInvoices.toString(),
      'عدد الأصناف': report.totalItems.toString(),
      'من تاريخ': report.startDate.toString().split(' ')[0],
      'إلى تاريخ': report.endDate.toString().split(' ')[0],
    };

    return await exportToCSV(
      title: 'تقرير المبيعات',
      headers: headers,
      data: data,
      summary: summary,
    );
  }

  /// Export inventory report to CSV
  Future<File> exportInventoryReportToCSV(InventoryReport report) async {
    final headers = [
      'بصمة الموتور',
      'النوع',
      'الماركة',
      'الموديل',
      'اللون',
      'رقم الشاسيه',
      'سعر الشراء',
      'سعر البيع المقترح',
      'المخزن',
      'الحالة',
      'تاريخ الإضافة',
    ];

    final data = <List<String>>[];
    for (final item in report.items) {
      data.add([
        item['motorFingerprintText']?.toString() ?? '',
        item['type']?.toString() ?? '',
        item['brand']?.toString() ?? '',
        item['model']?.toString() ?? '',
        item['color']?.toString() ?? '',
        item['chassisNumber']?.toString() ?? '',
        item['purchasePrice']?.toString() ?? '0',
        item['suggestedSellingPrice']?.toString() ?? '0',
        item['warehouseName']?.toString() ?? '',
        _getStatusText(item['status']?.toString() ?? ''),
        item['createdAt']?.toString() ?? '',
      ]);
    }

    final summary = {
      'إجمالي الأصناف': report.totalItems.toString(),
      'الأصناف المتاحة': report.availableItems.toString(),
      'الأصناف المباعة': report.soldItems.toString(),
      'الأصناف المحجوزة': report.reservedItems.toString(),
      'معدل التوفر': '${report.availabilityRate.toStringAsFixed(1)}%',
      'معدل المبيعات': '${report.soldRate.toStringAsFixed(1)}%',
    };

    return await exportToCSV(
      title: 'تقرير المخزون',
      headers: headers,
      data: data,
      summary: summary,
    );
  }

  /// Escape CSV cell content
  String _escapeCsvCell(String cell) {
    // Replace quotes with double quotes and handle newlines
    return cell.replaceAll('"', '""').replaceAll('\n', ' ').replaceAll('\r', ' ');
  }

  /// Get status text in Arabic
  String _getStatusText(String status) {
    switch (status) {
      case 'available':
        return 'متاح';
      case 'sold':
        return 'مباع';
      case 'transferred':
        return 'محول';
      case 'returned':
        return 'مرتجع';
      default:
        return status;
    }
  }
}
