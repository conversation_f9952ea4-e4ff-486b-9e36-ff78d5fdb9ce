# 📊 تحسينات كشف حساب الوكيل - تطبيق آل فرحان

## ✅ **التحسينات المطبقة:**

### 📱 **1. تحسين جدول كشف حساب الوكيل في التطبيق**

#### **الأعمدة الجديدة:**
| العمود | الوصف | الغرض |
|--------|-------|-------|
| **التاريخ** | تاريخ المعاملة | تتبع زمني للعمليات |
| **النوع** | نوع المعاملة (بيع/دفعة/تحويل) | تصنيف العمليات |
| **الوصف** | وصف مختصر للمعاملة | تفاصيل سريعة |
| **المبلغ** | قيمة المعاملة | المبلغ الإجمالي |
| **نصيب الوكيل** | حصة الوكيل من الربح | ما يستحقه الوكيل |
| **نصيب المؤسسة** | حصة المؤسسة من الربح | ما تستحقه المؤسسة |
| **نسبة المؤسسة %** | النسبة المئوية لربح المؤسسة | مؤشر الربحية |
| **تغيير الرصيد** | التأثير على رصيد الوكيل | زيادة أو نقصان |
| **المرجع** | رقم مرجعي مختصر | للمتابعة والتدقيق |

#### **أنواع المعاملات:**

##### **🟢 البيع (Sale):**
```dart
- الوصف: "بيع [اسم العميل]"
- المبلغ: سعر البيع الإجمالي
- نصيب الوكيل: حصة الوكيل من الربح
- نصيب المؤسسة: حصة المؤسسة من الربح
- نسبة المؤسسة: (نصيب المؤسسة ÷ إجمالي الربح) × 100
- تغيير الرصيد: +نصيب الوكيل
- المرجع: أول 8 أرقام من رقم الفاتورة
```

##### **🔴 الدفعة (Payment):**
```dart
- الوصف: "دفعة للمؤسسة"
- المبلغ: مبلغ الدفعة
- نصيب الوكيل: 0
- نصيب المؤسسة: 0
- نسبة المؤسسة: 0%
- تغيير الرصيد: -مبلغ الدفعة
- المرجع: أول 8 أرقام من رقم سند القبض
```

##### **🔵 التحويل (Transfer):**
```dart
- الوصف: "تحويل أصناف من المؤسسة"
- المبلغ: قيمة الأصناف المحولة
- نصيب الوكيل: 0
- نصيب المؤسسة: 0
- نسبة المؤسسة: 0%
- تغيير الرصيد: +قيمة الأصناف
- المرجع: أول 8 أرقام من رقم التحويل
```

---

### 📄 **2. تحسين PDF كشف حساب الوكيل**

#### **جدول شامل موحد:**
بدلاً من جداول منفصلة، أصبح هناك جدول واحد يعرض:

```
| التاريخ | النوع | الوصف | المبلغ | نصيب الوكيل | نصيب المؤسسة | نسبة المؤسسة % | تغيير الرصيد | المرجع |
```

#### **ألوان مميزة:**
- **🟢 البيع**: أخضر
- **🔴 الدفعة**: أحمر  
- **🔵 التحويل**: أزرق
- **نصيب الوكيل**: أخضر
- **نصيب المؤسسة**: أحمر
- **تغيير الرصيد الموجب**: أخضر
- **تغيير الرصيد السالب**: أحمر

---

### 🎯 **3. الفوائد المحققة:**

#### **للمديرين:**
✅ **رؤية واضحة لنسبة ربح المؤسسة** من كل معاملة  
✅ **تتبع دقيق للمدفوعات** من الوكلاء للمؤسسة  
✅ **مراقبة التحويلات** من المؤسسة للوكلاء  
✅ **أرقام مرجعية مختصرة** لسهولة المتابعة  
✅ **تحليل شامل للربحية** لكل وكيل  

#### **للوكلاء:**
✅ **فهم واضح لحصتهم** من كل عملية بيع  
✅ **تتبع دقيق للمدفوعات** التي قاموا بها  
✅ **رؤية شاملة لجميع المعاملات** في مكان واحد  
✅ **أرقام مرجعية** للرجوع للمعاملات الأصلية  

#### **للمحاسبين:**
✅ **سجل محاسبي دقيق** لجميع المعاملات  
✅ **توزيع واضح للأرباح** بين الوكيل والمؤسسة  
✅ **أرقام مرجعية** للتدقيق والمراجعة  
✅ **تقارير PDF منظمة** للأرشفة  

---

### 📊 **4. مثال على البيانات المعروضة:**

#### **في التطبيق والـ PDF:**

| التاريخ | النوع | الوصف | المبلغ | نصيب الوكيل | نصيب المؤسسة | نسبة المؤسسة % | تغيير الرصيد | المرجع |
|---------|-------|--------|-------|-------------|-------------|----------------|-------------|---------|
| 15/01/2024 | بيع | بيع أحمد محمد | 25,000 ج.م | 2,000 ج.م | 3,000 ج.م | 60.0% | +2,000 ج.م | INV12345 |
| 10/01/2024 | دفعة | دفعة للمؤسسة | 5,000 ج.م | - | - | - | -5,000 ج.م | RCP67890 |
| 05/01/2024 | تحويل | تحويل أصناف من المؤسسة | 20,000 ج.م | - | - | - | +20,000 ج.م | TRF11111 |

---

### 🔧 **5. التحسينات التقنية:**

#### **في الكود:**
```dart
// حساب نسبة ربح المؤسسة تلقائياً
final totalProfit = invoice.sellingPrice - invoice.purchasePrice;
final companyShare = totalProfit - invoice.agentProfitShare;
final companyProfitPercentage = totalProfit > 0 ? (companyShare / totalProfit) * 100 : 0;

// أرقام مرجعية مختصرة
'reference': invoice.invoiceNumber.length > 8 
    ? invoice.invoiceNumber.substring(0, 8) 
    : invoice.invoiceNumber,

// Tooltip للرقم المرجعي الكامل
Tooltip(
  message: transaction['full_reference'] ?? reference,
  child: Text(reference),
)
```

#### **في PDF:**
```dart
// جدول موحد بأعمدة محسنة
columnWidths: {
  0: const pw.FixedColumnWidth(50), // التاريخ
  1: const pw.FixedColumnWidth(40), // النوع
  2: const pw.FlexColumnWidth(2),   // الوصف
  3: const pw.FixedColumnWidth(50), // المبلغ
  4: const pw.FixedColumnWidth(50), // نصيب الوكيل
  5: const pw.FixedColumnWidth(50), // نصيب المؤسسة
  6: const pw.FixedColumnWidth(40), // نسبة المؤسسة
  7: const pw.FixedColumnWidth(50), // تغيير الرصيد
  8: const pw.FixedColumnWidth(45), // المرجع
}
```

---

### 📞 **الدعم الفني:**
**المطور**: Motasem Salem  
**WhatsApp**: 01062606098

---

### 🚀 **التحديثات القادمة:**
- إضافة فلترة حسب نوع المعاملة
- تصدير Excel لكشف الحساب
- رسوم بيانية لتوزيع الأرباح
- تقارير مقارنة بين الوكلاء
