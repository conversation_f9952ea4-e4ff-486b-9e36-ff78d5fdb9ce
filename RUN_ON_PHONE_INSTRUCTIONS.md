# تشغيل تطبيق آل فرحان على الهاتف 📱

## 🔧 الخطوات المطلوبة لتشغيل التطبيق على هاتفك

### 1. **إعداد الهاتف للتطوير**

#### **لهواتف Android:**
1. **تفعيل خيارات المطور:**
   - اذهب إلى `الإعدادات` → `حول الهاتف`
   - اضغط على `رقم البناء` 7 مرات متتالية
   - ستظهر رسالة "أصبحت مطوراً"

2. **تفعيل USB Debugging:**
   - اذهب إلى `الإعدادات` → `خيارات المطور`
   - فعّل `USB debugging`
   - فعّل `Install via USB` (إذا كان متاحاً)

3. **توصيل الهاتف:**
   - وصل الهاتف بالكمبيوتر عبر كابل USB
   - اختر `نقل الملفات` أو `MTP` عند ظهور الخيارات
   - اقبل تصريح USB Debugging عند ظهوره

#### **لهواتف iPhone:**
1. **تثبيت Xcode** (على Mac فقط)
2. **تسجيل Apple Developer Account**
3. **تفعيل Developer Mode في iOS**

### 2. **التحقق من اتصال الهاتف**

افتح Command Prompt أو PowerShell في مجلد المشروع وشغل:

```bash
# التحقق من Flutter
flutter doctor

# التحقق من الأجهزة المتصلة
flutter devices

# يجب أن ترى هاتفك في القائمة
```

### 3. **تشغيل التطبيق**

#### **الطريقة الأولى: التشغيل المباشر**
```bash
# انتقل لمجلد المشروع
cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"

# نظف المشروع
flutter clean

# احصل على التبعيات
flutter pub get

# شغل التطبيق على الهاتف
flutter run
```

#### **الطريقة الثانية: بناء APK وتثبيته**
```bash
# بناء APK للتطوير
flutter build apk --debug

# أو بناء APK للإنتاج
flutter build apk --release

# الملف سيكون في: build/app/outputs/flutter-apk/app-release.apk
```

### 4. **حل المشاكل الشائعة**

#### **مشكلة: الهاتف غير ظاهر في flutter devices**
```bash
# تحقق من ADB
adb devices

# إذا لم يظهر الهاتف، جرب:
adb kill-server
adb start-server
```

#### **مشكلة: خطأ في الصلاحيات**
- تأكد من قبول تصريح USB Debugging
- جرب كابل USB مختلف
- تأكد من تفعيل "Install via USB"

#### **مشكلة: خطأ في Firebase**
- تأكد من وجود ملف `google-services.json` في `android/app/`
- تحقق من إعدادات Firebase في المشروع

### 5. **خطوات التشغيل السريع**

**افتح Command Prompt كمدير وشغل:**

```bash
# 1. انتقل للمجلد
cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app"

# 2. تحقق من الأجهزة
flutter devices

# 3. شغل التطبيق
flutter run --debug
```

### 6. **إعدادات إضافية للأداء الأفضل**

#### **تسريع البناء:**
```bash
# استخدم هذا الأمر للتشغيل السريع
flutter run --hot-reload

# أو للبناء السريع
flutter run --debug --hot-reload
```

#### **لحل مشاكل الذاكرة:**
```bash
# زيادة ذاكرة Gradle
echo "org.gradle.jvmargs=-Xmx4g" >> android/gradle.properties
```

### 7. **اختبار المميزات**

بعد تشغيل التطبيق، تأكد من:

- [ ] **شاشة التحميل** تظهر بصورة المؤسسة
- [ ] **لوجو المؤسسة** يظهر في شاشة تسجيل الدخول
- [ ] **تسجيل الدخول** بالمستخدم الافتراضي:
  - اسم المستخدم: `ahmed`
  - كلمة المرور: `admin123`
- [ ] **الشاشة الرئيسية** تظهر مع اللوجو في الأعلى
- [ ] **القائمة الجانبية** تظهر اللوجو في الـ Header
- [ ] **جميع الشاشات** تعمل بشكل طبيعي

### 8. **نصائح للاستخدام**

#### **Hot Reload:**
- اضغط `r` في Terminal لإعادة تحميل سريع
- اضغط `R` لإعادة تشغيل كامل
- اضغط `q` للخروج

#### **مراقبة الأخطاء:**
- راقب Terminal للأخطاء
- استخدم `flutter logs` لرؤية سجلات مفصلة

### 9. **إنشاء APK للتوزيع**

```bash
# بناء APK نهائي للتوزيع
flutter build apk --release --split-per-abi

# الملفات ستكون في:
# build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
# build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk
# build/app/outputs/flutter-apk/app-x86_64-release.apk
```

### 10. **معلومات تسجيل الدخول**

#### **المستخدم الافتراضي (سوبر أدمن):**
- **اسم المستخدم:** `ahmed`
- **كلمة المرور:** `admin123`

#### **إنشاء مستخدمين جدد:**
- يمكن للسوبر أدمن إنشاء وكلاء جدد من شاشة إدارة المستخدمين
- كل وكيل يحتاج مخزن مخصص له

### 🚨 **ملاحظات مهمة**

1. **تأكد من اتصال الإنترنت** لأول مرة لإعداد Firebase
2. **احتفظ بنسخة احتياطية** من البيانات المهمة
3. **اختبر جميع المميزات** قبل التوزيع النهائي
4. **تحقق من الصلاحيات** لكل نوع مستخدم

### 📞 **الدعم الفني**

إذا واجهت أي مشاكل:
- **المطور:** معتصم سالم
- **واتساب:** 01062606098
- **تحقق من ملفات التوثيق** في المشروع

---

## 🎯 **الخطوة التالية**

**شغل هذا الأمر في Command Prompt:**

```bash
cd "C:\Users\<USER>\Documents\augment-projects\el_farhan_app" && flutter devices
```

إذا ظهر هاتفك في القائمة، شغل:

```bash
flutter run --debug
```

**التطبيق جاهز للتشغيل! 🚀**
