import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/invoice_model.dart';
import '../../services/data_service.dart';

class ComprehensiveCustomerInquiryScreen extends StatefulWidget {
  const ComprehensiveCustomerInquiryScreen({super.key});

  @override
  State<ComprehensiveCustomerInquiryScreen> createState() => _ComprehensiveCustomerInquiryScreenState();
}

class _ComprehensiveCustomerInquiryScreenState extends State<ComprehensiveCustomerInquiryScreen> {
  final DataService _dataService = DataService.instance;
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _customerNameController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _phoneController = TextEditingController();
  final _invoiceNumberController = TextEditingController();

  // Date range
  DateTime? _startDate;
  DateTime? _endDate;

  // Search results
  List<InvoiceModel> _searchResults = [];
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _customerNameController.dispose();
    _nationalIdController.dispose();
    _phoneController.dispose();
    _invoiceNumberController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if at least one search criteria is provided
    if (_customerNameController.text.trim().isEmpty &&
        _nationalIdController.text.trim().isEmpty &&
        _phoneController.text.trim().isEmpty &&
        _invoiceNumberController.text.trim().isEmpty &&
        _startDate == null &&
        _endDate == null) {
      setState(() {
        _errorMessage = 'يرجى إدخال معيار بحث واحد على الأقل';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _searchResults.clear();
    });

    try {
      final allInvoices = await _dataService.getInvoices();

      List<InvoiceModel> filteredInvoices = allInvoices.where((invoice) {
        // Filter by customer name
        if (_customerNameController.text.trim().isNotEmpty) {
          final customerData = invoice.customerData;
          if (customerData != null) {
            final customerName = customerData['fullName'] ??
                               customerData['customerName'] ??
                               customerData['name'] ?? '';
            if (!customerName.toLowerCase().contains(_customerNameController.text.trim().toLowerCase())) {
              return false;
            }
          } else {
            return false;
          }
        }

        // Filter by national ID
        if (_nationalIdController.text.trim().isNotEmpty) {
          final customerData = invoice.customerData;
          if (customerData != null) {
            final nationalId = customerData['nationalId'] ?? '';
            if (!nationalId.contains(_nationalIdController.text.trim())) {
              return false;
            }
          } else {
            return false;
          }
        }

        // Filter by phone
        if (_phoneController.text.trim().isNotEmpty) {
          final customerData = invoice.customerData;
          if (customerData != null) {
            final phone = customerData['phone'] ?? '';
            if (!phone.contains(_phoneController.text.trim())) {
              return false;
            }
          } else {
            return false;
          }
        }

        // Filter by invoice number
        if (_invoiceNumberController.text.trim().isNotEmpty) {
          if (!invoice.invoiceNumber.toLowerCase().contains(_invoiceNumberController.text.trim().toLowerCase())) {
            return false;
          }
        }

        // Filter by date range
        if (_startDate != null) {
          if (invoice.createdAt.isBefore(_startDate!)) {
            return false;
          }
        }

        if (_endDate != null) {
          final endOfDay = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
          if (invoice.createdAt.isAfter(endOfDay)) {
            return false;
          }
        }

        return true;
      }).toList();

      // Sort by creation date (newest first)
      filteredInvoices.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _searchResults = filteredInvoices;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في البحث: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استعلام العملاء الشامل'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Search form
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'معايير البحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Customer name field
                        TextFormField(
                          controller: _customerNameController,
                          decoration: const InputDecoration(
                            labelText: 'اسم العميل',
                            hintText: 'ادخل اسم العميل',
                            prefixIcon: Icon(Icons.person),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // National ID field
                        TextFormField(
                          controller: _nationalIdController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الهوية',
                            hintText: 'ادخل رقم الهوية',
                            prefixIcon: Icon(Icons.credit_card),
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 16),

                        // Phone field
                        TextFormField(
                          controller: _phoneController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الهاتف',
                            hintText: 'ادخل رقم الهاتف',
                            prefixIcon: Icon(Icons.phone),
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                        const SizedBox(height: 16),

                        // Invoice number field
                        TextFormField(
                          controller: _invoiceNumberController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الفاتورة',
                            hintText: 'ادخل رقم الفاتورة',
                            prefixIcon: Icon(Icons.receipt),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Date range
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _startDate ?? DateTime.now(),
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now(),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _startDate = date;
                                    });
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'من تاريخ',
                                    prefixIcon: Icon(Icons.calendar_today),
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    _startDate != null
                                        ? DateFormat('yyyy/MM/dd').format(_startDate!)
                                        : 'اختر التاريخ',
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _endDate ?? DateTime.now(),
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now(),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _endDate = date;
                                    });
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'إلى تاريخ',
                                    prefixIcon: Icon(Icons.calendar_today),
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    _endDate != null
                                        ? DateFormat('yyyy/MM/dd').format(_endDate!)
                                        : 'اختر التاريخ',
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Search button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _performSearch,
                            icon: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.search),
                            label: Text(_isLoading ? 'جاري البحث...' : 'بحث'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[700],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Error message
              if (_errorMessage != null)
                Card(
                  color: Colors.red[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red[700]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: Colors.red[700]),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Search results
              if (_searchResults.isNotEmpty)
                Card(
                  child: Column(
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                        ),
                        child: Text(
                          'نتائج البحث (${_searchResults.length} فاتورة)',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 400, // Fixed height to prevent overflow
                        child: ListView.builder(
                          itemCount: _searchResults.length,
                          itemBuilder: (context, index) {
                            final invoice = _searchResults[index];
                            return _InvoiceListTile(
                              invoice: invoice,
                              onTap: () => _showInvoiceDetails(invoice),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showInvoiceDetails(InvoiceModel invoice) {
    // Show invoice details dialog or navigate to details screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الفاتورة ${invoice.invoiceNumber}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('رقم الفاتورة: ${invoice.invoiceNumber}'),
              Text('التاريخ: ${DateFormat('yyyy/MM/dd').format(invoice.createdAt)}'),
              Text('السعر: ${invoice.sellingPrice} جنيه'),
              if (invoice.customerData != null) ...[
                const SizedBox(height: 8),
                const Text('بيانات العميل:', style: TextStyle(fontWeight: FontWeight.bold)),
                Text('الاسم: ${invoice.customerData!['fullName'] ?? invoice.customerData!['customerName'] ?? 'غير محدد'}'),
                Text('الهوية: ${invoice.customerData!['nationalId'] ?? 'غير محدد'}'),
                Text('الهاتف: ${invoice.customerData!['phone'] ?? 'غير محدد'}'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

class _InvoiceListTile extends StatelessWidget {
  final InvoiceModel invoice;
  final VoidCallback onTap;

  const _InvoiceListTile({
    required this.invoice,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final customerData = invoice.customerData;
    final customerName = customerData != null
        ? (customerData['fullName'] ?? customerData['customerName'] ?? customerData['name'] ?? 'غير محدد')
        : 'غير محدد';

    return ListTile(
      title: Text('فاتورة ${invoice.invoiceNumber}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('العميل: $customerName'),
          Text('التاريخ: ${DateFormat('yyyy/MM/dd').format(invoice.createdAt)}'),
          Text('المبلغ: ${invoice.sellingPrice} جنيه'),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap,
    );
  }
}