# دليل حل المشاكل - تطبيق آل فرحان 🔧

## 🚨 المشاكل الشائعة وحلولها

---

### **1. مشكلة: "No devices found"**

#### **الأعراض:**
```
No supported devices connected.
```

#### **الحلول:**

**أ) تحقق من اتصال الهاتف:**
```cmd
adb devices
```

**ب) إعادة تشغيل ADB:**
```cmd
adb kill-server
adb start-server
adb devices
```

**ج) تحقق من USB Debugging:**
- اذهب لإعدادات الهاتف → خيارات المطور
- تأكد من تفعيل "USB debugging"
- تأكد من تفعيل "Install via USB"

**د) جرب كابل USB مختلف:**
- تأكد أن الكابل يدعم نقل البيانات
- ليس كابل شحن فقط

---

### **2. مشكلة: "Gradle build failed"**

#### **الأعراض:**
```
FAILURE: Build failed with an exception.
```

#### **الحلول:**

**أ) تنظيف Gradle:**
```cmd
cd android
gradlew clean
cd ..
flutter clean
flutter pub get
```

**ب) حذف مجلدات البناء:**
```cmd
rmdir /s build
rmdir /s android\.gradle
flutter clean
flutter pub get
```

**ج) تحديث Gradle:**
- افتح `android/gradle/wrapper/gradle-wrapper.properties`
- تأكد من استخدام إصدار حديث من Gradle

---

### **3. مشكلة: "Firebase configuration error"**

#### **الأعراض:**
```
FirebaseOptions cannot be null when creating the default app.
```

#### **الحلول:**

**أ) تحقق من ملف Firebase:**
```cmd
dir android\app\google-services.json
```

**ب) إذا لم يكن موجوداً:**
- حمل الملف من Firebase Console
- ضعه في `android/app/google-services.json`

**ج) تحقق من اتصال الإنترنت:**
- Firebase يحتاج اتصال إنترنت في أول تشغيل

---

### **4. مشكلة: "Permission denied"**

#### **الأعراض:**
```
Permission denied (publickey).
```

#### **الحلول:**

**أ) تحقق من صلاحيات USB:**
- على الهاتف، اقبل تصريح USB Debugging
- اختر "Always allow from this computer"

**ب) إعادة تشغيل الهاتف:**
- أعد تشغيل الهاتف
- أعد توصيله بالكمبيوتر

---

### **5. مشكلة: "Out of memory"**

#### **الأعراض:**
```
Java heap space
OutOfMemoryError
```

#### **الحلول:**

**أ) زيادة ذاكرة Gradle:**
```cmd
echo org.gradle.jvmargs=-Xmx4g >> android\gradle.properties
```

**ب) إغلاق البرامج الأخرى:**
- أغلق Android Studio إذا كان مفتوحاً
- أغلق المتصفحات والبرامج الثقيلة

---

### **6. مشكلة: "SDK not found"**

#### **الأعراض:**
```
Android SDK not found
```

#### **الحلول:**

**أ) تحقق من متغيرات البيئة:**
```cmd
echo %ANDROID_HOME%
echo %ANDROID_SDK_ROOT%
```

**ب) تثبيت Android SDK:**
- حمل Android Studio
- ثبت Android SDK من خلاله

---

### **7. مشكلة: "App crashes on startup"**

#### **الأعراض:**
- التطبيق يفتح ثم يغلق فوراً

#### **الحلول:**

**أ) تحقق من السجلات:**
```cmd
flutter logs
```

**ب) تشغيل في وضع Debug:**
```cmd
flutter run --debug --verbose
```

**ج) تحقق من الصلاحيات:**
- تأكد من إعطاء التطبيق الصلاحيات المطلوبة

---

### **8. مشكلة: "Hot reload not working"**

#### **الأعراض:**
- التغييرات لا تظهر في التطبيق

#### **الحلول:**

**أ) إعادة تشغيل كامل:**
- اضغط `R` في Terminal

**ب) تحقق من الاتصال:**
```cmd
flutter devices
```

---

### **9. مشكلة: "Package not found"**

#### **الأعراض:**
```
Package 'xyz' not found
```

#### **الحلول:**

**أ) تحديث التبعيات:**
```cmd
flutter pub get
flutter pub upgrade
```

**ب) تنظيف الكاش:**
```cmd
flutter pub cache repair
```

---

### **10. مشكلة: "Build tools version"**

#### **الأعراض:**
```
Build Tools version is too low
```

#### **الحلول:**

**أ) تحديث Build Tools:**
- افتح Android Studio
- اذهب لـ SDK Manager
- حدث Build Tools

**ب) تحديث compileSdkVersion:**
- افتح `android/app/build.gradle`
- تأكد من استخدام إصدار حديث

---

## 🔍 أوامر التشخيص المفيدة

### **فحص شامل:**
```cmd
flutter doctor -v
```

### **فحص الأجهزة:**
```cmd
flutter devices -v
```

### **فحص التبعيات:**
```cmd
flutter pub deps
```

### **فحص الأخطاء:**
```cmd
flutter analyze
```

### **مراقبة السجلات:**
```cmd
flutter logs
```

---

## 📱 بديل: تثبيت APK مباشرة

**إذا فشل كل شيء:**

### **1. بناء APK:**
```cmd
flutter build apk --debug
```

### **2. نسخ APK:**
- انسخ من: `build\app\outputs\flutter-apk\app-debug.apk`
- انقل للهاتف عبر USB أو البلوتوث

### **3. تثبيت يدوي:**
- على الهاتف، فعّل "مصادر غير معروفة"
- ثبت APK من مدير الملفات

---

## 📞 طلب المساعدة

**إذا لم تحل المشكلة:**

### **أرسل هذه المعلومات:**

1. **نتيجة الأوامر:**
   ```cmd
   flutter doctor -v
   flutter devices
   adb devices
   ```

2. **رسالة الخطأ كاملة**

3. **معلومات الهاتف:**
   - نوع الهاتف
   - إصدار Android
   - مساحة التخزين المتاحة

4. **معلومات الكمبيوتر:**
   - نظام التشغيل
   - إصدار Flutter

### **التواصل:**
- **المطور:** معتصم سالم
- **واتساب:** 01062606098

---

## ✅ نصائح لتجنب المشاكل

1. **استخدم كابل USB أصلي**
2. **تأكد من اتصال إنترنت مستقر**
3. **أغلق البرامج الثقيلة أثناء البناء**
4. **احتفظ بمساحة كافية على القرص الصلب**
5. **حدث Flutter بانتظام**

---

## 🎯 الهدف

**الوصول لهذه النتيجة:**
```
✓ Flutter (Channel stable, 3.x.x)
✓ Android toolchain - develop for Android devices
✓ Connected device (1 available)
✓ No issues found!
```

**عندها ستتمكن من تشغيل التطبيق بنجاح!** 🚀
