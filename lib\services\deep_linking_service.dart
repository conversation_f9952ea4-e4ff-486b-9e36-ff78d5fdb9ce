import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../screens/sales/invoice_details_screen.dart';
import '../screens/agents/detailed_agent_statement_screen.dart';
import '../screens/documents/document_tracking_screen.dart';
import '../screens/inventory/inventory_screen.dart';
import '../screens/reports/comprehensive_reports_screen.dart';
import '../screens/notifications/advanced_notifications_screen.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';
import 'data_service.dart';

class DeepLinkingService {
  static final DeepLinkingService _instance = DeepLinkingService._internal();
  factory DeepLinkingService() => _instance;
  DeepLinkingService._internal();

  static DeepLinkingService get instance => _instance;

  final DataService _dataService = DataService.instance;
  late GoRouter _router;

  /// Initialize deep linking with router configuration
  GoRouter initializeRouter() {
    _router = GoRouter(
      initialLocation: '/',
      routes: [
        // Home route
        GoRoute(
          path: '/',
          builder: (context, state) => const Scaffold(
            body: Center(child: Text('الصفحة الرئيسية')),
          ),
        ),

        // Invoice details
        GoRoute(
          path: '/invoice_details/:invoiceId',
          builder: (context, state) {
            final invoiceId = state.pathParameters['invoiceId']!;
            return FutureBuilder<InvoiceModel?>(
              future: _getInvoiceById(invoiceId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Scaffold(
                    body: Center(child: CircularProgressIndicator()),
                  );
                }
                
                if (snapshot.hasError || !snapshot.hasData) {
                  return Scaffold(
                    appBar: AppBar(title: const Text('خطأ')),
                    body: const Center(
                      child: Text('لم يتم العثور على الفاتورة'),
                    ),
                  );
                }

                return InvoiceDetailsScreen(invoice: snapshot.data!);
              },
            );
          },
        ),

        // Agent statement
        GoRoute(
          path: '/agent_statement/:agentId',
          builder: (context, state) {
            final agentId = state.pathParameters['agentId']!;
            return FutureBuilder<UserModel?>(
              future: _getAgentById(agentId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Scaffold(
                    body: Center(child: CircularProgressIndicator()),
                  );
                }
                
                if (snapshot.hasError || !snapshot.hasData) {
                  return Scaffold(
                    appBar: AppBar(title: const Text('خطأ')),
                    body: const Center(
                      child: Text('لم يتم العثور على الوكيل'),
                    ),
                  );
                }

                return DetailedAgentStatementScreen(agent: snapshot.data!);
              },
            );
          },
        ),

        // Document details
        GoRoute(
          path: '/document_details/:documentId',
          builder: (context, state) {
            final documentId = state.pathParameters['documentId']!;
            return DocumentTrackingScreen(documentId: documentId);
          },
        ),

        // Transfer details
        GoRoute(
          path: '/transfer_details/:transferId',
          builder: (context, state) {
            final transferId = state.pathParameters['transferId']!;
            return Scaffold(
              appBar: AppBar(title: const Text('تفاصيل التحويل')),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.swap_horiz, size: 64, color: Colors.blue),
                    const SizedBox(height: 16),
                    Text('تفاصيل التحويل: $transferId'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('العودة'),
                    ),
                  ],
                ),
              ),
            );
          },
        ),

        // Payment details
        GoRoute(
          path: '/payment_details/:paymentId',
          builder: (context, state) {
            final paymentId = state.pathParameters['paymentId']!;
            return Scaffold(
              appBar: AppBar(title: const Text('تفاصيل الدفعة')),
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.payment, size: 64, color: Colors.green),
                    const SizedBox(height: 16),
                    Text('تفاصيل الدفعة: $paymentId'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('العودة'),
                    ),
                  ],
                ),
              ),
            );
          },
        ),

        // Inventory
        GoRoute(
          path: '/inventory',
          builder: (context, state) => const InventoryScreen(),
        ),

        // Reports
        GoRoute(
          path: '/reports',
          builder: (context, state) => const ComprehensiveReportsScreen(),
        ),

        // Notifications
        GoRoute(
          path: '/notifications',
          builder: (context, state) => const AdvancedNotificationsScreen(),
        ),

        // Test route
        GoRoute(
          path: '/test',
          builder: (context, state) => Scaffold(
            appBar: AppBar(title: const Text('صفحة تجريبية')),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.science, size: 64, color: Colors.purple),
                  SizedBox(height: 16),
                  Text('هذه صفحة تجريبية للاختبار'),
                ],
              ),
            ),
          ),
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(title: const Text('خطأ في التنقل')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('لم يتم العثور على الصفحة: ${state.location}'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('العودة للرئيسية'),
              ),
            ],
          ),
        ),
      ),
    );

    return _router;
  }

  /// Navigate to a specific route
  void navigateToRoute(BuildContext context, String route) {
    try {
      if (kDebugMode) {
        print('🔄 Navigating to: $route');
      }
      context.go(route);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Navigation error: $e');
      }
      // Fallback to showing a snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في التنقل: $route'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Navigate to invoice details
  void navigateToInvoice(BuildContext context, String invoiceId) {
    navigateToRoute(context, '/invoice_details/$invoiceId');
  }

  /// Navigate to agent statement
  void navigateToAgentStatement(BuildContext context, String agentId) {
    navigateToRoute(context, '/agent_statement/$agentId');
  }

  /// Navigate to document details
  void navigateToDocument(BuildContext context, String documentId) {
    navigateToRoute(context, '/document_details/$documentId');
  }

  /// Navigate to transfer details
  void navigateToTransfer(BuildContext context, String transferId) {
    navigateToRoute(context, '/transfer_details/$transferId');
  }

  /// Navigate to payment details
  void navigateToPayment(BuildContext context, String paymentId) {
    navigateToRoute(context, '/payment_details/$paymentId');
  }

  /// Navigate to inventory
  void navigateToInventory(BuildContext context) {
    navigateToRoute(context, '/inventory');
  }

  /// Navigate to reports
  void navigateToReports(BuildContext context) {
    navigateToRoute(context, '/reports');
  }

  /// Navigate to notifications
  void navigateToNotifications(BuildContext context) {
    navigateToRoute(context, '/notifications');
  }

  /// Parse and handle notification route
  void handleNotificationRoute(BuildContext context, String route) {
    if (route.startsWith('/')) {
      navigateToRoute(context, route);
    } else {
      // Handle relative routes
      navigateToRoute(context, '/$route');
    }
  }

  // Helper methods to fetch data
  Future<InvoiceModel?> _getInvoiceById(String invoiceId) async {
    try {
      final invoices = await _dataService.getInvoices();
      return invoices.firstWhere(
        (invoice) => invoice.id == invoiceId,
        orElse: () => throw Exception('Invoice not found'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching invoice: $e');
      }
      return null;
    }
  }

  Future<UserModel?> _getAgentById(String agentId) async {
    try {
      final users = await _dataService.getUsers();
      return users.firstWhere(
        (user) => user.id == agentId && user.role == 'agent',
        orElse: () => throw Exception('Agent not found'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching agent: $e');
      }
      return null;
    }
  }

  /// Generate shareable deep links
  String generateInvoiceLink(String invoiceId) {
    return '/invoice_details/$invoiceId';
  }

  String generateAgentStatementLink(String agentId) {
    return '/agent_statement/$agentId';
  }

  String generateDocumentLink(String documentId) {
    return '/document_details/$documentId';
  }

  String generateTransferLink(String transferId) {
    return '/transfer_details/$transferId';
  }

  String generatePaymentLink(String paymentId) {
    return '/payment_details/$paymentId';
  }
}
