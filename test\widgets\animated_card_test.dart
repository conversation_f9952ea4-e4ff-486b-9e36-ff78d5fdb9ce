import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:el_farhan_app/widgets/animated_card.dart';

void main() {
  group('AnimatedCard Widget Tests', () {
    testWidgets('should render child widget correctly', (WidgetTester tester) async {
      // Arrange
      const testText = 'Test Content';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AnimatedCard(
              child: Text(testText),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should animate on appearance', (WidgetTester tester) async {
      // Arrange
      const testText = 'Test Content';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AnimatedCard(
              duration: Duration(milliseconds: 300),
              child: Text(testText),
            ),
          ),
        ),
      );

      // Initial state (should be scaled down and transparent)
      await tester.pump();
      
      // Find the animated card
      final cardFinder = find.byType(AnimatedCard);
      expect(cardFinder, findsOneWidget);

      // Pump animation frames
      await tester.pump(const Duration(milliseconds: 150));
      await tester.pump(const Duration(milliseconds: 150));

      // Assert animation completed
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should handle tap events', (WidgetTester tester) async {
      // Arrange
      bool tapped = false;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnimatedCard(
              onTap: () => tapped = true,
              child: const Text('Tap me'),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Tap me'));
      await tester.pump();

      // Assert
      expect(tapped, isTrue);
    });

    testWidgets('should apply custom styling', (WidgetTester tester) async {
      // Arrange
      const customColor = Colors.red;
      const customElevation = 8.0;
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AnimatedCard(
              color: customColor,
              elevation: customElevation,
              child: Text('Styled Card'),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Assert
      final material = tester.widget<Material>(find.byType(Material));
      expect(material.color, equals(customColor));
      expect(material.elevation, equals(customElevation));
    });
  });

  group('AnimatedListItem Widget Tests', () {
    testWidgets('should render list items with staggered animation', (WidgetTester tester) async {
      // Arrange
      final items = ['Item 1', 'Item 2', 'Item 3'];
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListView.builder(
              itemCount: items.length,
              itemBuilder: (context, index) {
                return AnimatedListItem(
                  index: index,
                  child: ListTile(title: Text(items[index])),
                );
              },
            ),
          ),
        ),
      );

      // Assert all items are present
      for (final item in items) {
        expect(find.text(item), findsOneWidget);
      }
    });

    testWidgets('should animate with different delays for each item', (WidgetTester tester) async {
      // Arrange
      const delayBetweenItems = Duration(milliseconds: 100);
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                AnimatedListItem(
                  index: 0,
                  delayBetweenItems: delayBetweenItems,
                  child: Text('First Item'),
                ),
                AnimatedListItem(
                  index: 1,
                  delayBetweenItems: delayBetweenItems,
                  child: Text('Second Item'),
                ),
              ],
            ),
          ),
        ),
      );

      // Pump initial frame
      await tester.pump();

      // First item should start animating immediately
      await tester.pump(const Duration(milliseconds: 50));
      
      // Second item should start animating after delay
      await tester.pump(const Duration(milliseconds: 100));
      
      // Complete all animations
      await tester.pumpAndSettle();

      // Assert both items are visible
      expect(find.text('First Item'), findsOneWidget);
      expect(find.text('Second Item'), findsOneWidget);
    });
  });

  group('AnimatedCounter Widget Tests', () {
    testWidgets('should animate from 0 to target value', (WidgetTester tester) async {
      // Arrange
      const targetValue = 100;
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AnimatedCounter(
              value: targetValue,
              duration: Duration(milliseconds: 500),
            ),
          ),
        ),
      );

      // Initial state
      await tester.pump();
      expect(find.text('0'), findsOneWidget);

      // Animate to target
      await tester.pump(const Duration(milliseconds: 250));
      
      // Should be somewhere between 0 and 100
      final currentText = tester.widget<Text>(find.byType(Text)).data!;
      final currentValue = int.parse(currentText);
      expect(currentValue, greaterThan(0));
      expect(currentValue, lessThanOrEqualTo(targetValue));

      // Complete animation
      await tester.pumpAndSettle();
      expect(find.text('100'), findsOneWidget);
    });

    testWidgets('should update when value changes', (WidgetTester tester) async {
      // Arrange
      int currentValue = 50;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    AnimatedCounter(
                      value: currentValue,
                      duration: const Duration(milliseconds: 300),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          currentValue = 100;
                        });
                      },
                      child: const Text('Update'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Initial value
      await tester.pumpAndSettle();
      expect(find.text('50'), findsOneWidget);

      // Update value
      await tester.tap(find.text('Update'));
      await tester.pump();

      // Should start animating to new value
      await tester.pump(const Duration(milliseconds: 150));
      
      // Complete animation
      await tester.pumpAndSettle();
      expect(find.text('100'), findsOneWidget);
    });

    testWidgets('should apply prefix and suffix', (WidgetTester tester) async {
      // Arrange
      const value = 50;
      const prefix = '\$';
      const suffix = '.00';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AnimatedCounter(
              value: value,
              prefix: prefix,
              suffix: suffix,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Assert
      expect(find.text('\$50.00'), findsOneWidget);
    });
  });

  group('AnimatedProgressBar Widget Tests', () {
    testWidgets('should animate progress from 0 to target value', (WidgetTester tester) async {
      // Arrange
      const targetValue = 0.7;
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AnimatedProgressBar(
              value: targetValue,
              duration: Duration(milliseconds: 500),
              height: 10,
            ),
          ),
        ),
      );

      // Initial state
      await tester.pump();
      
      // Animate
      await tester.pump(const Duration(milliseconds: 250));
      
      // Complete animation
      await tester.pumpAndSettle();

      // Assert progress bar is present
      expect(find.byType(AnimatedProgressBar), findsOneWidget);
    });

    testWidgets('should update when value changes', (WidgetTester tester) async {
      // Arrange
      double currentValue = 0.3;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    AnimatedProgressBar(
                      value: currentValue,
                      duration: const Duration(milliseconds: 300),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          currentValue = 0.8;
                        });
                      },
                      child: const Text('Update Progress'),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Initial state
      await tester.pumpAndSettle();

      // Update progress
      await tester.tap(find.text('Update Progress'));
      await tester.pump();

      // Complete animation
      await tester.pumpAndSettle();

      // Assert progress bar updated
      expect(find.byType(AnimatedProgressBar), findsOneWidget);
    });
  });

  group('PulseAnimation Widget Tests', () {
    testWidgets('should continuously animate child', (WidgetTester tester) async {
      // Arrange
      const testText = 'Pulsing Text';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PulseAnimation(
              duration: Duration(milliseconds: 500),
              child: Text(testText),
            ),
          ),
        ),
      );

      // Initial state
      await tester.pump();
      expect(find.text(testText), findsOneWidget);

      // Animate through one cycle
      await tester.pump(const Duration(milliseconds: 250));
      await tester.pump(const Duration(milliseconds: 250));
      await tester.pump(const Duration(milliseconds: 250));

      // Should still be visible and animating
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should apply custom scale values', (WidgetTester tester) async {
      // Arrange
      const minScale = 0.8;
      const maxScale = 1.2;
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PulseAnimation(
              minScale: minScale,
              maxScale: maxScale,
              child: Text('Scaled Text'),
            ),
          ),
        ),
      );

      await tester.pump();
      
      // Assert widget is present
      expect(find.text('Scaled Text'), findsOneWidget);
    });
  });
}
