import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/enhanced_notification_service.dart';


class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService.instance;
  final EnhancedNotificationService _notificationService = EnhancedNotificationService.instance;

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;

  // Role-based getters
  bool get isSuperAdmin => _currentUser?.isSuperAdmin ?? false;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isAgent => _currentUser?.isAgent ?? false;
  bool get isShowroom => _currentUser?.isShowroom ?? false;

  // Permission getters
  bool get canManageUsers => _currentUser?.canManageUsers ?? false;
  bool get canManageInventory => _currentUser?.canManageInventory ?? false;
  bool get canCreateInvoices => _currentUser?.canCreateInvoices ?? false;
  bool get canViewReports => _currentUser?.canViewReports ?? false;
  bool get canManageDocuments => _currentUser?.canManageDocuments ?? false;

  // Initialize the provider
  Future<void> initialize() async {
    try {
      _setLoading(true);

      // Initialize auth service first to restore session
      await _authService.initialize();

      // Get current user after auth service initialization
      _currentUser = _authService.currentUser;

      if (_currentUser != null) {
        await _setupUserSession();
        if (kDebugMode) {
          print('✅ User session restored: ${_currentUser!.fullName}');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No saved user session found');
        }
      }
    } catch (e) {
      _setError('خطأ في تهيئة نظام المصادقة: $e');
      if (kDebugMode) {
        print('❌ Auth initialization error: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  // Sign in with username and password
  Future<void> signInWithUsername(String username, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _authService.signInWithUsername(username, password);

      if (user != null) {
        _currentUser = user;
        await _setupUserSession();

        if (kDebugMode) {
          print('✅ User signed in successfully: ${user.fullName}');
        }

        notifyListeners();
      } else {
        throw 'فشل في تسجيل الدخول';
      }
    } catch (e) {
      _setError(e.toString());
      if (kDebugMode) {
        print('❌ Sign in error: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _authService.signInWithEmailAndPassword(email, password);
      
      if (user != null) {
        _currentUser = user;
        await _setupUserSession();
        notifyListeners();
      } else {
        throw 'فشل في تسجيل الدخول';
      }
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Create new user (only for super admin)
  Future<UserModel?> createUser({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required String phone,
    required String role,
    String? warehouseId,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      if (!canManageUsers) {
        throw 'ليس لديك صلاحية لإنشاء مستخدمين جدد';
      }

      final newUser = await _authService.createUser(
        username: username,
        email: email,
        password: password,
        fullName: fullName,
        phone: phone,
        role: role,
        warehouseId: warehouseId,
      );

      return newUser;
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      _setLoading(true);
      _clearError();

      await _authService.updateUserProfile(updatedUser);
      
      // Update current user if it's the same user
      if (_currentUser?.id == updatedUser.id) {
        _currentUser = updatedUser;
        notifyListeners();
      }
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      _setLoading(true);
      _clearError();

      await _authService.changePassword(currentPassword, newPassword);
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      _clearError();

      // Clear notifications on logout
      _notificationService.clearNotifications();

      // Sign out from auth service (clears all session data)
      await _authService.signOut();

      // Clear current user
      _currentUser = null;

      if (kDebugMode) {
        print('✅ User signed out successfully');
      }

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      if (kDebugMode) {
        print('❌ Sign out error: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Check if user has specific permission
  bool hasPermission(String permission) {
    return _authService.hasPermission(permission);
  }

  // Get user's warehouse ID
  String? get userWarehouseId => _currentUser?.warehouseId;

  // Get user's role display name
  String get userRoleDisplayName {
    switch (_currentUser?.role) {
      case 'super_admin':
        return 'مدير أعلى';
      case 'admin':
        return 'مدير إداري';
      case 'agent':
        return 'وكيل';
      case 'showroom':
        return 'مستخدم معرض';
      default:
        return 'غير محدد';
    }
  }

  // Setup user session after successful login
  Future<void> _setupUserSession() async {
    try {
      if (_currentUser != null) {
        // Initialize notifications for user
        await _notificationService.initialize();
        
        if (kDebugMode) {
          print('User session setup completed for: ${_currentUser!.fullName}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error setting up user session: $e');
      }
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Refresh current user data
  Future<void> refreshCurrentUser() async {
    try {
      if (_currentUser != null) {
        _setLoading(true);
        // Reload user data from the auth service
        await _authService.initialize();
        _currentUser = _authService.currentUser;
        notifyListeners();
      }
    } catch (e) {
      _setError('خطأ في تحديث بيانات المستخدم: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Check if current user can access specific warehouse
  bool canAccessWarehouse(String warehouseId) {
    if (_currentUser == null) return false;
    
    // Super admin and admin can access all warehouses
    if (isSuperAdmin || isAdmin) return true;
    
    // Agents can only access their own warehouse
    if (isAgent) return _currentUser!.warehouseId == warehouseId;
    
    // Showroom users can access showroom warehouses
    if (isShowroom) {
      // This would need to be checked against warehouse type
      return true; // Simplified for now
    }
    
    return false;
  }

  // Get accessible warehouse IDs for current user
  List<String> getAccessibleWarehouseIds() {
    if (_currentUser == null) return [];
    
    // This would typically be fetched from the data service
    // For now, return based on user role
    if (isSuperAdmin || isAdmin) {
      return []; // Empty list means all warehouses
    } else if (isAgent && _currentUser!.warehouseId != null) {
      return [_currentUser!.warehouseId!];
    } else if (isShowroom) {
      return []; // Would return showroom warehouse IDs
    }
    
    return [];
  }
}
