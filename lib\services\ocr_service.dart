import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:image/image.dart' as img;

class OCRService {
  static final OCRService _instance = OCRService._internal();
  factory OCRService() => _instance;
  OCRService._internal();

  static OCRService get instance => _instance;

  final TextRecognizer _textRecognizer = TextRecognizer();

  /// Extract text from image file
  Future<OCRResult> extractTextFromImage(File imageFile) async {
    try {
      debugPrint('Starting OCR extraction for: ${imageFile.path}');
      
      // Preprocess image for better OCR results
      final processedImage = await _preprocessImage(imageFile);
      
      // Create InputImage from processed file
      final inputImage = InputImage.fromFile(processedImage);
      
      // Perform text recognition
      final RecognizedText recognizedText = await _textRecognizer.processImage(inputImage);
      
      // Extract and process text
      final extractedText = recognizedText.text;
      final confidence = _calculateConfidence(recognizedText);
      
      // Analyze text for specific patterns
      final analysis = _analyzeText(extractedText);
      
      debugPrint('OCR completed. Text length: ${extractedText.length}');
      debugPrint('Confidence: ${confidence.toStringAsFixed(2)}');
      
      return OCRResult(
        text: extractedText,
        confidence: confidence,
        isMotorFingerprint: analysis.isMotorFingerprint,
        isIdCard: analysis.isIdCard,
        isChassisNumber: analysis.isChassisNumber,
        extractedData: analysis.extractedData,
        blocks: recognizedText.blocks,
      );
    } catch (e) {
      debugPrint('OCR extraction failed: $e');
      return OCRResult(
        text: '',
        confidence: 0.0,
        isMotorFingerprint: false,
        isIdCard: false,
        isChassisNumber: false,
        extractedData: {},
        blocks: [],
      );
    }
  }

  /// Preprocess image for better OCR results
  Future<File> _preprocessImage(File originalFile) async {
    try {
      // Read image
      final bytes = await originalFile.readAsBytes();
      img.Image? image = img.decodeImage(bytes);
      
      if (image == null) return originalFile;
      
      // Apply image enhancements
      image = img.adjustColor(image, contrast: 1.2, brightness: 1.1);
      image = img.gaussianBlur(image, radius: 1);
      
      // Convert to grayscale for better text recognition
      image = img.grayscale(image);
      
      // Increase contrast
      image = img.adjustColor(image, contrast: 1.5);
      
      // Save processed image
      final processedPath = '${originalFile.path}_processed.jpg';
      final processedFile = File(processedPath);
      await processedFile.writeAsBytes(img.encodeJpg(image, quality: 95));
      
      return processedFile;
    } catch (e) {
      debugPrint('Image preprocessing failed: $e');
      return originalFile;
    }
  }

  /// Calculate overall confidence from recognized text
  double _calculateConfidence(RecognizedText recognizedText) {
    if (recognizedText.blocks.isEmpty) return 0.0;
    
    double totalConfidence = 0.0;
    int elementCount = 0;
    
    for (final block in recognizedText.blocks) {
      for (final line in block.lines) {
        for (final element in line.elements) {
          // Note: ML Kit doesn't provide confidence scores directly
          // We estimate based on text characteristics
          totalConfidence += _estimateElementConfidence(element.text);
          elementCount++;
        }
      }
    }
    
    return elementCount > 0 ? totalConfidence / elementCount : 0.0;
  }

  /// Estimate confidence for a text element
  double _estimateElementConfidence(String text) {
    if (text.isEmpty) return 0.0;
    
    double confidence = 0.5; // Base confidence
    
    // Higher confidence for longer text
    if (text.length > 3) confidence += 0.2;
    if (text.length > 6) confidence += 0.1;
    
    // Higher confidence for alphanumeric text
    if (RegExp(r'^[a-zA-Z0-9\u0600-\u06FF\s]+$').hasMatch(text)) {
      confidence += 0.2;
    }
    
    // Lower confidence for single characters
    if (text.length == 1) confidence -= 0.3;
    
    return confidence.clamp(0.0, 1.0);
  }

  /// Analyze extracted text for specific patterns
  TextAnalysis _analyzeText(String text) {
    final analysis = TextAnalysis();
    final cleanText = text.trim().toLowerCase();

    // Check if it's a motor fingerprint
    analysis.isMotorFingerprint = _isMotorFingerprint(cleanText);

    // Check if it's an ID card
    analysis.isIdCard = _isIdCard(cleanText);

    // Check if it's a chassis number
    analysis.isChassisNumber = _isChassisNumber(cleanText);

    // Extract specific data based on type
    if (analysis.isMotorFingerprint) {
      analysis.extractedData = _extractMotorFingerprintData(text);
    } else if (analysis.isIdCard) {
      analysis.extractedData = _extractEgyptianIdCardData(text);
    } else if (analysis.isChassisNumber) {
      analysis.extractedData = _extractChassisNumberData(text);
    }

    return analysis;
  }

  /// Check if text contains motor fingerprint patterns
  bool _isMotorFingerprint(String text) {
    // Common motor fingerprint patterns - enhanced for alphanumeric codes
    final patterns = [
      r'[A-Z0-9]{6,}', // Alphanumeric codes (letters + numbers)
      r'[A-Z]{2,4}\d{4,}', // Engine codes (letters followed by numbers)
      r'\d{2,4}[A-Z]{2,4}', // Numbers followed by letters
      r'[A-Z]\d{4,}[A-Z]', // Letter-numbers-letter pattern
      r'\d{4,}', // Serial numbers (fallback)
      r'[a-z]{2,3}\d{4,}', // Engine codes (lowercase)
      r'model|موديل',
      r'engine|محرك',
      r'motor|موتور',
      r'bike|دراجة',
      r'motorcycle|موتوسيكل',
      r'chassis|شاسيه',
      r'frame|إطار',
      r'vin|رقم',
      r'fingerprint|بصمة',
      r'code|كود',
      r'honda|yamaha|suzuki|kawasaki|bajaj|tvs|hero',
      r'هوندا|ياماها|سوزوكي|كاواساكي|باجاج',
    ];

    for (final pattern in patterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        return true;
      }
    }

    return false;
  }

  /// Check if text contains ID card patterns
  bool _isIdCard(String text) {
    // Common ID card patterns
    final patterns = [
      r'بطاقة|رقم قومي|هوية',
      r'national|id|identity',
      r'\d{14}', // Egyptian national ID pattern
      r'تاريخ الميلاد|birth|date',
      r'محل الميلاد|place|birth',
      r'الجنسية|nationality',
    ];
    
    for (final pattern in patterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        return true;
      }
    }
    
    return false;
  }

  /// Extract motor fingerprint specific data - Enhanced for alphanumeric codes
  Map<String, String> _extractMotorFingerprintData(String text) {
    final data = <String, String>{};

    // Clean and prepare text for better extraction
    final cleanText = _cleanMotorFingerprintText(text);

    // Extract alphanumeric motor fingerprint codes (priority patterns)
    final alphanumericPatterns = [
      // Common motorcycle fingerprint patterns (letters + numbers)
      RegExp(r'\b[A-Z]{2,4}\d{4,8}[A-Z]?\b'), // e.g., ABC1234D, XY5678
      RegExp(r'\b\d{2,4}[A-Z]{2,4}\d{0,4}\b'), // e.g., 12AB34, 1234XY
      RegExp(r'\b[A-Z]\d{4,8}[A-Z]\b'), // e.g., A1234B, X56789Y
      RegExp(r'\b[A-Z]{1,2}\d{6,8}\b'), // e.g., A123456, XY1234567
      RegExp(r'\b\d{4,6}[A-Z]{2,3}\b'), // e.g., 1234AB, 567890XYZ
      RegExp(r'\b[A-Z0-9]{8,12}\b'), // Mixed alphanumeric 8-12 chars
    ];

    String? bestFingerprint;
    int bestScore = 0;

    // Try each pattern and score the results
    for (final pattern in alphanumericPatterns) {
      final matches = pattern.allMatches(cleanText);
      for (final match in matches) {
        final candidate = match.group(0)!;
        final score = _scoreMotorFingerprint(candidate);

        if (score > bestScore) {
          bestScore = score;
          bestFingerprint = candidate;
        }
      }
    }

    // If no good alphanumeric match, try numeric patterns as fallback
    if (bestFingerprint == null || bestScore < 3) {
      final numericPatterns = [
        RegExp(r'\b\d{6,10}\b'), // Pure numeric 6-10 digits
        RegExp(r'\b\d{4,5}\b'), // Shorter numeric codes
      ];

      for (final pattern in numericPatterns) {
        final matches = pattern.allMatches(cleanText);
        for (final match in matches) {
          final candidate = match.group(0)!;
          final score = _scoreMotorFingerprint(candidate);

          if (score > bestScore) {
            bestScore = score;
            bestFingerprint = candidate;
          }
        }
      }
    }

    if (bestFingerprint != null) {
      data['motorFingerprint'] = bestFingerprint;
      data['confidence'] = (bestScore / 10.0).toString();
    }

    // Extract additional information
    _extractMotorAdditionalInfo(text, data);

    return data;
  }

  /// Clean motor fingerprint text for better extraction
  String _cleanMotorFingerprintText(String text) {
    // Remove common noise and normalize
    String cleaned = text.toUpperCase();

    // Remove common prefixes/suffixes that might interfere
    cleaned = cleaned.replaceAll(RegExp(r'\b(MOTOR|ENGINE|BIKE|SERIAL|NUMBER|CODE|MODEL)\b'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\b(موتور|محرك|دراجة|مسلسل|رقم|كود|موديل)\b'), '');

    // Remove special characters but keep alphanumeric
    cleaned = cleaned.replaceAll(RegExp(r'[^A-Z0-9\s]'), ' ');

    // Normalize spaces
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();

    return cleaned;
  }

  /// Score a potential motor fingerprint based on characteristics
  int _scoreMotorFingerprint(String candidate) {
    int score = 0;

    // Length scoring (optimal length is 6-10 characters)
    if (candidate.length >= 6 && candidate.length <= 10) {
      score += 3;
    } else if (candidate.length >= 4 && candidate.length <= 12) {
      score += 2;
    } else if (candidate.length >= 3) {
      score += 1;
    }

    // Mixed alphanumeric gets higher score
    final hasLetters = RegExp(r'[A-Z]').hasMatch(candidate);
    final hasNumbers = RegExp(r'\d').hasMatch(candidate);

    if (hasLetters && hasNumbers) {
      score += 4; // Best case: mixed letters and numbers
    } else if (hasNumbers && candidate.length >= 6) {
      score += 2; // Pure numbers but good length
    } else if (hasLetters) {
      score += 1; // Pure letters (less common)
    }

    // Pattern recognition bonus
    if (RegExp(r'^[A-Z]{2,3}\d{4,}$').hasMatch(candidate)) {
      score += 2; // Common pattern: letters followed by numbers
    }
    if (RegExp(r'^\d{4,6}[A-Z]{2,3}$').hasMatch(candidate)) {
      score += 2; // Common pattern: numbers followed by letters
    }
    if (RegExp(r'^[A-Z]\d{4,}[A-Z]$').hasMatch(candidate)) {
      score += 3; // High-confidence pattern: letter-numbers-letter
    }

    // Avoid obviously wrong patterns
    if (RegExp(r'^0+$').hasMatch(candidate)) {
      score = 0; // All zeros
    }
    if (RegExp(r'^1+$').hasMatch(candidate)) {
      score = 0; // All ones
    }

    return score;
  }

  /// Extract additional motor information
  void _extractMotorAdditionalInfo(String text, Map<String, String> data) {
    // Extract brand/manufacturer
    final brandPatterns = [
      RegExp(r'\b(HONDA|YAMAHA|SUZUKI|KAWASAKI|BAJAJ|TVS|HERO|KTM|DUCATI|BMW)\b', caseSensitive: false),
      RegExp(r'\b(هوندا|ياماها|سوزوكي|كاواساكي|باجاج)\b', caseSensitive: false),
    ];

    for (final pattern in brandPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        data['brand'] = match.group(0)!.toUpperCase();
        break;
      }
    }

    // Extract model year
    final yearPattern = RegExp(r'\b(19|20)\d{2}\b');
    final yearMatch = yearPattern.firstMatch(text);
    if (yearMatch != null) {
      final year = int.tryParse(yearMatch.group(0)!);
      if (year != null && year >= 1990 && year <= DateTime.now().year + 1) {
        data['year'] = yearMatch.group(0)!;
      }
    }

    // Extract engine capacity
    final ccPattern = RegExp(r'\b\d{2,4}\s*(CC|سي سي)\b', caseSensitive: false);
    final ccMatch = ccPattern.firstMatch(text);
    if (ccMatch != null) {
      data['engineCapacity'] = ccMatch.group(0)!;
    }

    // Extract model information
    final modelPattern = RegExp(r'(MODEL|موديل)[:\s]*([A-Z0-9\s]{2,20})', caseSensitive: false);
    final modelMatch = modelPattern.firstMatch(text);
    if (modelMatch != null) {
      data['model'] = modelMatch.group(2)!.trim();
    }
  }

  /// Extract ID card specific data
  Map<String, String> _extractIdCardData(String text) {
    final data = <String, String>{};
    
    // Extract national ID number (14 digits for Egypt)
    final idMatches = RegExp(r'\d{14}').allMatches(text);
    if (idMatches.isNotEmpty) {
      data['national_id'] = idMatches.first.group(0) ?? '';
    }
    
    // Extract name (usually the longest Arabic text line)
    final lines = text.split('\n');
    String longestArabicLine = '';
    for (final line in lines) {
      if (RegExp(r'[\u0600-\u06FF]').hasMatch(line) && line.length > longestArabicLine.length) {
        longestArabicLine = line.trim();
      }
    }
    if (longestArabicLine.isNotEmpty) {
      data['name'] = longestArabicLine;
    }
    
    // Extract birth date
    final birthDateMatch = RegExp(r'\d{1,2}[/\-]\d{1,2}[/\-]\d{4}').firstMatch(text);
    if (birthDateMatch != null) {
      data['birth_date'] = birthDateMatch.group(0) ?? '';
    }
    
    return data;
  }

  /// Extract Egyptian ID card specific data with Arabic numeral support
  Map<String, String> _extractEgyptianIdCardData(String text) {
    final data = <String, String>{};

    // Convert Arabic numerals to English numerals for processing
    String processedText = _convertArabicNumeralsToEnglish(text);

    // Extract national ID number (14 digits for Egypt)
    final idPattern = RegExp(r'\b\d{14}\b');
    final idMatch = idPattern.firstMatch(processedText);
    if (idMatch != null) {
      data['nationalId'] = idMatch.group(0)!;
    }

    // Extract name - Egyptian ID cards have specific patterns
    final namePatterns = [
      // Arabic patterns
      RegExp(r'(?:الاسم|اسم)\s*:?\s*([^\n\r\d]+)', caseSensitive: false),
      RegExp(r'(?:Name)\s*:?\s*([^\n\r\d]+)', caseSensitive: false),
      // Look for Arabic names (6+ Arabic characters)
      RegExp(r'([أ-ي\s]{6,})', caseSensitive: false),
      // Mixed Arabic/English names
      RegExp(r'([A-Za-z\u0600-\u06FF\s]{6,})', caseSensitive: false),
    ];

    for (final pattern in namePatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        if (match.group(1) != null) {
          final name = match.group(1)!.trim();
          // Validate name (should be at least 6 chars, no numbers, not common words)
          if (name.length >= 6 &&
              !RegExp(r'\d').hasMatch(name) &&
              !_isCommonWord(name.toLowerCase())) {
            data['name'] = name;
            break;
          }
        }
      }
      if (data.containsKey('name')) break;
    }

    // Extract address - Egyptian ID cards show governorate and area
    final addressPatterns = [
      RegExp(r'(?:العنوان|عنوان|Address)\s*:?\s*([^\n\r]+)', caseSensitive: false),
      RegExp(r'(?:محافظة|Governorate)\s*:?\s*([^\n\r]+)', caseSensitive: false),
      RegExp(r'(?:المحافظة)\s*:?\s*([^\n\r]+)', caseSensitive: false),
      // Common Egyptian governorates
      RegExp(r'(القاهرة|الجيزة|الإسكندرية|الشرقية|الغربية|المنوفية|الدقهلية|البحيرة|كفر الشيخ|دمياط|الفيوم|بني سويف|المنيا|أسيوط|سوهاج|قنا|الأقصر|أسوان|البحر الأحمر|الوادي الجديد|مطروح|شمال سيناء|جنوب سيناء|بورسعيد|السويس|الإسماعيلية)', caseSensitive: false),
    ];

    for (final pattern in addressPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        final address = match.group(1)!.trim();
        if (address.length > 3) {
          data['address'] = address;
          break;
        }
      }
    }

    // Extract birth date from national ID if available
    if (data.containsKey('nationalId')) {
      final nationalId = data['nationalId']!;
      if (nationalId.length == 14) {
        // Egyptian national ID format: YYMMDDGGCCCN
        try {
          final year = int.parse(nationalId.substring(0, 2));
          final month = int.parse(nationalId.substring(2, 4));
          final day = int.parse(nationalId.substring(4, 6));

          // Determine century (if year > 30, assume 19xx, else 20xx)
          final fullYear = year > 30 ? 1900 + year : 2000 + year;

          if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
            data['birthDate'] = '$day/$month/$fullYear';
          }
        } catch (e) {
          // Invalid date in national ID
        }
      }
    }

    return data;
  }

  /// Convert Arabic numerals (٠١٢٣٤٥٦٧٨٩) to English numerals (0123456789)
  String _convertArabicNumeralsToEnglish(String text) {
    const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
    const englishNumerals = '0123456789';

    String result = text;
    for (int i = 0; i < arabicNumerals.length; i++) {
      result = result.replaceAll(arabicNumerals[i], englishNumerals[i]);
    }
    return result;
  }

  /// Check if a word is a common word that shouldn't be considered a name
  bool _isCommonWord(String word) {
    const commonWords = [
      'جمهورية', 'مصر', 'العربية', 'بطاقة', 'رقم', 'قومي', 'الرقم', 'القومي',
      'egypt', 'arab', 'republic', 'card', 'national', 'number', 'id',
      'الاسم', 'اسم', 'name', 'العنوان', 'عنوان', 'address', 'محافظة',
      'تاريخ', 'الميلاد', 'birth', 'date', 'male', 'female', 'ذكر', 'أنثى'
    ];

    return commonWords.any((commonWord) => word.contains(commonWord));
  }

  /// Check if text contains chassis number patterns
  bool _isChassisNumber(String text) {
    // Common chassis number patterns
    final patterns = [
      r'chassis|شاسيه',
      r'frame|إطار',
      r'vin|رقم',
      r'[a-z0-9]{17}', // Standard VIN format
      r'[a-z]{2,3}\d{6,}', // Chassis codes
      r'\d{6,}[a-z]{2,}', // Number + letters
    ];

    for (final pattern in patterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        return true;
      }
    }

    return false;
  }

  /// Extract chassis number specific data
  Map<String, String> _extractChassisNumberData(String text) {
    final data = <String, String>{};

    // Extract VIN (17 characters alphanumeric)
    final vinMatch = RegExp(r'[A-HJ-NPR-Z0-9]{17}', caseSensitive: false).firstMatch(text);
    if (vinMatch != null) {
      data['chassis_number'] = vinMatch.group(0) ?? '';
    }

    // Extract chassis codes (letters + numbers)
    final chassisMatch = RegExp(r'[A-Z]{2,3}\d{6,}', caseSensitive: false).firstMatch(text);
    if (chassisMatch != null) {
      data['chassis_number'] = chassisMatch.group(0) ?? '';
    }

    // Extract frame numbers
    final frameMatch = RegExp(r'\d{6,}[A-Z]{2,}', caseSensitive: false).firstMatch(text);
    if (frameMatch != null) {
      data['chassis_number'] = frameMatch.group(0) ?? '';
    }

    return data;
  }

  /// Clean up resources
  void dispose() {
    _textRecognizer.close();
  }
}

class OCRResult {
  final String text;
  final double confidence;
  final bool isMotorFingerprint;
  final bool isIdCard;
  final bool isChassisNumber;
  final Map<String, String> extractedData;
  final List<TextBlock> blocks;

  OCRResult({
    required this.text,
    required this.confidence,
    required this.isMotorFingerprint,
    required this.isIdCard,
    required this.isChassisNumber,
    required this.extractedData,
    required this.blocks,
  });

  @override
  String toString() {
    return 'OCRResult(text: ${text.length} chars, confidence: ${confidence.toStringAsFixed(2)}, '
           'isMotorFingerprint: $isMotorFingerprint, isIdCard: $isIdCard, isChassisNumber: $isChassisNumber)';
  }
}

class TextAnalysis {
  bool isMotorFingerprint = false;
  bool isIdCard = false;
  bool isChassisNumber = false;
  Map<String, String> extractedData = {};
}
