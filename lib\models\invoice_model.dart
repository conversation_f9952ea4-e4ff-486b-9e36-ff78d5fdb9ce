import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class InvoiceModel {
  final String id;
  final String invoiceNumber; // رقم الفاتورة الفريد
  final String type; // customer, agent
  final String? customerId; // For customer invoices
  final String? agentId; // For agent invoices
  final String warehouseId; // المخزن الذي تمت منه العملية
  final String itemId; // معرف الصنف (بصمة الموتور)
  final double itemCost; // تكلفة الصنف الأساسية
  final double sellingPrice; // سعر البيع النهائي
  final double profitAmount; // مبلغ الربح
  final double companyProfitShare; // نصيب المؤسسة من الربح
  final double agentProfitShare; // نصيب الوكيل من الربح (إن وجد)
  final String status; // pending, paid, cancelled
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy; // User ID who created this invoice
  final Map<String, dynamic>? customerData; // بيانات العميل المستخرجة من بطاقة الهوية
  final List<String>? customerIdImages; // صور بطاقة الهوية (وش وظهر)
  final Map<String, dynamic>? additionalData;

  // Additional customer fields for convenience
  String? get customerName => customerData?['name'];
  String? get customerPhone => customerData?['phone'];
  String? get customerAddress => customerData?['address'];
  String? get customerNationalId => customerData?['nationalId'];

  // Additional financial fields for convenience
  double get purchasePrice => itemCost;
  double get agentCommission => agentProfitShare;
  double get companyShare => companyProfitShare;

  InvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.type,
    this.customerId,
    this.agentId,
    required this.warehouseId,
    required this.itemId,
    required this.itemCost,
    required this.sellingPrice,
    required this.profitAmount,
    required this.companyProfitShare,
    this.agentProfitShare = 0,
    this.status = 'pending',
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.customerData,
    this.customerIdImages,
    this.additionalData,
  });

  // Convert from Firestore document
  factory InvoiceModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return InvoiceModel(
      id: doc.id,
      invoiceNumber: data['invoiceNumber'] ?? '',
      type: data['type'] ?? '',
      customerId: data['customerId'],
      agentId: data['agentId'],
      warehouseId: data['warehouseId'] ?? '',
      itemId: data['itemId'] ?? '',
      itemCost: (data['itemCost'] ?? 0).toDouble(),
      sellingPrice: (data['sellingPrice'] ?? 0).toDouble(),
      profitAmount: (data['profitAmount'] ?? 0).toDouble(),
      companyProfitShare: (data['companyProfitShare'] ?? 0).toDouble(),
      agentProfitShare: (data['agentProfitShare'] ?? 0).toDouble(),
      status: data['status'] ?? 'pending',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      customerData: _parseMapFromFirestore(data['customerData']),
      customerIdImages: data['customerIdImages'] != null
          ? List<String>.from(data['customerIdImages'])
          : null,
      additionalData: _parseMapFromFirestore(data['additionalData']),
    );
  }

  // Convert from Map (for local database)
  factory InvoiceModel.fromMap(Map<String, dynamic> map) {
    return InvoiceModel(
      id: map['id'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      type: map['type'] ?? '',
      customerId: map['customerId'],
      agentId: map['agentId'],
      warehouseId: map['warehouseId'] ?? '',
      itemId: map['itemId'] ?? '',
      itemCost: (map['itemCost'] ?? 0).toDouble(),
      sellingPrice: (map['sellingPrice'] ?? 0).toDouble(),
      profitAmount: (map['profitAmount'] ?? 0).toDouble(),
      companyProfitShare: (map['companyProfitShare'] ?? 0).toDouble(),
      agentProfitShare: (map['agentProfitShare'] ?? 0).toDouble(),
      status: map['status'] ?? 'pending',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      createdBy: map['createdBy'] ?? '',
      customerData: _decodeFromDatabase<Map<String, dynamic>>(map['customerData']),
      customerIdImages: _decodeFromDatabase<List<String>>(map['customerIdImages']),
      additionalData: _decodeFromDatabase<Map<String, dynamic>>(map['additionalData']),
    );
  }

  // Convert to Map (for Firestore and local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'type': type,
      'customerId': customerId,
      'agentId': agentId,
      'warehouseId': warehouseId,
      'itemId': itemId,
      'itemCost': itemCost,
      'sellingPrice': sellingPrice,
      'profitAmount': profitAmount,
      'companyProfitShare': companyProfitShare,
      'agentProfitShare': agentProfitShare,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'customerData': _encodeForDatabase(customerData),
      'customerIdImages': _encodeForDatabase(customerIdImages),
      'additionalData': _encodeForDatabase(additionalData),
      'syncStatus': 0,
    };
  }

  // Helper method to encode data for database storage
  String? _encodeForDatabase(dynamic data) {
    if (data == null) return null;
    try {
      return jsonEncode(data);
    } catch (e) {
      return data.toString();
    }
  }

  // Helper method to decode data from database storage
  static T? _decodeFromDatabase<T>(dynamic data) {
    if (data == null) return null;

    try {
      if (data is String) {
        final decoded = jsonDecode(data);
        if (T.toString() == 'Map<String, dynamic>' && decoded is Map) {
          return Map<String, dynamic>.from(decoded) as T;
        } else if (T.toString() == 'List<String>' && decoded is List) {
          return List<String>.from(decoded) as T;
        }
        return decoded as T;
      } else {
        // Data is already in the correct format
        if (T.toString() == 'Map<String, dynamic>' && data is Map) {
          return Map<String, dynamic>.from(data) as T;
        } else if (T.toString() == 'List<String>' && data is List) {
          return List<String>.from(data) as T;
        }
        return data as T;
      }
    } catch (e) {
      return null;
    }
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'invoiceNumber': invoiceNumber,
      'type': type,
      'customerId': customerId,
      'agentId': agentId,
      'warehouseId': warehouseId,
      'itemId': itemId,
      'itemCost': itemCost,
      'sellingPrice': sellingPrice,
      'profitAmount': profitAmount,
      'companyProfitShare': companyProfitShare,
      'agentProfitShare': agentProfitShare,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'customerData': customerData,
      'customerIdImages': customerIdImages,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  InvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? type,
    String? customerId,
    String? agentId,
    String? warehouseId,
    String? itemId,
    double? itemCost,
    double? sellingPrice,
    double? profitAmount,
    double? companyProfitShare,
    double? agentProfitShare,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? customerData,
    List<String>? customerIdImages,
    Map<String, dynamic>? additionalData,
  }) {
    return InvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      type: type ?? this.type,
      customerId: customerId ?? this.customerId,
      agentId: agentId ?? this.agentId,
      warehouseId: warehouseId ?? this.warehouseId,
      itemId: itemId ?? this.itemId,
      itemCost: itemCost ?? this.itemCost,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      profitAmount: profitAmount ?? this.profitAmount,
      companyProfitShare: companyProfitShare ?? this.companyProfitShare,
      agentProfitShare: agentProfitShare ?? this.agentProfitShare,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      customerData: customerData ?? this.customerData,
      customerIdImages: customerIdImages ?? this.customerIdImages,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isCustomerInvoice => type == 'customer';
  bool get isAgentInvoice => type == 'agent';
  bool get isPending => status == 'pending';
  bool get isPaid => status == 'paid';
  bool get isCancelled => status == 'cancelled';



  /// Parse Map from Firestore data (handles both Map and String)
  static Map<String, dynamic>? _parseMapFromFirestore(dynamic data) {
    if (data == null) return null;

    if (data is Map<String, dynamic>) {
      return data;
    }

    if (data is String) {
      try {
        // Try to parse as JSON string
        final decoded = jsonDecode(data);
        if (decoded is Map<String, dynamic>) {
          return decoded;
        }
      } catch (e) {
        // If parsing fails, return null
        return null;
      }
    }

    return null;
  }

  @override
  String toString() {
    return 'InvoiceModel(id: $id, invoiceNumber: $invoiceNumber, type: $type, sellingPrice: $sellingPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
