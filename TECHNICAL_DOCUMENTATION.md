# التوثيق التقني - تطبيق آل فرحان للنقل الخفيف

## نظرة عامة على البنية التقنية

### معمارية التطبيق
يتبع التطبيق نمط **Clean Architecture** مع فصل واضح بين الطبقات:

```
Presentation Layer (UI)
    ↓
Business Logic Layer (Providers/Services)
    ↓
Data Layer (Models/Repositories)
    ↓
External Services (Firebase/SQLite)
```

### إدارة الحالة
- **Provider Pattern**: لإدارة حالة التطبيق
- **AuthProvider**: إدارة حالة المصادقة والمستخدم الحالي
- **State Management**: حالة محلية للشاشات المعقدة

## خدمات التطبيق الأساسية

### 1. FirebaseService
```dart
class FirebaseService {
  // تهيئة Firebase
  Future<void> initialize()
  
  // إدارة الاتصال
  Future<bool> isOnline()
  
  // إدارة الإشعارات
  Future<String?> getFCMToken()
}
```

**المسؤوليات:**
- تهيئة Firebase والخدمات المرتبطة
- إدارة حالة الاتصال بالإنترنت
- إعداد Firebase Cloud Messaging
- إدارة الإعدادات الأمنية

### 2. LocalDatabaseService
```dart
class LocalDatabaseService {
  // عمليات CRUD عامة
  Future<int> insert(String table, Map<String, dynamic> data)
  Future<List<Map<String, dynamic>>> query(String table)
  Future<int> update(String table, Map<String, dynamic> data)
  Future<int> delete(String table, String where, List<dynamic> whereArgs)
  
  // إدارة المزامنة
  Future<void> addToSyncQueue(String tableName, String recordId, String operation)
  Future<void> markAsSynced(String table, String recordId)
}
```

**المسؤوليات:**
- إدارة قاعدة البيانات المحلية SQLite
- تنفيذ عمليات CRUD
- إدارة طابور المزامنة
- فهرسة البيانات للبحث السريع

### 3. SyncService
```dart
class SyncService {
  // مزامنة شاملة
  Future<void> syncAll()
  
  // مزامنة حسب النوع
  Future<void> syncUsers()
  Future<void> syncItems()
  Future<void> syncInvoices()
  
  // معالجة طابور المزامنة
  Future<void> processSyncQueue()
}
```

**المسؤوليات:**
- مزامنة البيانات بين المحلي والسحابي
- إدارة التعارضات
- مراقبة حالة الاتصال
- المزامنة التلقائية والدورية

### 4. AuthService
```dart
class AuthService {
  // تسجيل الدخول
  Future<UserModel?> signInWithUsername(String username, String password)
  Future<UserModel?> signInWithEmailAndPassword(String email, String password)
  
  // إدارة المستخدمين
  Future<UserModel?> createUser({...})
  Future<void> updateUserProfile(UserModel user)
  
  // الصلاحيات
  bool hasPermission(String permission)
}
```

**المسؤوليات:**
- إدارة المصادقة والتحقق
- إنشاء وتحديث المستخدمين
- إدارة الصلاحيات والأدوار
- إدارة الجلسات

### 5. ImageService
```dart
class ImageService {
  // التقاط الصور
  Future<File?> takePhoto()
  Future<File?> pickImageFromGallery()
  
  // معالجة الصور
  Future<File> compressImage(File imageFile)
  Future<String> uploadImage(File imageFile)
  
  // تقنية OCR
  Future<String> extractTextFromImage(File imageFile)
  Future<String> extractMotorFingerprintText(File imageFile)
  Future<Map<String, String>> extractIdCardData(File frontImage, File backImage)
}
```

**المسؤوليات:**
- التقاط ومعالجة الصور
- ضغط وتحسين جودة الصور
- رفع الصور للتخزين السحابي
- استخراج النصوص باستخدام OCR

### 6. DataService
```dart
class DataService {
  // إدارة المستخدمين
  Future<List<UserModel>> getUsers({String? role, bool? isActive})
  Future<UserModel?> getUserById(String userId)
  
  // إدارة الأصناف
  Future<List<ItemModel>> getItems({String? warehouseId, String? status})
  Future<ItemModel?> getItemById(String itemId)
  Future<ItemModel> createItem(ItemModel item)
  Future<ItemModel> updateItem(ItemModel item)
  
  // البحث
  Future<List<ItemModel>> searchItemsByFingerprint(String searchText)
}
```

**المسؤوليات:**
- توفير واجهة موحدة للبيانات
- إدارة التبديل بين المحلي والسحابي
- تنفيذ منطق الأعمال
- إدارة التخزين المؤقت

## نماذج البيانات

### UserModel
```dart
class UserModel {
  final String id;
  final String username;
  final String email;
  final String fullName;
  final String phone;
  final String role; // super_admin, admin, agent, showroom
  final String? warehouseId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Helper methods
  bool get isSuperAdmin => role == 'super_admin';
  bool get canManageUsers => isSuperAdmin;
  bool get canManageInventory => isSuperAdmin || isAdmin;
}
```

### ItemModel
```dart
class ItemModel {
  final String id; // Motor fingerprint text
  final String type; // نوع المركبة
  final String model;
  final String color;
  final String brand;
  final String countryOfOrigin;
  final int yearOfManufacture;
  final double purchasePrice;
  final double suggestedSellingPrice;
  final String motorFingerprintImageUrl;
  final String motorFingerprintText;
  final String currentWarehouseId;
  final String status; // available, sold, transferred, returned
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  
  // Helper methods
  bool get isAvailable => status == 'available';
  String get displayName => '$brand $model ($color)';
  double get profitAmount => suggestedSellingPrice - purchasePrice;
}
```

### InvoiceModel
```dart
class InvoiceModel {
  final String id;
  final String invoiceNumber;
  final String type; // customer, agent
  final String? customerId;
  final String? agentId;
  final String warehouseId;
  final String itemId;
  final double itemCost;
  final double sellingPrice;
  final double profitAmount;
  final double companyProfitShare;
  final double agentProfitShare;
  final String status; // pending, paid, cancelled
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final Map<String, dynamic>? customerData;
  final List<String>? customerIdImages;
  
  // Helper methods
  bool get isCustomerInvoice => type == 'customer';
  String? get customerName => customerData?['fullName'];
}
```

## استراتيجية العمل بدون إنترنت

### 1. التخزين المحلي
- **SQLite Database**: نسخة كاملة من البيانات محلياً
- **Shared Preferences**: إعدادات المستخدم والتطبيق
- **File System**: الصور والملفات المؤقتة

### 2. طابور المزامنة
```dart
// إضافة عملية للطابور
await _localDb.addToSyncQueue(
  'items', 
  item.id, 
  'INSERT', 
  item.toMap()
);

// معالجة الطابور عند الاتصال
await _syncService.processSyncQueue();
```

### 3. إدارة التعارضات
- **Last-Write-Wins**: للبيانات العامة
- **Timestamp-based**: مقارنة أوقات التحديث
- **Manual Resolution**: للعمليات المالية الحرجة

### 4. مراقبة الاتصال
```dart
// مراقبة تغيير حالة الاتصال
_connectivity.onConnectivityChanged.listen((results) {
  if (isConnected && !_isSyncing) {
    syncAll();
  }
});
```

## نظام الأمان والصلاحيات

### Firebase Security Rules
```javascript
// مثال على قواعد الأمان
match /items/{itemId} {
  allow read: if isAuthenticated() && (
    isAdminOrSuperAdmin() ||
    isOwnerOfWarehouse(resource.data.currentWarehouseId)
  );
  
  allow create: if canManageInventory();
  allow update: if canManageInventory() || canUpdateItemStatus();
}
```

### إدارة الصلاحيات في التطبيق
```dart
// فحص الصلاحيات
if (authProvider.canManageInventory) {
  // عرض خيارات إدارة المخزون
}

if (authProvider.hasPermission('create_invoices')) {
  // السماح بإنشاء الفواتير
}
```

## تحسين الأداء

### 1. تحميل البيانات
- **Lazy Loading**: تحميل البيانات عند الحاجة
- **Pagination**: تقسيم البيانات الكبيرة
- **Caching**: حفظ البيانات المستخدمة بكثرة

### 2. معالجة الصور
- **Compression**: ضغط الصور قبل الرفع
- **Resizing**: تغيير حجم الصور حسب الاستخدام
- **Format Optimization**: استخدام تنسيقات محسنة

### 3. قاعدة البيانات
- **Indexing**: فهرسة الحقول المستخدمة في البحث
- **Query Optimization**: تحسين الاستعلامات
- **Connection Pooling**: إدارة اتصالات قاعدة البيانات

## معالجة الأخطاء

### 1. استراتيجية معالجة الأخطاء
```dart
try {
  await _dataService.createItem(item);
  AppUtils.showSnackBar(context, 'تم حفظ الصنف بنجاح');
} catch (e) {
  if (e is NetworkException) {
    // حفظ في الطابور للمزامنة لاحقاً
    await _localDb.addToSyncQueue('items', item.id, 'INSERT', item.toMap());
    AppUtils.showSnackBar(context, 'تم حفظ الصنف محلياً، سيتم المزامنة عند توفر الإنترنت');
  } else {
    AppUtils.showSnackBar(context, 'خطأ في حفظ الصنف: $e', isError: true);
  }
}
```

### 2. تسجيل الأخطاء
- **Local Logging**: تسجيل الأخطاء محلياً
- **Remote Logging**: إرسال الأخطاء للخادم
- **User Feedback**: رسائل واضحة للمستخدم

## اختبار التطبيق

### 1. اختبارات الوحدة
```dart
// اختبار نماذج البيانات
test('UserModel should create from map correctly', () {
  final map = {'id': '1', 'username': 'test', ...};
  final user = UserModel.fromMap(map);
  expect(user.id, '1');
  expect(user.username, 'test');
});

// اختبار الخدمات
test('AuthService should authenticate user', () async {
  final user = await authService.signInWithUsername('test', 'password');
  expect(user, isNotNull);
  expect(user!.username, 'test');
});
```

### 2. اختبارات التكامل
```dart
// اختبار المزامنة
testWidgets('Sync service should sync data when online', (tester) async {
  // إعداد البيانات التجريبية
  // تشغيل المزامنة
  // التحقق من النتائج
});
```

### 3. اختبارات الواجهة
```dart
// اختبار شاشة تسجيل الدخول
testWidgets('Login screen should authenticate user', (tester) async {
  await tester.pumpWidget(MyApp());
  
  await tester.enterText(find.byKey(Key('username')), 'testuser');
  await tester.enterText(find.byKey(Key('password')), 'password');
  await tester.tap(find.byKey(Key('login_button')));
  
  await tester.pumpAndSettle();
  
  expect(find.byType(HomeScreen), findsOneWidget);
});
```

## نشر التطبيق

### 1. إعداد البناء
```bash
# بناء للإنتاج
flutter build apk --release
flutter build appbundle --release

# بناء لـ iOS
flutter build ios --release
```

### 2. إعداد Firebase للإنتاج
- تكوين مشروع Firebase منفصل للإنتاج
- تحديث قواعد الأمان
- إعداد النسخ الاحتياطي

### 3. مراقبة الأداء
- Firebase Performance Monitoring
- Crashlytics لتتبع الأخطاء
- Analytics لتتبع الاستخدام

---

هذا التوثيق التقني يوفر فهماً شاملاً لبنية التطبيق وكيفية عمل مكوناته المختلفة.
