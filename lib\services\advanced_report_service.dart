import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart';

import '../models/invoice_model.dart';
import '../models/user_model.dart';
import '../models/warehouse_model.dart';
import '../models/agent_account_model.dart';
import '../models/payment_model.dart';
import '../models/inventory_item_model.dart';
import '../core/utils/app_utils.dart';
import 'data_service.dart';
import 'enhanced_pdf_service.dart';

class AdvancedReportService {
  static final AdvancedReportService _instance = AdvancedReportService._internal();
  factory AdvancedReportService() => _instance;
  AdvancedReportService._internal();

  static AdvancedReportService get instance => _instance;

  final DataService _dataService = DataService.instance;

  /// Generate comprehensive sales report
  Future<Map<String, dynamic>> generateSalesReport({
    DateTime? startDate,
    DateTime? endDate,
    String? agentId,
    String? warehouseId,
    String? status,
  }) async {
    try {
      final invoices = await _dataService.getInvoices();
      final agents = await _dataService.getUsers();
      final warehouses = await _dataService.getWarehouses();

      // Filter invoices based on criteria
      var filteredInvoices = invoices.where((invoice) {
        bool matchesDate = true;
        bool matchesAgent = true;
        bool matchesWarehouse = true;
        bool matchesStatus = true;

        if (startDate != null && endDate != null) {
          matchesDate = invoice.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
                       invoice.createdAt.isBefore(endDate.add(const Duration(days: 1)));
        }

        if (agentId != null && agentId.isNotEmpty) {
          matchesAgent = invoice.agentId == agentId;
        }

        if (warehouseId != null && warehouseId.isNotEmpty) {
          matchesWarehouse = invoice.warehouseId == warehouseId;
        }

        if (status != null && status != 'all') {
          matchesStatus = invoice.status == status;
        }

        return matchesDate && matchesAgent && matchesWarehouse && matchesStatus;
      }).toList();

      // Calculate statistics
      final totalSales = filteredInvoices.fold<double>(0, (sum, invoice) => sum + invoice.totalAmount);
      final totalProfit = filteredInvoices.fold<double>(0, (sum, invoice) => sum + invoice.profitAmount);
      final averageSale = filteredInvoices.isNotEmpty ? totalSales / filteredInvoices.length : 0.0;

      // Group by agent
      final Map<String, List<InvoiceModel>> salesByAgent = {};
      for (final invoice in filteredInvoices) {
        salesByAgent.putIfAbsent(invoice.agentId, () => []).add(invoice);
      }

      // Group by warehouse
      final Map<String, List<InvoiceModel>> salesByWarehouse = {};
      for (final invoice in filteredInvoices) {
        salesByWarehouse.putIfAbsent(invoice.warehouseId, () => []).add(invoice);
      }

      // Group by date (daily)
      final Map<String, List<InvoiceModel>> salesByDate = {};
      for (final invoice in filteredInvoices) {
        final dateKey = DateFormat('yyyy-MM-dd').format(invoice.createdAt);
        salesByDate.putIfAbsent(dateKey, () => []).add(invoice);
      }

      // Top performing agents
      final agentPerformance = salesByAgent.entries.map((entry) {
        final agent = agents.firstWhere((a) => a.id == entry.key, orElse: () => UserModel.empty());
        final agentSales = entry.value.fold<double>(0, (sum, invoice) => sum + invoice.totalAmount);
        final agentProfit = entry.value.fold<double>(0, (sum, invoice) => sum + invoice.profitAmount);
        
        return {
          'agentId': entry.key,
          'agentName': agent.fullName,
          'salesCount': entry.value.length,
          'totalSales': agentSales,
          'totalProfit': agentProfit,
          'averageSale': entry.value.isNotEmpty ? agentSales / entry.value.length : 0.0,
        };
      }).toList();

      agentPerformance.sort((a, b) => (b['totalSales'] as double).compareTo(a['totalSales'] as double));

      // Warehouse performance
      final warehousePerformance = salesByWarehouse.entries.map((entry) {
        final warehouse = warehouses.firstWhere((w) => w.id == entry.key, orElse: () => WarehouseModel.empty());
        final warehouseSales = entry.value.fold<double>(0, (sum, invoice) => sum + invoice.totalAmount);
        
        return {
          'warehouseId': entry.key,
          'warehouseName': warehouse.name,
          'salesCount': entry.value.length,
          'totalSales': warehouseSales,
        };
      }).toList();

      warehousePerformance.sort((a, b) => (b['totalSales'] as double).compareTo(a['totalSales'] as double));

      // Daily sales trend
      final dailySales = salesByDate.entries.map((entry) {
        final dailyTotal = entry.value.fold<double>(0, (sum, invoice) => sum + invoice.totalAmount);
        return {
          'date': entry.key,
          'salesCount': entry.value.length,
          'totalSales': dailyTotal,
        };
      }).toList();

      dailySales.sort((a, b) => (a['date'] as String).compareTo(b['date'] as String));

      return {
        'summary': {
          'totalInvoices': filteredInvoices.length,
          'totalSales': totalSales,
          'totalProfit': totalProfit,
          'averageSale': averageSale,
          'profitMargin': totalSales > 0 ? (totalProfit / totalSales) * 100 : 0.0,
        },
        'agentPerformance': agentPerformance,
        'warehousePerformance': warehousePerformance,
        'dailySales': dailySales,
        'filteredInvoices': filteredInvoices,
        'period': {
          'startDate': startDate,
          'endDate': endDate,
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating sales report: $e');
      }
      rethrow;
    }
  }

  /// Generate inventory report
  Future<Map<String, dynamic>> generateInventoryReport({
    String? warehouseId,
    String? category,
    bool? lowStock,
  }) async {
    try {
      final items = await _dataService.getInventoryItems();
      final warehouses = await _dataService.getWarehouses();

      // Filter items based on criteria
      var filteredItems = items.where((item) {
        bool matchesWarehouse = true;
        bool matchesCategory = true;
        bool matchesLowStock = true;

        if (warehouseId != null && warehouseId.isNotEmpty) {
          matchesWarehouse = item.warehouseId == warehouseId;
        }

        if (category != null && category.isNotEmpty && category != 'all') {
          matchesCategory = item.category == category;
        }

        if (lowStock == true) {
          matchesLowStock = item.quantity <= (item.minQuantity ?? 0);
        }

        return matchesWarehouse && matchesCategory && matchesLowStock;
      }).toList();

      // Calculate statistics
      final totalItems = filteredItems.length;
      final totalValue = filteredItems.fold<double>(0, (sum, item) => sum + (item.quantity * item.costPrice));
      final lowStockItems = filteredItems.where((item) => item.quantity <= (item.minQuantity ?? 0)).length;

      // Group by warehouse
      final Map<String, List<InventoryItemModel>> itemsByWarehouse = {};
      for (final item in filteredItems) {
        itemsByWarehouse.putIfAbsent(item.warehouseId, () => []).add(item);
      }

      // Group by category
      final Map<String, List<InventoryItemModel>> itemsByCategory = {};
      for (final item in filteredItems) {
        itemsByCategory.putIfAbsent(item.category, () => []).add(item);
      }

      // Warehouse inventory summary
      final warehouseInventory = itemsByWarehouse.entries.map((entry) {
        final warehouse = warehouses.firstWhere((w) => w.id == entry.key, orElse: () => WarehouseModel.empty());
        final warehouseValue = entry.value.fold<double>(0, (sum, item) => sum + (item.quantity * item.costPrice));
        final warehouseLowStock = entry.value.where((item) => item.quantity <= (item.minQuantity ?? 0)).length;
        
        return {
          'warehouseId': entry.key,
          'warehouseName': warehouse.name,
          'itemCount': entry.value.length,
          'totalValue': warehouseValue,
          'lowStockCount': warehouseLowStock,
        };
      }).toList();

      // Category summary
      final categoryInventory = itemsByCategory.entries.map((entry) {
        final categoryValue = entry.value.fold<double>(0, (sum, item) => sum + (item.quantity * item.costPrice));
        final categoryQuantity = entry.value.fold<int>(0, (sum, item) => sum + item.quantity);
        
        return {
          'category': entry.key,
          'itemCount': entry.value.length,
          'totalQuantity': categoryQuantity,
          'totalValue': categoryValue,
        };
      }).toList();

      return {
        'summary': {
          'totalItems': totalItems,
          'totalValue': totalValue,
          'lowStockItems': lowStockItems,
          'lowStockPercentage': totalItems > 0 ? (lowStockItems / totalItems) * 100 : 0.0,
        },
        'warehouseInventory': warehouseInventory,
        'categoryInventory': categoryInventory,
        'filteredItems': filteredItems,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating inventory report: $e');
      }
      rethrow;
    }
  }

  /// Generate agent performance report
  Future<Map<String, dynamic>> generateAgentPerformanceReport({
    DateTime? startDate,
    DateTime? endDate,
    String? agentId,
  }) async {
    try {
      final agents = await _dataService.getUsers();
      final invoices = await _dataService.getInvoices();
      final payments = await _dataService.getPayments();
      final agentAccounts = await _dataService.getAgentAccounts();

      // Filter agents (only agents, not admins)
      final agentUsers = agents.where((user) => user.role == 'agent').toList();

      // Filter by specific agent if provided
      final filteredAgents = agentId != null && agentId.isNotEmpty
          ? agentUsers.where((agent) => agent.id == agentId).toList()
          : agentUsers;

      // Filter invoices by date range
      var filteredInvoices = invoices;
      if (startDate != null && endDate != null) {
        filteredInvoices = invoices.where((invoice) {
          return invoice.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
                 invoice.createdAt.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();
      }

      // Filter payments by date range
      var filteredPayments = payments;
      if (startDate != null && endDate != null) {
        filteredPayments = payments.where((payment) {
          return payment.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
                 payment.createdAt.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();
      }

      // Calculate performance for each agent
      final agentPerformance = filteredAgents.map((agent) {
        final agentInvoices = filteredInvoices.where((invoice) => invoice.agentId == agent.id).toList();
        final agentPayments = filteredPayments.where((payment) => payment.agentId == agent.id).toList();
        final agentAccount = agentAccounts.firstWhere((account) => account.agentId == agent.id, 
                                                     orElse: () => AgentAccountModel.empty());

        final totalSales = agentInvoices.fold<double>(0, (sum, invoice) => sum + invoice.totalAmount);
        final totalProfit = agentInvoices.fold<double>(0, (sum, invoice) => sum + invoice.profitAmount);
        final totalPayments = agentPayments.fold<double>(0, (sum, payment) => sum + payment.amount);

        return {
          'agentId': agent.id,
          'agentName': agent.fullName,
          'agentPhone': agent.phoneNumber,
          'salesCount': agentInvoices.length,
          'totalSales': totalSales,
          'totalProfit': totalProfit,
          'totalPayments': totalPayments,
          'currentBalance': agentAccount.currentBalance,
          'averageSale': agentInvoices.isNotEmpty ? totalSales / agentInvoices.length : 0.0,
          'profitMargin': totalSales > 0 ? (totalProfit / totalSales) * 100 : 0.0,
          'paymentRatio': totalSales > 0 ? (totalPayments / totalSales) * 100 : 0.0,
        };
      }).toList();

      // Sort by total sales
      agentPerformance.sort((a, b) => (b['totalSales'] as double).compareTo(a['totalSales'] as double));

      // Calculate overall statistics
      final totalAgentSales = agentPerformance.fold<double>(0, (sum, agent) => sum + (agent['totalSales'] as double));
      final totalAgentProfit = agentPerformance.fold<double>(0, (sum, agent) => sum + (agent['totalProfit'] as double));
      final totalAgentPayments = agentPerformance.fold<double>(0, (sum, agent) => sum + (agent['totalPayments'] as double));

      return {
        'summary': {
          'totalAgents': filteredAgents.length,
          'activeAgents': agentPerformance.where((agent) => (agent['salesCount'] as int) > 0).length,
          'totalSales': totalAgentSales,
          'totalProfit': totalAgentProfit,
          'totalPayments': totalAgentPayments,
          'averageSalesPerAgent': filteredAgents.isNotEmpty ? totalAgentSales / filteredAgents.length : 0.0,
        },
        'agentPerformance': agentPerformance,
        'period': {
          'startDate': startDate,
          'endDate': endDate,
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating agent performance report: $e');
      }
      rethrow;
    }
  }

  /// Export sales report to Excel
  Future<Uint8List> exportSalesReportToExcel(Map<String, dynamic> reportData) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sales Report'];

      // Remove default sheet
      excel.delete('Sheet1');

      // Add headers
      final headers = ['التاريخ', 'الوكيل', 'المخزن', 'رقم الفاتورة', 'المبلغ', 'الربح', 'الحالة'];
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = headers[i];
      }

      // Add data
      final invoices = reportData['filteredInvoices'] as List<InvoiceModel>;
      for (int i = 0; i < invoices.length; i++) {
        final invoice = invoices[i];
        final row = i + 1;

        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value =
            DateFormat('yyyy-MM-dd').format(invoice.createdAt);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = invoice.agentId;
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = invoice.warehouseId;
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = invoice.invoiceNumber;
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = invoice.totalAmount;
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = invoice.profitAmount;
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row)).value = invoice.status;
      }

      // Add summary sheet
      final summarySheet = excel['Summary'];
      final summary = reportData['summary'] as Map<String, dynamic>;

      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = 'إجمالي الفواتير';
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = summary['totalInvoices'];

      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1)).value = 'إجمالي المبيعات';
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 1)).value = summary['totalSales'];

      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2)).value = 'إجمالي الأرباح';
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 2)).value = summary['totalProfit'];

      return excel.encode()!;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting to Excel: $e');
      }
      rethrow;
    }
  }

  /// Export report to PDF
  Future<Uint8List> exportReportToPDF({
    required String reportType,
    required Map<String, dynamic> reportData,
    String? title,
  }) async {
    try {
      return await EnhancedPdfService.instance.generateReportPDF(
        reportType: reportType,
        reportData: reportData,
        title: title ?? 'تقرير $reportType',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting report to PDF: $e');
      }
      rethrow;
    }
  }

  /// Generate profit and loss report
  Future<Map<String, dynamic>> generateProfitLossReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final invoices = await _dataService.getInvoices();
      final payments = await _dataService.getPayments();
      final items = await _dataService.getInventoryItems();

      // Filter by date range
      var filteredInvoices = invoices;
      var filteredPayments = payments;

      if (startDate != null && endDate != null) {
        filteredInvoices = invoices.where((invoice) {
          return invoice.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
                 invoice.createdAt.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();

        filteredPayments = payments.where((payment) {
          return payment.createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
                 payment.createdAt.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();
      }

      // Calculate revenue
      final totalRevenue = filteredInvoices.fold<double>(0, (sum, invoice) => sum + invoice.totalAmount);
      final totalCost = filteredInvoices.fold<double>(0, (sum, invoice) => sum + invoice.costAmount);
      final grossProfit = totalRevenue - totalCost;

      // Calculate expenses (this would need to be expanded based on your expense tracking)
      final operatingExpenses = 0.0; // Placeholder - add actual expense calculation

      // Net profit
      final netProfit = grossProfit - operatingExpenses;

      // Profit margin
      final grossProfitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0.0;
      final netProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0.0;

      // Monthly breakdown
      final Map<String, Map<String, double>> monthlyData = {};
      for (final invoice in filteredInvoices) {
        final monthKey = DateFormat('yyyy-MM').format(invoice.createdAt);
        monthlyData.putIfAbsent(monthKey, () => {
          'revenue': 0.0,
          'cost': 0.0,
          'profit': 0.0,
        });
        monthlyData[monthKey]!['revenue'] = monthlyData[monthKey]!['revenue']! + invoice.totalAmount;
        monthlyData[monthKey]!['cost'] = monthlyData[monthKey]!['cost']! + invoice.costAmount;
        monthlyData[monthKey]!['profit'] = monthlyData[monthKey]!['profit']! + invoice.profitAmount;
      }

      return {
        'summary': {
          'totalRevenue': totalRevenue,
          'totalCost': totalCost,
          'grossProfit': grossProfit,
          'operatingExpenses': operatingExpenses,
          'netProfit': netProfit,
          'grossProfitMargin': grossProfitMargin,
          'netProfitMargin': netProfitMargin,
        },
        'monthlyBreakdown': monthlyData,
        'period': {
          'startDate': startDate,
          'endDate': endDate,
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating profit/loss report: $e');
      }
      rethrow;
    }
  }
}
