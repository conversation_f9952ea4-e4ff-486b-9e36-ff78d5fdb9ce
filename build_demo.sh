#!/bin/bash

echo "========================================"
echo "   بناء النسخة التجريبية لتطبيق آل فرحان"
echo "========================================"
echo

echo "[1/5] نسخ ملف pubspec التجريبي..."
cp pubspec_demo.yaml pubspec.yaml

echo "[2/5] نسخ ملف main التجريبي..."
cp lib/main_demo.dart lib/main.dart

echo "[3/5] تحميل المكتبات..."
flutter pub get

echo "[4/5] بناء APK..."
flutter build apk --release

echo "[5/5] نسخ APK إلى مجلد التوزيع..."
mkdir -p dist
cp build/app/outputs/flutter-apk/app-release.apk dist/el_farhan_demo.apk

echo
echo "========================================"
echo "تم بناء التطبيق بنجاح!"
echo "مكان الملف: dist/el_farhan_demo.apk"
echo "========================================"
echo

echo "لتثبيت التطبيق على الجهاز:"
echo "adb install dist/el_farhan_demo.apk"
echo

read -p "اضغط Enter للمتابعة..."
