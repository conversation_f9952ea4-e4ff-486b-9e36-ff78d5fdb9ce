# 🧪 سيناريو الاختبار الشامل - شاشات الوكلاء

## 🎯 **الهدف**
اختبار جميع شاشات الوكلاء للتأكد من صحة الحسابات ووضوح البيانات للمدير

---

## 📱 **خطوات الاختبار الفعلي**

### **المرحلة 1: تسجيل الدخول والوصول للشاشات**

#### **الخطوة 1: تسجيل الدخول**
```
1. افتح التطبيق
2. سجل دخول بـ:
   - اسم المستخدم: admin
   - كلمة المرور: admin123
3. تأكد من ظهور الشاشة الرئيسية
```

#### **الخطوة 2: الوصول لإدارة الوكلاء**
```
1. اضغط على "إدارة الوكلاء" من القائمة الرئيسية
2. تأكد من ظهور 3 تبويبات:
   - نظرة عامة
   - إدارة الحسابات  
   - تسجيل دفعة
```

---

### **المرحلة 2: اختبار تبويبة "نظرة عامة"**

#### **الخطوة 3: مراجعة الإحصائيات**
```
✅ تحقق من وجود:
   - إجمالي الوكلاء: 13 وكيل
   - الوكلاء النشطون: عدد معقول
   - إجمالي المديونية: بالجنيه المصري
   - إجمالي المدفوع: بالجنيه المصري
   - الرصيد الإجمالي: موجب أو سالب

✅ تأكد من:
   - الأرقام منطقية
   - العملة تظهر "ج.م" وليس "EGP"
   - الألوان واضحة
```

#### **الخطوة 4: اختبار الأنشطة الحديثة**
```
✅ تحقق من:
   - ظهور آخر المعاملات
   - التواريخ صحيحة
   - أسماء الوكلاء واضحة
   - المبالغ بالعملة الصحيحة
```

---

### **المرحلة 3: اختبار تبويبة "إدارة الحسابات"**

#### **الخطوة 5: مراجعة قائمة الوكلاء**
```
✅ تأكد من ظهور:
   - 13 حساب وكيل (وليس "لا توجد حسابات")
   - اسم كل وكيل واضح
   - رقم الهاتف (إن وجد)
   - الرصيد الحالي بألوان صحيحة:
     * أحمر = الوكيل مدين للمؤسسة
     * أخضر = المؤسسة مدينة للوكيل
```

#### **الخطوة 6: اختبار تفاصيل وكيل محدد**
```
1. اختر الوكيل "uuu" (لديه 4 فواتير)
2. اضغط على الكارت الخاص به
3. تحقق من التفاصيل:
   ✅ إجمالي الدين: 23,000 ج.م
   ✅ إجمالي المدفوع: 1,000 ج.م  
   ✅ الرصيد الحالي: 500 ج.م (أحمر)
   ✅ عدد المعاملات: 7 معاملات
```

#### **الخطوة 7: اختبار كشف الحساب التفصيلي**
```
1. من تفاصيل الوكيل، اضغط "كشف حساب تفصيلي"
2. تأكد من ظهور 3 تبويبات:
   - نظرة عامة
   - كشف الحساب
   - الإحصائيات
```

---

### **المرحلة 4: اختبار كشف الحساب التفصيلي**

#### **الخطوة 8: تبويبة "نظرة عامة"**
```
✅ تحقق من:
   - معلومات الوكيل (الاسم، الهاتف، المعرف)
   - ملخص الحساب (الديون، المدفوع، الرصيد)
   - الأنشطة الحديثة
   - لا توجد مشاكل في Layout
```

#### **الخطوة 9: تبويبة "كشف الحساب"**
```
✅ تحقق من الجدول:
   - العناوين: التاريخ، النوع، الوصف، مدين، دائن، المرجع
   - البيانات تظهر بوضوح
   - التواريخ بالتنسيق الصحيح
   - المبالغ بـ "ج.م" وليس "EGP"
   - الألوان: أحمر للمدين، أخضر للدائن
   - أنواع المعاملات:
     * تحويل (أزرق)
     * دفعة (أخضر)  
     * ربح بيع (برتقالي)
```

#### **الخطوة 10: تبويبة "الإحصائيات"**
```
✅ تحقق من:
   - الرسوم البيانية تظهر
   - الإحصائيات التفصيلية
   - نسب الأرباح
   - مقارنات الأداء
```

---

### **المرحلة 5: اختبار تصدير PDF**

#### **الخطوة 11: تصدير كشف الحساب**
```
1. من شاشة كشف الحساب التفصيلي
2. اضغط على أيقونة PDF في الأعلى
3. انتظر رسالة "جاري إنشاء ملف PDF..."
4. تأكد من عدم ظهور أخطاء خطوط:
   ❌ لا يجب ظهور: "Unable to find a font to draw"
   ✅ يجب ظهور: "تم تصدير كشف الحساب بنجاح"
```

#### **الخطوة 12: مراجعة محتوى PDF**
```
✅ تحقق من PDF:
   - العنوان: "كشف حساب الوكيل"
   - اسم الوكيل واضح
   - جميع النصوص بالعربية
   - العملة "ج.م" وليس "EGP"
   - الجدول مرتب ومقروء
   - التواريخ صحيحة
   - المبالغ دقيقة
   - الملخص النهائي صحيح
```

---

### **المرحلة 6: اختبار تسجيل دفعة**

#### **الخطوة 13: تبويبة "تسجيل دفعة"**
```
1. ارجع لشاشة إدارة الوكلاء
2. اختر تبويبة "تسجيل دفعة"
3. اختر وكيل من القائمة
4. أدخل مبلغ تجريبي (مثل 500 ج.م)
5. أضف ملاحظة
6. اضغط "تسجيل الدفعة"
```

#### **الخطوة 14: التحقق من تسجيل الدفعة**
```
✅ تأكد من:
   - ظهور رسالة نجاح
   - تحديث رصيد الوكيل
   - ظهور المعاملة في كشف الحساب
   - صحة الحسابات الجديدة
```

---

### **المرحلة 7: اختبار الفلترة والبحث**

#### **الخطوة 15: اختبار البحث**
```
1. في تبويبة "إدارة الحسابات"
2. استخدم شريط البحث
3. ابحث عن "uuu"
4. تأكد من ظهور النتائج الصحيحة
```

#### **الخطوة 16: اختبار الفلترة**
```
1. جرب فلاتر مختلفة:
   - جميع الوكلاء
   - الوكلاء المدينون
   - الوكلاء الدائنون
2. تأكد من صحة النتائج
```

---

## ✅ **معايير النجاح**

### **يعتبر الاختبار ناجحاً إذا:**
- ✅ جميع الشاشات تفتح بدون أخطاء
- ✅ البيانات تظهر بوضوح ودقة
- ✅ الحسابات منطقية وصحيحة
- ✅ PDF يُصدر بالعربية الكاملة
- ✅ لا توجد مشاكل في Layout
- ✅ العملة تظهر "ج.م" في كل مكان
- ✅ الألوان واضحة ومعبرة
- ✅ تسجيل الدفعات يعمل بنجاح

---

## 🚨 **مشاكل محتملة وحلولها**

### **إذا ظهرت مشاكل:**

#### **مشكلة: "لا توجد حسابات وكلاء"**
```
الحل: تأكد من تحميل البيانات من Firebase
```

#### **مشكلة: أخطاء خطوط في PDF**
```
الحل: تأكد من تحميل الخطوط العربية
```

#### **مشكلة: Layout مكسور**
```
الحل: تأكد من إصلاح SizedBox في detailed_agent_statement_screen.dart
```

---

## 📞 **الدعم الفني**

**المطور**: Motasem Salem  
**WhatsApp**: 01062606098

---

## 🎯 **النتيجة المتوقعة**

بعد إجراء هذا الاختبار، يجب أن تكون جميع شاشات الوكلاء تعمل بكفاءة عالية وتوفر للمدير:

- 📊 بيانات مالية دقيقة وواضحة
- 💰 حسابات منطقية ومفهومة  
- 📄 تقارير PDF احترافية بالعربية
- 🎨 واجهة مستخدم سهلة وجميلة
- ⚡ أداء سريع وموثوق

🎉 **التطبيق جاهز للاستخدام الفعلي!**
