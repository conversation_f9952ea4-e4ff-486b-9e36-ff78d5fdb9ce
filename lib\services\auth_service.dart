import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firebase_service.dart';
import 'local_database_service.dart';
import '../models/user_model.dart';
import '../core/constants/app_constants.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();

  final FirebaseService _firebaseService = FirebaseService.instance;
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  
  UserModel? _currentUser;
  
  // Get current user
  UserModel? get currentUser => _currentUser;
  
  // Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  // Initialize auth service
  Future<void> initialize() async {
    try {
      // Try to restore session from local storage first
      await _restoreUserSession();

      // Check if user is already signed in with Firebase
      final firebaseUser = _firebaseService.currentUser;
      if (firebaseUser != null && _currentUser == null) {
        await _loadCurrentUser(firebaseUser.uid);
      }

      // Listen to auth state changes
      _firebaseService.auth.authStateChanges().listen(_onAuthStateChanged);

      if (kDebugMode) {
        print('Auth service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing auth service: $e');
      }
    }
  }

  // Handle auth state changes
  void _onAuthStateChanged(User? firebaseUser) async {
    if (firebaseUser != null) {
      await _loadCurrentUser(firebaseUser.uid);
    } else {
      _currentUser = null;
      await _clearLocalUserData();
    }
  }

  // Load current user data
  Future<void> _loadCurrentUser(String userId) async {
    try {
      final firebaseUser = _firebaseService.currentUser;
      if (firebaseUser == null) return;

      // Try to load from local database first by email
      final localUsers = await _localDb.query(
        'users',
        where: 'email = ?',
        whereArgs: [firebaseUser.email],
      );

      if (localUsers.isNotEmpty) {
        _currentUser = UserModel.fromMap(localUsers.first);
      } else {
        // Try to load from Firebase
        try {
          final querySnapshot = await _firebaseService.firestore
              .collection(AppConstants.usersCollection)
              .where('email', isEqualTo: firebaseUser.email)
              .limit(1)
              .get();

          if (querySnapshot.docs.isNotEmpty) {
            _currentUser = UserModel.fromFirestore(querySnapshot.docs.first);
            // Save to local database
            await _localDb.insert('users', _currentUser!.toMap());
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error loading from Firebase: $e');
          }
        }
      }

      if (_currentUser != null) {
        await _saveUserSession();
        if (kDebugMode) {
          print('Current user loaded: ${_currentUser!.fullName}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading current user: $e');
      }
    }
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _firebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadCurrentUser(credential.user!.uid);
        return _currentUser;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('Firebase Auth Error: ${e.code} - ${e.message}');
      }
      throw _getAuthErrorMessage(e.code);
    } catch (e) {
      if (kDebugMode) {
        print('Sign in error: $e');
      }
      throw 'حدث خطأ أثناء تسجيل الدخول';
    }
  }

  // Sign in with username and password (Local + Firebase hybrid)
  Future<UserModel?> signInWithUsername(String username, String password) async {
    try {
      // First, try to find user in local database
      final localUsers = await _localDb.query(
        'users',
        where: 'username = ?',
        whereArgs: [username],
      );

      if (localUsers.isNotEmpty) {
        final user = UserModel.fromMap(localUsers.first);

        // Check if this is the super admin with default credentials
        if (user.role == AppConstants.superAdminRole &&
            (username == 'admin' || username == 'ahmed') && password == 'admin123') {
          _currentUser = user;
          await _saveUserSession();

          if (kDebugMode) {
            print('Super admin signed in locally: ${user.fullName}');
          }

          return user;
        }

        // For agents and other users, check password in additionalData
        if (user.additionalData != null && user.additionalData!.containsKey('password')) {
          final storedPassword = user.additionalData!['password'];
          if (storedPassword == password) {
            _currentUser = user;
            await _saveUserSession();

            if (kDebugMode) {
              print('User signed in locally: ${user.fullName} (${user.role})');
            }

            return user;
          } else {
            throw 'كلمة المرور غير صحيحة';
          }
        }

        // If no password stored locally, try Firebase authentication
        if (user.email.isNotEmpty) {
          try {
            return await signInWithEmailAndPassword(user.email, password);
          } catch (e) {
            if (kDebugMode) {
              print('Firebase auth failed for local user: $e');
            }
            throw 'كلمة المرور غير صحيحة';
          }
        }
      }



      // If not found locally, try Firebase as fallback
      try {
        final querySnapshot = await _firebaseService.firestore
            .collection(AppConstants.usersCollection)
            .where('username', isEqualTo: username)
            .limit(1)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          final email = querySnapshot.docs.first.data()['email'];
          return await signInWithEmailAndPassword(email, password);
        } else {
          throw 'اسم المستخدم غير موجود';
        }
      } catch (e) {
        if (kDebugMode) {
          print('Firebase query error: $e');
        }
        throw 'اسم المستخدم غير موجود';
      }
    } catch (e) {
      if (kDebugMode) {
        print('Username sign in error: $e');
      }
      rethrow;
    }
  }

  // Create new user (only for super admin)
  Future<UserModel?> createUser({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required String phone,
    required String role,
    String? warehouseId,
  }) async {
    try {
      // Check if current user is super admin
      if (_currentUser?.role != AppConstants.superAdminRole) {
        throw 'ليس لديك صلاحية لإنشاء مستخدمين جدد';
      }



      // Create Firebase user
      final credential = await _firebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        final now = DateTime.now();
        final newUser = UserModel(
          id: credential.user!.uid,
          username: username,
          email: email,
          fullName: fullName,
          phone: phone,
          role: role,
          warehouseId: warehouseId,
          isActive: true,
          createdAt: now,
          updatedAt: now,
        );

        // Save to Firebase
        await _firebaseService.firestore
            .collection(AppConstants.usersCollection)
            .doc(newUser.id)
            .set(newUser.toFirestore());

        // Save to local database
        await _localDb.insert('users', newUser.toMap());

        if (kDebugMode) {
          print('New user created: ${newUser.fullName}');
        }

        return newUser;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('Create user error: ${e.code} - ${e.message}');
      }
      throw _getAuthErrorMessage(e.code);
    } catch (e) {
      if (kDebugMode) {
        print('Create user error: $e');
      }
      rethrow;
    }
  }

  // Update user profile
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      // Update in Firebase
      await _firebaseService.firestore
          .collection(AppConstants.usersCollection)
          .doc(updatedUser.id)
          .update(updatedUser.toFirestore());

      // Update in local database
      await _localDb.update(
        'users',
        updatedUser.toMap(),
        'id = ?',
        [updatedUser.id],
      );

      // Update current user if it's the same user
      if (_currentUser?.id == updatedUser.id) {
        _currentUser = updatedUser;
        await _saveUserSession();
      }

      if (kDebugMode) {
        print('User profile updated: ${updatedUser.fullName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user profile: $e');
      }
      rethrow;
    }
  }

  // Change password
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      final user = _firebaseService.currentUser;
      if (user == null) throw 'المستخدم غير مسجل الدخول';

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      if (kDebugMode) {
        print('Password changed successfully');
      }
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('Change password error: ${e.code} - ${e.message}');
      }
      throw _getAuthErrorMessage(e.code);
    } catch (e) {
      if (kDebugMode) {
        print('Change password error: $e');
      }
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // Sign out from Firebase
      await _firebaseService.signOut();

      // Clear current user
      _currentUser = null;

      // Clear all local session data
      await _clearLocalUserData();

      if (kDebugMode) {
        print('✅ User signed out successfully - all session data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Sign out error: $e');
      }
      rethrow;
    }
  }

  // Save user session to local storage
  Future<void> _saveUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUser != null) {
        await prefs.setString('current_user_id', _currentUser!.id);
        await prefs.setString('current_user_role', _currentUser!.role);
        await prefs.setString('current_user_data', json.encode(_currentUser!.toMap()));
        await prefs.setBool('is_logged_in', true);
        await prefs.setString('user_email', _currentUser!.email);
        await prefs.setString('login_timestamp', DateTime.now().toIso8601String());

        // Set session to never expire (until manual logout)
        await prefs.setBool('remember_login', true);
        await prefs.setString('session_token', 'persistent_${_currentUser!.id}_${DateTime.now().millisecondsSinceEpoch}');

        if (kDebugMode) {
          print('✅ User session saved successfully for: ${_currentUser!.fullName}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving user session: $e');
      }
    }
  }

  // Restore user session from local storage
  Future<void> _restoreUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool('is_logged_in') ?? false;
      final rememberLogin = prefs.getBool('remember_login') ?? false;
      final userDataString = prefs.getString('current_user_data');
      final sessionToken = prefs.getString('session_token');

      if (isLoggedIn && rememberLogin && userDataString != null && sessionToken != null) {
        final userData = json.decode(userDataString);
        _currentUser = UserModel.fromMap(userData);

        if (kDebugMode) {
          print('✅ User session restored: ${_currentUser!.fullName}');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No valid session found to restore');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring user session: $e');
      }
      // Clear corrupted session data
      await _clearLocalUserData();
    }
  }

  // Clear local user data
  Future<void> _clearLocalUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');
      await prefs.remove('current_user_role');
      await prefs.remove('current_user_data');
      await prefs.remove('is_logged_in');
      await prefs.remove('user_email');
      await prefs.remove('login_timestamp');
      await prefs.remove('remember_login');
      await prefs.remove('session_token');

      if (kDebugMode) {
        print('✅ All local user data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing local user data: $e');
      }
    }
  }

  // Get user-friendly error messages
  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'المستخدم غير موجود';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا المستخدم';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
      case 'network-request-failed':
        return 'خطأ في الاتصال بالإنترنت';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }

  // Check user permissions
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;

    switch (permission) {
      case 'manage_users':
        return _currentUser!.isSuperAdmin;
      case 'manage_inventory':
        return _currentUser!.isSuperAdmin || _currentUser!.isAdmin;
      case 'create_invoices':
        return _currentUser!.canCreateInvoices;
      case 'view_reports':
        return _currentUser!.canViewReports;
      case 'manage_documents':
        return _currentUser!.canManageDocuments;
      default:
        return false;
    }
  }


}
