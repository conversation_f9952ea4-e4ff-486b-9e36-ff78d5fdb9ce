# نظام إدارة المستخدمين - تطبيق آل فرحان

## 🔐 **آلية إنشاء المستخدم الأساسي**

### ✅ **الشروط لإنشاء المستخدم الأساسي:**

#### 1. **فحص قاعدة البيانات المحلية:**
```
- يتم فحص جدول 'users' في SQLite
- إذا وُجد أي مستخدم، لا يتم إنشاء المستخدم الأساسي
```

#### 2. **فحص Firebase (إذا متاح):**
```
- يتم فحص مجموعة 'users' في Firestore
- إذا وُجد أي مستخدم، لا يتم إنشاء المستخدم الأساسي
- إذا فشل الاتصال بـ Firebase، يتم تجاهل الخطأ والمتابعة
```

#### 3. **إنشاء المستخدم فقط إذا:**
```
✅ لا يوجد أي مستخدمين في قاعدة البيانات المحلية
✅ لا يوجد أي مستخدمين في Firebase (أو فشل الاتصال)
✅ هذا هو التشغيل الأول للتطبيق
```

---

## 👤 **بيانات المستخدم الأساسي:**

```
ID: admin_001
اسم المستخدم: ahmed
البريد الإلكتروني: <EMAIL>
الاسم الكامل: أحمد محمد - المدير الأعلى
الهاتف: 01234567890
الدور: super_admin
كلمة المرور: admin123 (محفوظة في جدول منفصل)
```

---

## 🔄 **دورة حياة المستخدم الأساسي:**

### **المرحلة 1: التشغيل الأول**
```
1. فحص قواعد البيانات
2. عدم وجود مستخدمين
3. إنشاء المستخدم الأساسي
4. إنشاء جدول كلمات المرور التجريبية
5. حفظ كلمة مرور المستخدم الأساسي
```

### **المرحلة 2: الاستخدام العادي**
```
1. تسجيل الدخول بالمستخدم الأساسي
2. إضافة مستخدمين حقيقيين
3. إعداد Firebase والمزامنة
```

### **المرحلة 3: التنظيف التلقائي**
```
1. عند إضافة أول مستخدم حقيقي
2. حذف المستخدم التجريبي تلقائياً
3. حذف كلمة المرور التجريبية
4. الانتقال للعمل مع المستخدمين الحقيقيين فقط
```

---

## 🛡️ **الأمان والحماية:**

### **حماية من التكرار:**
```
✅ فحص مزدوج (محلي + Firebase)
✅ لا يتم إنشاء المستخدم إذا وُجد أي مستخدمين
✅ رسائل واضحة في Console للمطورين
```

### **حماية في الإنتاج:**
```
✅ المستخدم التجريبي يُحذف تلقائياً
✅ كلمات المرور التجريبية تُحذف تلقائياً
✅ لا تأثير على المستخدمين الحقيقيين
```

### **حماية من الأخطاء:**
```
✅ معالجة أخطاء Firebase
✅ العمل في وضع offline
✅ عدم توقف التطبيق عند فشل الفحص
```

---

## 📱 **سيناريوهات الاستخدام:**

### **السيناريو 1: تطبيق جديد (أول مرة)**
```
1. المطور يشغل التطبيق
2. لا يوجد مستخدمين
3. يتم إنشاء المستخدم الأساسي
4. يسجل دخول بـ ahmed/admin123
5. يضيف مستخدمين حقيقيين
6. المستخدم التجريبي يُحذف تلقائياً
```

### **السيناريو 2: تطبيق موجود (به مستخدمين)**
```
1. المطور يشغل التطبيق
2. يوجد مستخدمين في قاعدة البيانات
3. لا يتم إنشاء المستخدم الأساسي
4. يعمل التطبيق بالمستخدمين الموجودين
```

### **السيناريو 3: توزيع للوكلاء**
```
1. الوكيل يحصل على التطبيق
2. قاعدة البيانات فارغة
3. يتم إنشاء المستخدم الأساسي
4. الوكيل يسجل دخول بـ ahmed/admin123
5. يضيف مستخدميه الخاصين
6. المستخدم التجريبي يُحذف تلقائياً
```

### **السيناريو 4: استعادة من Firebase**
```
1. تطبيق جديد لكن Firebase به بيانات
2. فحص Firebase يجد مستخدمين
3. لا يتم إنشاء المستخدم الأساسي
4. يتم تحميل المستخدمين من Firebase
```

---

## 🔧 **للمطورين:**

### **رسائل Console المهمة:**
```
✅ "Users already exist in local database. Skipping admin user creation."
✅ "Users already exist in Firebase. Skipping admin user creation."
✅ "Firebase check failed (offline mode): [error]"
✅ "=== DEFAULT ADMIN USER CREATED ==="
✅ "Demo admin user removed - real users are now being created"
```

### **فحص حالة النظام:**
```sql
-- فحص المستخدمين المحليين
SELECT * FROM users;

-- فحص كلمات المرور التجريبية
SELECT * FROM demo_passwords;
```

### **إعادة تعيين للتطوير:**
```sql
-- حذف جميع المستخدمين (للاختبار فقط)
DELETE FROM users;
DELETE FROM demo_passwords;
```

---

## ⚠️ **تحذيرات مهمة:**

### **للمطورين:**
```
⚠️ لا تحذف المستخدم الأساسي يدوياً إلا للاختبار
⚠️ تأكد من إعداد Firebase قبل الإنتاج
⚠️ اختبر السيناريوهات المختلفة قبل التوزيع
```

### **للمستخدمين النهائيين:**
```
✅ النظام آمن ولا يحتاج تدخل
✅ المستخدم التجريبي يُحذف تلقائياً
✅ لا تأثير على البيانات الحقيقية
```

---

## 🎯 **النتيجة:**

**نظام ذكي وآمن لإدارة المستخدمين يضمن:**
- ✅ عدم تكرار المستخدم الأساسي
- ✅ العمل في جميع السيناريوهات
- ✅ الأمان في الإنتاج
- ✅ سهولة التطوير والاختبار

**التطبيق جاهز للتوزيع بأمان! 🚀**
