import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../services/data_service.dart';
import '../../services/pdf_service.dart';
import '../../models/item_model.dart';

class DetailedSalesReport extends StatefulWidget {
  const DetailedSalesReport({super.key});

  @override
  State<DetailedSalesReport> createState() => _DetailedSalesReportState();
}

class _DetailedSalesReportState extends State<DetailedSalesReport> {
  final DataService _dataService = DataService.instance;
  final PdfService _pdfService = PdfService.instance;
  List<ItemModel> _soldItems = [];
  bool _isLoading = false;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    // Set default date range to current month
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, 1);
    _endDate = now;
    _loadSoldItems();
  }

  Future<void> _loadSoldItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final allItems = await _dataService.getItems();
      final soldItems = allItems.where((item) => 
        item.status == 'sold' &&
        (_startDate == null || item.updatedAt.isAfter(_startDate!)) &&
        (_endDate == null || item.updatedAt.isBefore(_endDate!.add(const Duration(days: 1))))
      ).toList();
      
      // Sort by date (newest first)
      soldItems.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      
      setState(() {
        _soldItems = soldItems;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalSales = _soldItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);
    final totalProfit = _soldItems.fold<double>(0, (sum, item) => sum + (item.suggestedSellingPrice - item.purchasePrice));

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير المبيعات المفصل'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _generatePDF,
            tooltip: 'تصدير PDF',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSoldItems,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // Date filters
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 8),
                          Text(_startDate != null 
                            ? 'من: ${_startDate!.toString().split(' ')[0]}'
                            : 'تاريخ البداية'),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 8),
                          Text(_endDate != null 
                            ? 'إلى: ${_endDate!.toString().split(' ')[0]}'
                            : 'تاريخ النهاية'),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Summary
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryCard('عدد المبيعات', _soldItems.length.toString(), Colors.blue),
                _buildSummaryCard('إجمالي المبيعات', '${totalSales.toStringAsFixed(0)} ج.م', Colors.green),
                _buildSummaryCard('إجمالي الربح', '${totalProfit.toStringAsFixed(0)} ج.م', Colors.orange),
              ],
            ),
          ),
          
          const Divider(),
          
          // Sales table
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _soldItems.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد مبيعات في الفترة المحددة',
                          style: TextStyle(fontSize: 18),
                        ),
                      )
                    : SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: DataTable(
                          columns: const [
                            DataColumn(label: Text('التاريخ')),
                            DataColumn(label: Text('النوع')),
                            DataColumn(label: Text('الموديل')),
                            DataColumn(label: Text('اللون')),
                            DataColumn(label: Text('الماركة')),
                            DataColumn(label: Text('سعر الشراء')),
                            DataColumn(label: Text('سعر البيع')),
                            DataColumn(label: Text('الربح')),
                          ],
                          rows: _soldItems.map((item) {
                            final profit = item.suggestedSellingPrice - item.purchasePrice;
                            return DataRow(
                              cells: [
                                DataCell(Text(item.updatedAt.toString().split(' ')[0])),
                                DataCell(Text(item.type)),
                                DataCell(Text(item.model)),
                                DataCell(Text(item.color)),
                                DataCell(Text(item.brand)),
                                DataCell(Text('${item.purchasePrice.toStringAsFixed(0)} ج.م')),
                                DataCell(Text('${item.suggestedSellingPrice.toStringAsFixed(0)} ج.م')),
                                DataCell(Text(
                                  '${profit.toStringAsFixed(0)} ج.م',
                                  style: TextStyle(
                                    color: profit >= 0 ? Colors.green : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        child: Column(
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? (_startDate ?? DateTime.now()) : (_endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
      _loadSoldItems();
    }
  }

  Future<void> _generatePDF() async {
    try {
      final totalSales = _soldItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);
      final totalProfit = _soldItems.fold<double>(0, (sum, item) => sum + (item.suggestedSellingPrice - item.purchasePrice));

      final salesData = _soldItems.map((item) => {
        'updatedAt': item.updatedAt,
        'type': item.type,
        'model': item.model,
        'color': item.color,
        'brand': item.brand,
        'purchasePrice': item.purchasePrice,
        'suggestedSellingPrice': item.suggestedSellingPrice,
      }).toList();

      final summary = {
        'salesCount': _soldItems.length,
        'totalSales': totalSales,
        'totalProfit': totalProfit,
      };

      final pdfData = await _pdfService.generateSalesReportPdf(
        sales: salesData,
        title: 'تقرير المبيعات المفصل',
        startDate: _startDate,
        endDate: _endDate,
        summary: summary,
      );

      await _pdfService.printPdf(pdfData, 'تقرير المبيعات المفصل');
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تصدير PDF: $e', isError: true);
      }
    }
  }
}
