import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

class PermissionService {
  static PermissionService? _instance;
  static PermissionService get instance => _instance ??= PermissionService._();
  
  PermissionService._();

  // Check if running on Android 13+ (API level 33+)
  bool get isAndroid13Plus {
    if (!Platform.isAndroid) return false;
    // This is a simplified check - in production you might want to use device_info_plus
    return true; // Assume modern Android for now
  }

  // Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      
      if (kDebugMode) {
        print('Camera permission status: $status');
      }
      
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting camera permission: $e');
      }
      return false;
    }
  }

  // Request storage permission
  Future<bool> requestStoragePermission() async {
    try {
      final status = await Permission.storage.request();
      
      if (kDebugMode) {
        print('Storage permission status: $status');
      }
      
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting storage permission: $e');
      }
      return false;
    }
  }

  // Request photos permission (for newer Android versions)
  Future<bool> requestPhotosPermission() async {
    try {
      final status = await Permission.photos.request();
      
      if (kDebugMode) {
        print('Photos permission status: $status');
      }
      
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting photos permission: $e');
      }
      return false;
    }
  }

  // Request all necessary permissions for camera and image handling
  Future<bool> requestCameraAndStoragePermissions() async {
    try {
      // For Android 13+, we only need camera permission for taking photos
      // Storage permission is not needed for app-specific directories

      final cameraStatus = await Permission.camera.request();
      final cameraGranted = cameraStatus == PermissionStatus.granted;

      if (kDebugMode) {
        print('Camera permission: $cameraGranted');
        print('Android 13+ - Storage permission not required for app directories');
      }

      return cameraGranted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting permissions: $e');
      }
      return false;
    }
  }

  // Check if camera permission is granted
  Future<bool> isCameraPermissionGranted() async {
    try {
      final status = await Permission.camera.status;
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking camera permission: $e');
      }
      return false;
    }
  }

  // Check if storage permission is granted (Android 13+ doesn't need this for app directories)
  Future<bool> isStoragePermissionGranted() async {
    try {
      // For Android 13+, storage permission is not needed for app-specific directories
      // Always return true as we save images in app directories
      if (kDebugMode) {
        print('Storage permission check - Android 13+ uses app directories (always granted)');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking storage permission: $e');
      }
      return true; // Default to true for app directories
    }
  }

  // Open app settings if permission is permanently denied
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      if (kDebugMode) {
        print('Error opening app settings: $e');
      }
    }
  }

  // Get permission status message
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'تم منح الصلاحية';
      case PermissionStatus.denied:
        return 'تم رفض الصلاحية';
      case PermissionStatus.restricted:
        return 'الصلاحية مقيدة';
      case PermissionStatus.limited:
        return 'الصلاحية محدودة';
      case PermissionStatus.permanentlyDenied:
        return 'تم رفض الصلاحية نهائياً';
      case PermissionStatus.provisional:
        return 'الصلاحية مؤقتة';
    }
  }

  // Request notification permission
  Future<bool> requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      
      if (kDebugMode) {
        print('Notification permission status: $status');
      }
      
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting notification permission: $e');
      }
      return false;
    }
  }

  // Request location permission (if needed for future features)
  Future<bool> requestLocationPermission() async {
    try {
      final status = await Permission.location.request();
      
      if (kDebugMode) {
        print('Location permission status: $status');
      }
      
      return status == PermissionStatus.granted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting location permission: $e');
      }
      return false;
    }
  }

  // Check all required permissions for the app
  Future<Map<String, bool>> checkAllPermissions() async {
    try {
      final results = <String, bool>{};
      
      results['camera'] = await isCameraPermissionGranted();
      results['storage'] = await isStoragePermissionGranted();
      results['notification'] = await Permission.notification.status == PermissionStatus.granted;
      
      return results;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking all permissions: $e');
      }
      return {};
    }
  }

  // Request all app permissions at once
  Future<bool> requestAllAppPermissions() async {
    try {
      final cameraAndStorage = await requestCameraAndStoragePermissions();
      final notification = await requestNotificationPermission();
      
      if (kDebugMode) {
        print('All permissions requested - Camera/Storage: $cameraAndStorage, Notification: $notification');
      }
      
      return cameraAndStorage; // Notification is optional
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting all app permissions: $e');
      }
      return false;
    }
  }
}
